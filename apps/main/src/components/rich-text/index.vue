<template>
    <div class="rich-html-content" v-html="html" />
</template>

<script setup lang="ts">
defineProps({
    html: {
        type: String,
        default: '',
    },
});
</script>

<style lang="less">
@import '@/styles/ui-reset/rich-text.less';
</style>

<style lang="less" scoped>
.rich-html-content {
    :deep(.lext-theme-ul) {
        .lext-theme-listItem {
            margin-left: 0;
            // display: flex;
            flex-wrap: wrap;
            margin-bottom: 8px;
            color: #4d4d4d;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            list-style-type: none;

            &::marker {
                display: none;
            }

            &::before {
                display: inline-block;
                content: '';
                background: url(https://img.bosszhipin.com/static/file/2024/30n1m8vzed1722586469603.png.webp) no-repeat;
                background-size: auto 100%;
                margin-top: 3px;
                margin-right: 8px;
                width: 16px;
                min-width: 16px;
                height: 16px;
                min-height: 16px;
            }
        }
    }
}
</style>
