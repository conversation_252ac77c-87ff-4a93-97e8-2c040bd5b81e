<template>
    <div class="man-machine-core" :class="{ 'man-machine-core--inline': inline }">
        <slot :onClick="onCountdownBtnClick" :loading="isVerifyLoading">
            <span class="man-machine-core__btn" :class="{ counting: remaining > 0 }" @click="onCountdownBtnClick">
                {{ !remaining ? btnText : counterText }}
            </span>
        </slot>
    </div>
</template>

<script lang="ts" setup>
import { throttle } from '@boss/utils';
import { useCountdown } from '@crm/vueuse-pro';

import type { IVerifyParams, IVerifyFailParams, IVerifyFailError, ICountdownBtnProps, IVerifyProps } from './types';

import { useVerifyCode } from './hooks/use-verify-code';

const props = withDefaults(defineProps<ICountdownBtnProps & IVerifyProps>(), {
    inline: true,
    initialCountDown: 60,
    btnText: '发送验证码',
    // 发送成功后是否展示倒计时
    showCounter: true,
    // 发送成功后是否对重复点击进行提示
    showRepeat: false,
    beforeSend: () => Promise.resolve(true),
});

const emit = defineEmits<{
    (_e: 'click'): void;
    (_e: 'success', _verifyParams: IVerifyParams): void;
    (_e: 'fail', _failParams: IVerifyFailParams): void;
    (_e: 'close'): void;
}>();

/** data */
const { verifyCodeInit, isVerifyLoading } = useVerifyCode();
const { remaining, start } = useCountdown(0);

const counterText = computed(() => {
    if (props.showCounter) {
        return `${remaining.value}s后重新发送`;
    }
    return props.btnText;
});

/** methods */
const throttleInitVerifyCode = throttle(initVerifyCode, 2000, {
    leading: true, // 首次点击立即执行
    trailing: false, // 不需要在结束时额外执行一次
});

function initVerifyCode() {
    verifyCodeInit({
        onSuccess: async (data) => {
            if (props.ajaxFn) {
                // 验证成功后会返回数据，进行接口请求（比如发送短信…）
                const { code, message } = await props.ajaxFn(
                    {
                        ...props.extraReqParams,
                    },
                    { headers: data.headers },
                );
                if (code === 0) {
                    // 执行倒计时
                    emit('success', data.headers);
                    if (props.successToast && typeof props.successToast === 'string') {
                        Toast.success(props.successToast);
                    }
                    startCountdown();
                } else {
                    // 接口报错了
                    emit('fail', { code, message });
                }
            } else {
                emit('success', data.headers);
                if (props.successToast && typeof props.successToast === 'string') {
                    Toast.success(props.successToast);
                }
            }
        },
        onFail: (error) => {
            const messageInstance = error as IVerifyFailError;
            // 验证sdk报错
            Toast.danger(messageInstance.message);
            emit('fail', { code: messageInstance.code, message: messageInstance.message });
        },
        onClose: () => {
            emit('close');
        },
    });
}

async function init() {
    const beforeSend = await props.beforeSend();

    if (beforeSend) {
        if (!props.manualInit) {
            throttleInitVerifyCode();
        }
    }
}

async function manualTriggerInit() {
    await checkRemaining();
    throttleInitVerifyCode();
}

function checkRemaining() {
    return new Promise((resolve) => {
        if (remaining.value > 0) {
            if (props.showRepeat) {
                const msg = typeof props.showRepeat === 'string' ? props.showRepeat : `操作过于频繁，请${remaining.value}s后重试`;
                Toast.warning(msg);
            }
        } else {
            resolve(true);
        }
    });
}

async function onCountdownBtnClick() {
    await checkRemaining();
    init();
    emit('click');
}

// 开始倒计时
function startCountdown() {
    start(props.initialCountDown);
}

defineOptions({ name: 'ManMachineCore' });

defineExpose({ trigger: manualTriggerInit });
</script>

<style lang="less">
@import './styles/common.less';

.man-machine-core {
    position: relative;
}

.man-machine-core--inline {
    display: inline-flex;
}

.man-machine-core__btn {
    display: inline-block;
    padding: 0 8px;
    border-radius: 12px;
    line-height: 24px;
    color: var(--man-machine-primary-color);
    cursor: pointer;
    &:hover {
        background-color: var(--man-machine-hover-bg-color);
    }
    &:active {
        color: var(--man-machine-secondary-color);
        background-color: var(--man-machine-active-bg-color);
    }
    &.counting {
        color: var(--man-machine-disabled-color);
        cursor: initial;
        &:hover {
            background-color: transparent;
            color: var(--man-machine-disabled-color);
        }
    }
}
</style>
