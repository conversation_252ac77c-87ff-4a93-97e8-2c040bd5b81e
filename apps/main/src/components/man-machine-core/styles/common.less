@import url(./variable.less);

.verify-sdk-popup-dialog {
    .verify-popup-overlay {
        z-index: 9998 !important;
    }
    .verify-popup-box {
        top: 50% !important;
        z-index: 9999 !important;
    }

    .send-photo-btn {
        right: 108px !important;
        color: var(--man-machine-primary-color) !important;
        &:hover {
            color: var(--man-machine-hover-color) !important;
        }
    }
}

.picture_box {
    position: relative;
    width: 100%;
    padding-right: 100px;
    .input_code {
        width: 100% !important;
        height: 40px !important;
        padding: 9px 38px 9px 12px !important;
        font-size: 14px;
        border: 1px solid #e3e7ed;
        border-radius: 8px;
        outline: none;
        top: 0 !important;
        &:focus {
            box-shadow: 1px 2px 3px var(--man-machine-shadow-color);
            border-color: var(--man-machine-primary-color);
        }
    }
    .code_img {
        position: absolute;
        top: 0px;
        right: 0px;
        height: 40px !important;
        width: 96px;
        border-radius: 8px;
        outline: none;
        margin-left: 0 !important;
    }
}
