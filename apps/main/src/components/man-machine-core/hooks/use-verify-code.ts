import type { IResponseData, VerifyCodeInitOptions } from '../types';

import { loadSDK, isSDKLoaded } from '../utils/load-machine-sdk';

export function useVerifyCode() {
    const verifyInstance = ref();

    // 网易云盾的极速验证方式比较缓慢，新增 loading 提升交互体验
    const isVerifyLoading = ref(false);

    function updateLoading(value: boolean) {
        isVerifyLoading.value = value;
    }

    async function verifyCodeInit({ onSuccess, onFail, onClose }: VerifyCodeInitOptions) {
        updateLoading(true);

        // 如果SDK未加载，先加载SDK
        if (!isSDKLoaded.value) {
            await loadSDK();
        }

        verifyInstance.value = new window.VerifyCodeSDK({
            mode: 'popup',

            success(data: IResponseData) {
                onSuccess(data);
                updateLoading(false);
            },
            fail(error: unknown) {
                onFail(error);
                updateLoading(false);
            },
            onClose() {
                onClose?.();
                updateLoading(false);
            },
        });
    }

    return { verifyCodeInit, isVerifyLoading };
}
