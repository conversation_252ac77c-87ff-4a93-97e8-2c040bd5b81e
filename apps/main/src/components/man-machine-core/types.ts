/**
 * 验证码类型
 * 1-极验,2-阿里云,3-图片验证码,4-网易易盾
 */
type VerifyCodeType = 1 | 2 | 3 | 4;

interface IBaseData {
    type: VerifyCodeType;
    headers?: any;
}

// type = 1 的响应类型
export interface IType1Response extends IBaseData {
    type: 1;
    challenge: string;
    validate: string;
    seccode: string;
}

// type = 2 的响应类型
export interface IType2Response extends IBaseData {
    type: 2;
    csig: string;
    csessionId: string;
    ctoken: string;
}

// type = 3 的响应类型
export interface IType3Response extends IBaseData {
    type: 3;
    captcha: string;
    randKey: string;
}

// type = 4 的响应类型
export interface IType4Response extends IBaseData {
    type: 4;
    validate: string;
}

export interface IType2Params extends IBaseData {
    sType: 2;
    sig: string;
    sessionId: string;
    sToken: string;
}

type RenameTypeToSType<T extends { type: VerifyCodeType }> = Omit<T, 'type'> & { sType: T['type'] };

export type IResponseData = IType1Response | IType2Response | IType3Response | IType4Response;
export type IVerifyParams = RenameTypeToSType<IType1Response> | RenameTypeToSType<IType2Params> | RenameTypeToSType<IType3Response> | RenameTypeToSType<IType4Response>;

// 验证参数
export interface IVerifyProps {
    // 发送短信接口
    ajaxFn?: (_params?: any, _options?: any) => Promise<any>;
    // 发送接口时需要的额外参数
    extraReqParams?: Record<string, any>;
    inline?: boolean;
    successToast?: string | boolean;
    beforeSend?: () => Promise<boolean> | boolean;
    manualInit?: boolean;
}

// 倒计时参数
export interface ICountdownBtnProps {
    initialCountDown?: number;
    btnText?: string;
    showCounter?: boolean;
    showRepeat?: string | boolean;
}

export interface IVerifyFailParams {
    code?: number;
    message?: string;
}

// 验证码初始化参数类型
export interface VerifyCodeInitOptions {
    dom?: HTMLElement;
    type?: number;
    onSuccess: (_data: IResponseData) => void;
    onFail: (_error: unknown) => void;
    onClose?: () => void;
}

export interface IVerifyFailError {
    code: number;
    message: string;
}

// 验证码SDK构造函数参数类型
interface VerifyCodeSDKOptions {
    dom?: HTMLElement;
    type?: number;
    mode?: 'popup' | 'float';
    success: (_data: IResponseData) => void;
    fail: (_error: unknown) => void;
    onClose: () => void;
}

// 更新全局声明
declare global {
    interface Window {
        VerifyCodeSDK: {
            new (_options: VerifyCodeSDKOptions): any;
        };
    }
}
