// 加载script数据
export function loadScripts(urls: string[]): Promise<void[]> {
    const promises = urls.map((url) => {
        return new Promise<void>((resolve, reject) => {
            // 资源未加载
            if (!document.querySelector(`script[src="${url}"]`)) {
                const script = document.createElement('script');
                script.type = 'text/javascript';
                script.src = url;

                script.onload = () => resolve();
                script.onerror = () => reject(new Error(`Failed to load script: ${url}`));

                document.head.appendChild(script);
            } else {
                // 资源已存在，直接resolve
                resolve();
            }
        });
    });

    return Promise.all(promises);
}
