import { loadScripts } from './load-script';

// const SDK_URL = `https://static.zhipin.com/assets/zhipin/geek/verify-sdk/verify-sdk-v4.js?t=${Date.now()}`;
const SDK_URL = `https://static.zhipin.com/assets/zhipin/geek/verify-sdk/verify-sdk-v4.js?t=${Date.now()}`;
const isSDKLoaded = ref(false);
let loadPromise: Promise<void> | null = null;

// SDK 加载函数
function loadSDK() {
    if (!loadPromise) {
        loadPromise = loadScripts([SDK_URL]).then(() => {
            isSDKLoaded.value = true;
        });
    }
    return loadPromise;
}

export { loadSDK, isSDKLoaded };
