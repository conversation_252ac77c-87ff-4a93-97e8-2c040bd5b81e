<template>
    <b-button class="debug-panel__trigger" @click.stop="visible = true">调试面板</b-button>

    <b-drawer v-model="visible" title="开发调试面板" :width="600" unmountOnClose>
        <div class="debug-panel-drawer">
            <div class="exam-list">
                <div class="exam-list__header">
                    <h3>考试场次列表</h3>
                    <div class="debug-info__actions">
                        <b-button status="warning" size="small" @click="clearLocalStorage">清除本地缓存</b-button>
                        <b-button type="primary" size="small" :loading="loading" @click="fetchExamList">刷新列表</b-button>
                    </div>
                </div>
                <div v-if="loading" class="exam-list__loading">加载中...</div>
                <div v-else-if="!seqId" class="exam-list__empty">未找到场次ID，请确认已登录sssssss</div>
                <div v-else-if="examList.length === 0" class="exam-list__empty">暂无场次数据</div>
                <div v-else class="exam-list__list">
                    <div v-for="exam in examList" :key="exam.encryptExamId" class="exam-list__item">
                        <div class="exam-list__item-info">
                            <div class="exam-list__item-name">{{ exam.examName }}</div>
                            <div class="exam-list__item-status">
                                <span>状态: {{ getAnswerStatusText(exam.answerStatus) }}</span>
                                <span class="exam-list__item-divider">|</span>
                                <span>类型: {{ getExamSourceText(exam.examSource) }}</span>
                            </div>
                            <div class="exam-list__item-time">
                                <span>开始: {{ exam.startTime }}</span>
                                <span class="exam-list__item-divider">|</span>
                                <span>结束: {{ exam.endTime }}</span>
                            </div>
                            <div class="exam-list__item-extra" v-if="exam.adviseDurationStr">
                                <span>作答时间: {{ exam.adviseDurationStr }}</span>
                            </div>
                        </div>
                        <div class="exam-list__item-actions">
                            <b-button type="primary" size="small" @click="showConfirmReset(exam)">重置</b-button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="debug-info">
                <div class="debug-info__header">
                    <h3>调试信息</h3>
                </div>
                <div class="debug-info__items">
                    <div class="debug-info__item">
                        <div class="debug-info__label">场次 SeqId:</div>
                        <div class="debug-info__value">{{ seqId || '未获取' }}</div>
                        <b-button type="primary" size="xsmall" @click="copyToClipboard(seqId as string)">复制</b-button>
                    </div>
                    <div class="debug-info__item">
                        <div class="debug-info__label">当前小考 EncryptExamId:</div>
                        <div class="debug-info__value">{{ monitorStore.examBaseInfo.examInfo?.examId || '未获取' }}</div>
                        <b-button type="primary" size="xsmall" @click="copyToClipboard(monitorStore.examBaseInfo.examInfo?.examId)">复制</b-button>
                    </div>
                    <div class="debug-info__item">
                        <div class="debug-info__label">人脸识别结果:</div>
                        <div class="debug-info__value">
                            {{ getRecognizeResultText(debuggingStore.examConfig.systemRecognizeResult) }} ({{ debuggingStore.examConfig.systemRecognizeResult }})
                        </div>
                        <b-button type="primary" size="xsmall" @click="copyToClipboard(debuggingStore.examConfig.systemRecognizeResult)">复制值</b-button>
                    </div>
                    <div class="debug-info__item">
                        <div class="debug-info__label">当前重连次数:</div>
                        <div class="debug-info__value">{{ debuggingStore.examConfig.reLoginRule?.currentReLoginCount ?? 'N/A' }}</div>
                        <b-button type="primary" size="xsmall" @click="copyToClipboard(debuggingStore.examConfig.reLoginRule?.currentReLoginCount)">复制</b-button>
                    </div>
                    <div class="debug-info__item">
                        <div class="debug-info__label">最大切屏次数:</div>
                        <div class="debug-info__value">{{ debuggingStore.examConfig.switchScreenRule?.maxSwitchCount ?? 'N/A' }}</div>
                        <b-button type="primary" size="xsmall" @click="copyToClipboard(debuggingStore.examConfig.switchScreenRule?.maxSwitchCount)">复制</b-button>
                    </div>
                </div>
            </div>

            <!-- 新增URL测试工具 -->
            <div class="url-tester">
                <div class="url-tester__header">
                    <h3>URL测试工具</h3>
                </div>
                <div class="url-tester__content">
                    <div class="url-tester__input-group">
                        <b-input v-model="urlToTest" placeholder="输入要测试的URL" />
                        <b-button type="primary" size="small" :loading="urlTesting" @click="testUrl">测试</b-button>
                    </div>
                    <div class="url-tester__status" v-if="urlTestResult">
                        <div class="url-tester__status-item">
                            <span class="url-tester__status-label">状态码:</span>
                            <span
                                class="url-tester__status-value"
                                :class="{
                                    'url-tester__status--success': urlTestResult.status >= 200 && urlTestResult.status < 300,
                                    'url-tester__status--error': urlTestResult.status >= 400,
                                }"
                            >
                                {{ urlTestResult.status }}
                            </span>
                        </div>
                        <div class="url-tester__status-item">
                            <span class="url-tester__status-label">响应时间:</span>
                            <span class="url-tester__status-value">{{ urlTestResult.time }}ms</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 新增存储查看器 -->
            <div class="storage-viewer">
                <div class="storage-viewer__header">
                    <h3>存储查看器</h3>
                    <div class="storage-viewer__tabs">
                        <div class="storage-viewer__tab" :class="{ 'storage-viewer__tab--active': storageTab === 'localStorage' }" @click="storageTab = 'localStorage'">
                            LocalStorage
                        </div>
                        <div class="storage-viewer__tab" :class="{ 'storage-viewer__tab--active': storageTab === 'sessionStorage' }" @click="storageTab = 'sessionStorage'">
                            SessionStorage
                        </div>
                        <div class="storage-viewer__tab" :class="{ 'storage-viewer__tab--active': storageTab === 'cookies' }" @click="storageTab = 'cookies'">Cookies</div>
                    </div>
                </div>
                <div class="storage-viewer__content">
                    <div class="storage-viewer__actions">
                        <b-input v-model="storageSearchKey" placeholder="搜索键名" size="small" />
                        <b-button size="small" @click="refreshStorageData">刷新</b-button>
                    </div>
                    <div class="storage-viewer__items">
                        <div v-if="filteredStorageItems.length === 0" class="storage-viewer__empty">无数据</div>
                        <div v-for="item in filteredStorageItems" :key="item.key" class="storage-viewer__item">
                            <div class="storage-viewer__item-key">{{ item.key }}</div>
                            <div class="storage-viewer__item-value">{{ item.value }}</div>
                            <div class="storage-viewer__item-actions">
                                <b-button size="xsmall" @click="copyToClipboard(item.value)">复制值</b-button>
                                <b-button status="danger" size="xsmall" @click="removeStorageItem(item.key)">删除</b-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="store-info">
                <div class="store-info__header">
                    <h3>Monitor Store State</h3>
                </div>
                <pre class="store-info__content">{{ JSON.stringify(monitorStore.$state, null, 2) }}</pre>
            </div>

            <div class="env-info">
                <div class="env-info__header">
                    <h3>Environment Info</h3>
                </div>
                <div class="env-info__content">
                    <div class="env-info__item">
                        <span class="env-info__label">Mode:</span>
                        <span class="env-info__value">{{ envMode }}</span>
                    </div>
                </div>
            </div>

            <div class="event-simulation">
                <div class="event-simulation__header">
                    <h3>事件模拟</h3>
                </div>
                <div class="event-simulation__actions">
                    <b-button status="danger" size="small" @click="simulateWebSocketDisconnect">模拟 WebSocket 断开</b-button>
                    <b-button status="success" size="small" @click="simulateWebSocketReconnect">模拟 WebSocket 重连</b-button>
                    <b-button status="danger" size="small" @click="simulateNetworkOffline">模拟网络断开</b-button>
                    <b-button status="success" size="small" @click="simulateNetworkOnline">模拟网络恢复</b-button>
                </div>
            </div>
        </div>
    </b-drawer>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue';
import { useMonitorStore } from '@/store/use-monitor-store';
import { useRoute } from 'vue-router';
import { chatService } from '@/utils/socket';
import { useDebuggingStore } from '@/views/monitor/store';

defineOptions({ name: 'DebugPanel' });

const visible = ref(false);
const loading = ref(false);

// URL测试功能
const urlToTest = ref('');
const urlTesting = ref(false);
const urlTestResult = ref<{ status: number; time: number } | null>(null);

// 存储查看器功能
const storageTab = ref<'localStorage' | 'sessionStorage' | 'cookies'>('localStorage');
const storageItems = ref<{ key: string; value: string }[]>([]);
const storageSearchKey = ref('');

// 获取过滤后的存储项
const filteredStorageItems = computed(() => {
    if (!storageSearchKey.value) return storageItems.value;
    return storageItems.value.filter((item) => item.key.toLowerCase().includes(storageSearchKey.value.toLowerCase()));
});

enum AnswerStatusEnum {
    开始作答 = 1,
    已完成 = 2,
    已结束 = 3,
}
interface IExamItem {
    encryptExamId: string; // 加密考试id
    examName: string; // 考试名称
    startTime: string; // 考试开始时间
    endTime: string; // 考试结束时间
    examSource: 0 | 2; // 关联来源 0考试 2测评
    sort: number; // 排序 从1开始
    answerTypes: (1 | 2)[]; // 作答方式 1电脑端 2手机端
    examUrl: string; // 考试链接或二维码
    answerStatus: AnswerStatusEnum; // 作答状态 1开始作答  2已完成 3已结束
    adviseDurationStr?: string; // 作答时间
    examH5Url?: string; // 考试H5链接
    combinationType: 1 | 2; // 组合类型 1 按场次时间开始 2 前场交卷即可开始
}
const examList = ref<IExamItem[]>([]);
const monitorStore = useMonitorStore();
const debuggingStore = useDebuggingStore();

// 获取环境变量
const envMode = computed(() => import.meta.env.MODE);
// const envApiUrl = computed(() => import.meta.env.VITE_API_BASE_URL); // 示例：如果定义了 VITE_API_BASE_URL

// 获取考试类型文本
const getExamSourceText = (source: 0 | 2) => {
    const sourceMap: Record<number, string> = {
        0: '考试',
        2: '测评',
    };
    return sourceMap[source] || '未知';
};

// 获取作答状态文本
const getAnswerStatusText = (status: AnswerStatusEnum) => {
    return AnswerStatusEnum[status] || '未知';
};

// 获取人脸识别结果文本
const getRecognizeResultText = (result: number | undefined) => {
    const resultMap: Record<number, string> = {
        0: '未进行',
        1: '成功',
        2: '失败',
        3: '审核中',
    };
    return result !== undefined ? resultMap[result] || '未知状态' : 'N/A';
};

const $route = useRoute();
const seqId = computed(() => $route.params.seqId || $route.query.seqId);
// 获取考试场次列表
const fetchExamList = async () => {
    loading.value = true;
    examList.value = [];

    const encryptExamId = seqId.value;
    try {
        if (encryptExamId) {
            // 从 monitorStore 获取考试场次数据
            const res = await Invoke.examList.getExamList({
                encryptExamId,
            });
            if (res.code === 0 && res.data) {
                examList.value = res.data.examList;
            }
        }
    } catch (error) {
        logger.error('获取考试场次列表失败:', error);
    } finally {
        loading.value = false;
    }
};

// 显示确认对话框
const showConfirmReset = (exam: IExamItem) => {
    resetExam(exam.encryptExamId);
    // Dialog.confirm({
    //     title: '确认重置',
    //     content: `确定要重置考试 "${exam.examName}" 的数据吗？此操作不可恢复。`,
    //     cancelText: '取消',
    //     confirmText: '确认重置',
    //     onOk: () => {

    //     },
    // });
};

// 重置考试数据
const resetExam = async (examId: string) => {
    try {
        const res = await fetch(`/wapi/web/tool/clearExamineeAnswerData?encExamId=${encodeURIComponent(examId)}`);
        const data = await res.json();

        if (data.code === 0) {
            Toast.success('重置成功');
            // 重新获取列表
            fetchExamList();
        } else {
            Toast.danger(data.msg || '重置失败');
        }
    } catch (error) {
        logger.error('重置考试数据失败:', error);
        Toast.danger('重置失败');
    }
};

// 复制到剪贴板
const copyToClipboard = (text: string | number | undefined | string[]) => {
    if (!text) {
        Toast.info('没有可复制的内容');
        return;
    }
    const textToCopy = Array.isArray(text) ? text.join(',') : String(text);

    navigator.clipboard
        .writeText(textToCopy)
        .then(() => {
            Toast.success('已复制到剪贴板');
        })
        .catch((err) => {
            logger.error('复制失败:', err);
            Toast.danger('复制失败');
        });
};

// 清除本地缓存
const clearLocalStorage = () => {
    try {
        localStorage.clear();
        Toast.success('本地缓存已清除');
    } catch (error) {
        logger.error('清除本地缓存失败:', error);
        Toast.danger('清除本地缓存失败');
    }
};

// 仅在开发环境下才加载数据
onMounted(() => {
    fetchExamList();
});

// 监听抽屉打开事件，重新获取场次列表
const handleVisibleChange = (val: boolean) => {
    if (val) {
        fetchExamList();
        refreshStorageData();
    }
};

watch(visible, handleVisibleChange);

// URL测试功能
const testUrl = async () => {
    if (!urlToTest.value) {
        Toast.info('请输入URL');
        return;
    }

    urlTesting.value = true;
    const startTime = Date.now();

    try {
        const response = await fetch(urlToTest.value);
        const endTime = Date.now();
        urlTestResult.value = {
            status: response.status,
            time: endTime - startTime,
        };
    } catch (error) {
        logger.error('URL测试失败:', error);
        Toast.danger('URL请求失败');
    } finally {
        urlTesting.value = false;
    }
};

// 存储查看器功能
const refreshStorageData = () => {
    storageItems.value = [];

    try {
        if (storageTab.value === 'localStorage') {
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key) {
                    storageItems.value.push({
                        key,
                        value: localStorage.getItem(key) || '',
                    });
                }
            }
        } else if (storageTab.value === 'sessionStorage') {
            for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i);
                if (key) {
                    storageItems.value.push({
                        key,
                        value: sessionStorage.getItem(key) || '',
                    });
                }
            }
        } else if (storageTab.value === 'cookies') {
            document.cookie.split(';').forEach((cookie) => {
                const [key, value] = cookie.trim().split('=');
                if (key) {
                    storageItems.value.push({
                        key,
                        value: value || '',
                    });
                }
            });
        }
    } catch (error) {
        logger.error('获取存储数据失败:', error);
    }
};

// 删除存储项
const removeStorageItem = (key: string) => {
    try {
        if (storageTab.value === 'localStorage') {
            localStorage.removeItem(key);
        } else if (storageTab.value === 'sessionStorage') {
            sessionStorage.removeItem(key);
        } else if (storageTab.value === 'cookies') {
            document.cookie = `${key}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
        }
        refreshStorageData();
        Toast.success(`已删除 ${key}`);
    } catch (error) {
        logger.error('删除存储项失败:', error);
        Toast.danger('删除失败');
    }
};

// 监听存储类型变化，刷新数据
watch(storageTab, refreshStorageData);

// --- Event Simulation --- //

const simulateWebSocketDisconnect = () => {
    logger.warn('[DebugPanel] Simulating WebSocket Disconnect...');
    try {
        chatService.disconnect();
        Toast.success('模拟 WebSocket 断开成功');
    } catch (error) {
        logger.error('[DebugPanel] Failed to simulate WebSocket disconnect:', error);
        Toast.danger('模拟 WebSocket 断开失败');
    }
};

const simulateWebSocketReconnect = () => {
    logger.warn('[DebugPanel] Simulating WebSocket Reconnect...');
    const baseInfo = monitorStore.examBaseInfo;
    const params = {
        encryptMobile: baseInfo.mobile,
        examId: baseInfo.examInfo?.examId,
        wsConnectSecrect: baseInfo.seqInfo?.wsConnectSecret,
    };

    if (!params.encryptMobile || !params.examId || !params.wsConnectSecrect) {
        logger.error('[DebugPanel] Missing parameters for WebSocket reconnect:', params);
        Toast.danger('无法重连 WebSocket：缺少必要参数');
        return;
    }

    try {
        chatService.connect(params);
        Toast.success('模拟 WebSocket 重连成功');
    } catch (error) {
        logger.error('[DebugPanel] Failed to simulate WebSocket reconnect:', error);
        Toast.danger('模拟 WebSocket 重连失败');
    }
};

// 模拟网络断开
const simulateNetworkOffline = () => {
    logger.warn('[DebugPanel] Simulating Network Offline...');
    try {
        // 触发offline事件
        window.dispatchEvent(new Event('offline'));
        Toast.success('模拟网络断开成功');
    } catch (error) {
        logger.error('[DebugPanel] Failed to simulate network offline:', error);
        Toast.danger('模拟网络断开失败');
    }
};

// 模拟网络恢复
const simulateNetworkOnline = () => {
    logger.warn('[DebugPanel] Simulating Network Online...');
    try {
        // 触发online事件
        window.dispatchEvent(new Event('online'));
        Toast.success('模拟网络恢复成功');
    } catch (error) {
        logger.error('[DebugPanel] Failed to simulate network online:', error);
        Toast.danger('模拟网络恢复失败');
    }
};
</script>

<style lang="less" scoped>
.debug-panel__trigger {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    margin-right: 8px;
}

/* 应用于抽屉的样式 */
.debug-panel-drawer {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

/* 考试列表区块 */
.exam-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.exam-list__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.exam-list__loading,
.exam-list__empty {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px 0;
    color: var(--gray-color-6);
}

.exam-list__list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.exam-list__item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-radius: 4px;
    background-color: var(--gray-color-1);
    gap: 16px;
}

.exam-list__item-info {
    display: flex;
    flex-direction: column;
    flex: 1;
    gap: 4px;
}

.exam-list__item-name {
    font-weight: 500;
}

.exam-list__item-status,
.exam-list__item-time,
.exam-list__item-extra {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: var(--gray-color-6);
}

exam-list__item-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* 调试信息区块 */
.debug-info {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.debug-info__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.debug-info__items {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.debug-info__item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    background-color: var(--gray-color-1);
    border-radius: 4px;
    gap: 8px;
}

.debug-info__label {
    display: flex;
    min-width: 200px;
    font-weight: 500;
    flex-shrink: 0;
}

.debug-info__value {
    display: flex;
    flex-grow: 1;
    word-break: break-all;
    overflow-wrap: break-word;
    min-width: 0;
}

.debug-info__item .b-button[size='xsmall'] {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.debug-info__actions {
    display: flex;
    padding: 8px 16px;
    gap: 10px;
}

/* URL测试工具区块 */
.url-tester {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.url-tester__header {
    display: flex;
    align-items: center;
}

.url-tester__content {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.url-tester__input-group {
    display: flex;
    gap: 8px;
}

.url-tester__status {
    display: flex;
    flex-direction: column;
    padding: 10px;
    background-color: var(--gray-color-1);
    border-radius: 4px;
    gap: 8px;
}

.url-tester__status-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.url-tester__status-label {
    font-weight: 500;
    min-width: 80px;
}

.url-tester__status--success {
    color: var(--success-color);
}

.url-tester__status--error {
    color: var(--danger-color);
}

/* 存储查看器区块 */
.storage-viewer {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.storage-viewer__header {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.storage-viewer__tabs {
    display: flex;
    gap: 12px;
    border-bottom: 1px solid var(--gray-color-3);
}

.storage-viewer__tab {
    padding: 8px 12px;
    cursor: pointer;
    color: var(--gray-color-6);
    &--active,
    &:hover {
        color: var(--primary-color);
        border-bottom: 2px solid var(--primary-color);
    }
}

.storage-viewer__content {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.storage-viewer__actions {
    display: flex;
    gap: 8px;
}

.storage-viewer__items {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 200px;
    overflow-y: auto;
}

.storage-viewer__empty {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px 0;
    color: var(--gray-color-6);
}

.storage-viewer__item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background-color: var(--gray-color-1);
    border-radius: 4px;
    gap: 8px;
}

.storage-viewer__item-key {
    font-weight: 500;
    width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.storage-viewer__item-value {
    flex: 1;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.storage-viewer__item-actions {
    display: flex;
    gap: 4px;
}

/* Store信息区块 */
.store-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.store-info__header {
    display: flex;
    align-items: center;
}

.store-info__content {
    display: block; /* pre元素保持block以保持格式化 */
    padding: 10px;
    background-color: var(--gray-color-1);
    border-radius: 4px;
    font-size: 12px;
    max-height: 300px;
    overflow-y: auto;
    word-break: break-all;
    white-space: pre-wrap;
}

/* 环境信息区块 */
.env-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.env-info__header {
    display: flex;
    align-items: center;
}

.env-info__content {
    display: flex;
    flex-direction: column;
    padding: 10px 16px;
    background-color: var(--gray-color-1);
    border-radius: 4px;
    gap: 4px;
}

.env-info__item {
    display: flex;
    align-items: center;
    font-size: 13px;
    gap: 8px;
}

.env-info__label {
    display: flex;
    font-weight: 500;
    min-width: 100px;
    flex-shrink: 0;
}

.env-info__value {
    display: flex;
    flex: 1;
    word-break: break-all;
}

/* 事件模拟区块 */
.event-simulation {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.event-simulation__header {
    display: flex;
    align-items: center;
}

.event-simulation__actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}
</style>
