<template>
    <b-dialog
        v-model="visible"
        title=""
        wrapClass="dialog-exam-monitor-hint"
        :footer="false"
        :showClose="false"
        :showLayer="false"
        :layerClosable="false"
        :enableEscClose="false"
        lock
        fullscreen
        @close="onClose"
    >
        <div class="hint-title">电脑摄像头监控异常</div>
        <div class="hint-sub-title">建议重新点击浏览器地址右侧摄像头按钮，如图所示选中对应选项，点击完成按钮后才能继续考试</div>
        <div class="img-wrap">
            <img src="@/assets/images/monitor/media-hint.jpg" alt="" style="width: 736px; height: 365px; border-radius: 8px; margin-top: 70px" />
        </div>
        <div class="btns">
            <b-button type="primary" @click="onClose"> 不处理 </b-button>
        </div>
    </b-dialog>
</template>

<script setup lang="ts" name="DialogExamMediaMonitorHint">
import { ref, watch } from 'vue';

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false,
    },
});
const emit = defineEmits(['update:modelValue', 'dialogClosed']);
const visible = ref(false);
watch(
    () => props.modelValue,
    () => {
        visible.value = props.modelValue;
        if (props.modelValue) {
            // 埋点开始
            BossAnalyticsTrack('zhice-pc-exam-paper-camera-dialog', {
                pData: {
                    type: TrackTypeEnum.失败,
                    message: 'open',
                    nameZh: '弹出电脑摄像头监控异常弹窗',
                },
            });
            // 埋点结束
        }
    },
    { immediate: true },
);
function onClose() {
    visible.value = false;
    emit('update:modelValue', visible.value);
    emit('dialogClosed', 'media');
    // 埋点开始
    BossAnalyticsTrack('zhice-pc-exam-paper-camera-dialog', {
        pData: {
            type: TrackTypeEnum.失败,
            message: 'close',
            nameZh: '关闭电脑摄像头监控异常弹窗',
        },
    });
    // 埋点结束
}
</script>
