<template>
    <canvas ref="canvasRef" />
</template>

<script setup lang="ts">
import type { QRCodeRenderersOptions } from 'qrcode';
import QRCode from 'qrcode';
import { onMounted, ref } from 'vue';

const props = defineProps({
    width: {
        type: Number,
        default: 150,
    },
    height: {
        type: Number,
        default: 150,
    },
    url: {
        type: String,
        default: '',
    },
    options: {
        type: Object,
        default: () => ({}),
    },
    onError: {
        type: Function,
        default: () => {},
    },
});
const canvasRef = ref();

onMounted(() => {
    const options = {
        errorCorrectionLevel: 'L',
        type: 'terminal',
        quality: 0.95,
        width: props.width,
        height: props.height,
        margin: 1,
        color: {
            dark: '#000',
            light: '#FFF',
        },
        ...props.options,
    } as QRCodeRenderersOptions;

    QRCode.toCanvas(canvasRef.value, props.url, options, (error: any) => {
        if (error) {
            props.onError(error);
        }
    });
});
</script>
