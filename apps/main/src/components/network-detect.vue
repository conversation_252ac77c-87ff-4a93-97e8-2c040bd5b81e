<template>
    <b-dialog
        v-model="offlineDialogVisible"
        wrapClass="offline-dialog dialog-icons-default"
        confirmText="继续答题"
        :footer="true"
        :showCancel="false"
        :showClose="false"
        :showLayer="true"
        lock
        @close="onOfflineDialogClose"
    >
        <div class="manual-header">
            <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <title>切片</title>
                <g id="页面-2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                    <g id="设备检测-检测失败" transform="translate(-323.000000, -400.000000)" fill="#2eaef1">
                        <g id="编组-15" transform="translate(323.000000, 381.000000)">
                            <g id="icon/警示备份-4" transform="translate(0.000000, 19.000000)">
                                <path
                                    id="形状结合"
                                    d="M8,1 C11.8659932,1 15,4.13400675 15,8 C15,11.8659932 11.8659932,15 8,15 C4.13400675,15 1,11.8659932 1,8 C1,4.13400675 4.13400675,1 8,1 Z M8,10 C7.44771525,10 7,10.4477153 7,11 C7,11.5522847 7.44771525,12 8,12 C8.55228475,12 9,11.5522847 9,11 C9,10.4477153 8.55228475,10 8,10 Z M8.5,4 L7.5,4 C7.22385763,4 7,4.22385763 7,4.5 L7,8.5 C7,8.77614237 7.22385763,9 7.5,9 L8.5,9 C8.77614237,9 9,8.77614237 9,8.5 L9,4.5 C9,4.22385763 8.77614237,4 8.5,4 Z"
                                />
                            </g>
                        </g>
                    </g>
                </g>
            </svg>
            <div class="header-text">网络连接断开</div>
        </div>
        <div class="manual-content">检测到你已断开网络连接，当前作答记录已保存，请检查你的网络连接状态，网络连接恢复后可继续答题。</div>
    </b-dialog>
</template>

<script setup lang="ts">
import { useNetwork } from '@crm/vueuse-pro';
import { onUnmounted, ref, watch } from 'vue';

defineOptions({
    name: 'NetworkDetect',
});

const { isSupported, isOnline } = useNetwork();

const offlineDialogVisible = ref(false);

let timer = -1;
/**
 * 允许网络断开的最大时长
 */
const MAX_ALLOWED_TIME = 30 * 1000;

if (isSupported.value) {
    watch(
        isOnline, // 监听来自vueuse的值时，不能取.value
        () => {
            if (isOnline.value) {
                offlineDialogVisible.value = false;
                window.clearInterval(timer);
                timer = -1;
                // checkIfExamDone();
            } else {
                timer = window.setTimeout(() => {
                    offlineDialogVisible.value = true;
                }, MAX_ALLOWED_TIME);
            }
        },
        { immediate: true },
    );
}

function onOfflineDialogClose() {
    offlineDialogVisible.value = false;
    timer = window.setTimeout(() => {
        offlineDialogVisible.value = true;
    }, MAX_ALLOWED_TIME);
}
onUnmounted(() => {
    offlineDialogVisible.value = false;
    window.clearInterval(timer);
    timer = -1;
});
</script>

<style lang="less">
.offline-dialog {
    .manual-header {
        display: flex;
        align-items: center;
        margin-bottom: 24px;
        padding-top: 10px;
        .header-text {
            margin-left: 8px;
            font-size: 16px;
            color: #29292d;
        }
    }
}
</style>
