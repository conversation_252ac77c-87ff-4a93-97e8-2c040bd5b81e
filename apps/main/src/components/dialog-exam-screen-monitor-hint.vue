<template>
    <b-dialog
        v-model="visible"
        title=""
        wrapClass="dialog-exam-monitor-hint"
        :footer="false"
        :showClose="false"
        :showLayer="false"
        :layerClosable="false"
        :enableEscClose="false"
        lock
        fullscreen
        @close="onClose"
    >
        <div class="hint-title">电脑屏幕监控异常</div>
        <div class="hint-sub-title">您的屏幕共享异常，请点击下方重新共享按钮，选择整个屏幕，选中共享屏幕点击共享。</div>
        <div class="img-wrap">
            <img src="@/assets/images/monitor/screen-hint.png" alt="" style="width: 736px; height: auto; border-radius: 8px; margin-top: 16px" />
        </div>
        <div class="btns">
            <b-button type="primary" @click="clickRestart"> 重新共享 </b-button>
            <b-button type="default" @click="onClose"> 不处理 </b-button>
        </div>
    </b-dialog>
</template>

<script setup lang="ts" name="DialogExamScreenMonitorHint">
import { ref, watch } from 'vue';

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false,
    },
});
const emit = defineEmits(['update:modelValue', 'startScreenShare', /* 'changeCanIncrease', */ 'dialogClosed']);

const visible = ref(false);
watch(
    () => props.modelValue,
    () => {
        visible.value = props.modelValue;
        if (props.modelValue) {
            // 埋点开始
            BossAnalyticsTrack('zhice-pc-exam-paper-screen-dialog', {
                pData: {
                    type: TrackTypeEnum.失败,
                    message: 'open',
                    nameZh: '弹出电脑屏幕监控异常弹窗',
                },
            });
            // 埋点结束
        }
    },
    { immediate: true },
);

function onClose() {
    visible.value = false;
    emit('update:modelValue', visible.value);
    emit('dialogClosed', 'screen');
    // 埋点
    BossAnalyticsTrack('zhice-pc-exam-paper-screen-dialog', {
        pData: {
            type: TrackTypeEnum.失败,
            message: 'close',
            nameZh: '关闭电脑屏幕监控异常弹窗',
        },
    });
}
async function clickRestart() {
    // 埋点
    BossAnalyticsTrack('zhice-pc-exam-begin-again', { pData: '点击重试电脑屏幕监控' });

    try {
        // 先尝试退出全屏模式，避免权限冲突
        if (document.fullscreenElement) {
            await document.exitFullscreen();
        }

        // 等待一小段时间确保全屏状态完全退出
        await new Promise(resolve => setTimeout(resolve, 300));

        // 触发重新开始屏幕共享流程
        emit('startScreenShare');

        // 关闭当前对话框
        onClose();
    } catch (error) {
        console.warn('退出全屏失败，使用页面刷新方案:', error);
        // 如果退出全屏失败，仍然使用刷新页面的方案
        window.location.reload();
    }
}
</script>

<style lang="less">
.dialog-exam-monitor-hint {
    &.b-dialog-wrap .b-dialog-container .b-dialog-modal .b-dialog-body {
        padding-top: 60px;
        padding-bottom: 0;
    }
    .img-wrap {
        text-align: center;
    }

    .hint-title {
        text-align: center;
        color: #2d2d2d;
        font-size: 18px;
        margin-bottom: 16px;
    }

    .hint-sub-title {
        text-align: center;
        color: #5d7080;
        font-size: 13px;
    }

    .btns {
        text-align: center;
        margin-top: 40px;
        position: sticky;
        bottom: 0;
        background: #fff;
        padding: 20px 0;
        .b-button {
            margin-right: 24px;
        }
    }
}
</style>
