<template>
    <b-dialog
        v-model="visible"
        title=""
        wrapClass="dialog-exam-monitor-hint"
        :footer="false"
        :showClose="false"
        :showLayer="false"
        :layerClosable="false"
        :enableEscClose="false"
        lock
        fullscreen
        @close="onClose"
    >
        <div class="hint-title">第二视角手机监控异常</div>
        <div class="hint-sub-title">建议手机重新扫码按照下方要求重新设置</div>
        <div class="container-phone">
            <div class="section-wrap section-wrap-phone">
                <div class="title">设置指南</div>
                <div class="content">
                    <div class="point point1">
                        <div class="point-title">要点一:</div>
                        <div class="point-content">
                            <p>内容务必包含</p>
                            <p>1.完整的电脑屏幕、键盘</p>
                            <p>2.双手处于监控范围内</p>
                            <p>3.桌面整洁、无杂物</p>
                        </div>
                    </div>
                    <div class="point point2">
                        <div class="point-title">要点二:</div>
                        <div class="point-content">
                            <p>务必看到考生侧脸<br />和上半身</p>
                        </div>
                    </div>
                    <div class="point point3">
                        <div class="point-title">要点三:</div>
                        <div class="point-content">
                            <p>1.手机摆放正侧方</p>
                            <p>2.距离电脑1-1.5米处</p>
                            <p>3.架高手机，俯拍桌面</p>
                        </div>
                    </div>
                    <div class="picture">
                        <img src="@/assets/images/monitor/secondary.jpg" alt="" />
                    </div>
                </div>
            </div>
            <div class="section-wrap section-wrap-qrcode">
                <div class="title">手机扫码开启监控</div>
                <H5ExamMonitorQrCode />
                <p class="qr-hint">- 扫码开启监控 -</p>
            </div>
        </div>

        <div class="btns">
            <b-button type="primary" @click="onClose"> 不处理 </b-button>
        </div>
    </b-dialog>
</template>

<script setup lang="ts" name="DialogExamPhoneMonitorHint">
import { ref, watch } from 'vue';
import H5ExamMonitorQrCode from '@/components/h5-exam-monitor-qr-code.vue';

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false,
    },
});
const emit = defineEmits(['update:modelValue', 'dialogClosed']);
const visible = ref(false);

watch(
    () => props.modelValue,
    () => {
        visible.value = props.modelValue;
        if (props.modelValue) {
            // 埋点开始
            BossAnalyticsTrack('zhice-pc-exam-paper-phone-dialog', {
                pData: {
                    type: TrackTypeEnum.失败,
                    message: 'open',
                    nameZh: '弹出手机监控异常弹窗',
                },
            });
            // 埋点结束
        }
    },
    { immediate: true },
);
function onClose() {
    visible.value = false;
    emit('update:modelValue', visible.value);
    emit('dialogClosed', 'phone');
    // 埋点开始
    BossAnalyticsTrack('zhice-pc-exam-paper-phone-dialog', {
        pData: {
            type: TrackTypeEnum.失败,
            message: 'close',
            nameZh: '关闭手机监控异常弹窗',
        },
    });
    // 埋点结束
}
</script>

<style lang="less">
.dialog-exam-monitor-hint {
    .container-phone {
        display: flex;
        width: 872px;
        margin: 0 auto;
        margin-top: 79px;

        .section-wrap {
            padding: 12px;
            border-radius: 8px;
            border: 1px solid #f0f1f2;
            height: 349px;
            position: relative;

            &:nth-child(1) {
                width: 556px;
                margin-right: 16px;
            }

            &:nth-child(2) {
                flex-grow: 1;
            }

            .title {
                margin: 0px -12px 8px;
                padding: 0 12px;
                border: 2px solid transparent;
                border-left-color: #12ada9;
                height: 12px;
                display: flex;
                align-items: center;
            }

            .sub-title {
                color: #939cbc;
                font-size: 13px;
                margin-bottom: 12px;
            }

            .content {
                .point {
                    position: absolute;
                    background: rgba(18, 173, 169, 0.06);
                    padding: 12px 16px;
                    &::after {
                        content: '';
                        position: absolute;
                    }

                    .point-title {
                        color: #12ada9;
                        margin-bottom: 8px;
                    }

                    .point-content {
                        font-size: 13px;
                        line-height: 18px;
                    }

                    &.point1 {
                        top: 84px;
                        left: 16px;
                    }

                    &.point2 {
                        top: 84px;
                        left: 374px;
                    }

                    &.point3 {
                        top: 190px;
                        left: 374px;
                    }
                }
                .picture img {
                    width: 150px;
                    margin-left: 191px;
                    margin-top: 42px;
                }
            }

            &.section-wrap-qrcode {
                .qr-hint {
                    position: absolute;
                    top: 260px;
                    left: 0;
                    width: 100%;
                    text-align: center;
                    color: #939cbc;
                }
                canvas {
                    display: block;
                    margin: 0 auto;
                    position: relative;
                    top: 69px;
                }
            }
        }
    }
}
</style>
