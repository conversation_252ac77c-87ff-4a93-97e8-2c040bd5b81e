import { createTimer, timeCenter } from '@/store/time';
import { readonly, ref } from 'vue';

export type CustomToast = {
    title: string;
    status: 'info' | 'warning';
    overlay?: boolean;
    opened: boolean;
    closeDelay?: number;
    onCloseComplete?: () => void;
};

export const useToast = () => {
    const opened = ref(false);
    const title = ref('');
    const status = ref('info');
    const overlay = ref(true);
    const onCloseComplete = ref();
    const finishTimeValue = ref(0);
    const { currentTime } = timeCenter;

    const answerToastCountdown = createTimer();

    const open = (props: Omit<CustomToast, 'opened'>) => {
        opened.value = true;
        status.value = props.status;
        title.value = props.title;
        overlay.value = props.overlay || false;
        onCloseComplete.value = props.onCloseComplete;
        finishTimeValue.value = currentTime.value + (props.closeDelay || 3000);
        answerToastCountdown.start({
            finishTime: () => finishTimeValue.value,
            onFinished: async () => {
                opened.value = false;
            },
        });
    };

    return { opened: readonly(opened), open, status, title, overlay, onCloseComplete };
};
