import type { WatchSource, WatchStopHandle } from 'vue';
import { timeCenter } from '@/store/time';

import { defineStore } from 'pinia';
import { getCurrentInstance, onUnmounted, watch } from 'vue';

interface EventCallback {
    debounceTime?: number;
    callback?: () => void;
    lastInvocationTime?: number;
}

export const globalEventCallbacks = {
    examWillEnd: { debounceTime: 60 * 1000, callback: () => logger.debug('examWillEnd'), lastInvocationTime: 0 } as EventCallback,
    paperEnd: { debounceTime: 0, callback: () => logger.debug('paperEnd'), lastInvocationTime: 0 } as EventCallback,
    pushQuestion: { debounceTime: 0, callback: () => logger.debug('pushQuestion'), lastInvocationTime: 0 } as EventCallback,

    // 更多事件及其默认回调...
};

interface CallbackFunction {
    (): void;
}

interface EventListener {
    watcher: WatchStopHandle;
    callback: CallbackFunction;
}

interface EventListenersState {
    listeners: Record<string, Set<EventListener>>;
}

export const useEventListenersStore = defineStore('eventListeners', {
    state: (): EventListenersState => ({
        listeners: {},
    }),
    actions: {
        addListener(eventName: string, listener: EventListener) {
            if (!this.listeners[eventName]) {
                this.listeners[eventName] = new Set();
            }
            this.listeners[eventName].add(listener);
        },
        removeListener(eventName: string, listener: EventListener) {
            this.listeners[eventName]?.delete(listener);
        },
    },
});

type ConditionFunction = () => boolean;

export async function useEventSubscription(
    eventName: keyof typeof globalEventCallbacks,
    condition: WatchSource<boolean> | ConditionFunction,
    customCallback: CallbackFunction | null = null,
) {
    const store = useEventListenersStore();
    const debounceTime = globalEventCallbacks[eventName].debounceTime || 0;

    const callback = customCallback || globalEventCallbacks[eventName].callback || (() => {});

    // 应用防抖逻辑
    const { currentTime } = timeCenter;

    const debounceCallback = () => {
        const lastInvocationTime = globalEventCallbacks[eventName].lastInvocationTime || 0;

        if (!lastInvocationTime || currentTime.value - lastInvocationTime >= debounceTime) {
            callback();
            globalEventCallbacks[eventName].lastInvocationTime = currentTime.value;
        }
    };

    // 监听条件的变化
    const watcher = watch(
        condition,
        (newValue) => {
            if (newValue) {
                debounceCallback();
            }
        },
        { immediate: true },
    );

    const listener = { watcher, callback: debounceCallback };
    store.addListener(eventName, listener);

    const instance = getCurrentInstance();
    if (instance) {
        onUnmounted(() => {
            watcher(); // 停止监听
            store.removeListener(eventName, listener);
        }, instance);
    }
}
