import type { IExamDebugInfo } from '@crm/exam-types';
import type { BaseInfoFormData, ExamBaseInfo } from './type';
import { STORAGE_KEY_KANJIAN_EXAM_SEQ_ID } from '@/shared';
import { createTimer, timeCenter } from '@/store/time';
import { Storage } from '@crm/exam-utils';
import dayjs from 'dayjs';
import { defineStore } from 'pinia';
import { computed, ref } from 'vue';

const examBaseInfoDefault = {
    encryptUserId: '', // 考生加密id
    userName: '', // 考生姓名
    mobile: '', // 考生加密手机号
    seqInfo: {
        // 大场次信息
        seqId: '', // 大场次加密Id
        seqName: '', // 大场次名称
        seqStartTime: '', // 大场次开始时间
        seqEndTime: '', // 大场次结束时间
        seqRemarks: '', // 大场次作答说明
        canDebug: true, // 是否考前准备
        wsConnectSecret: '', // ws连接密码
    },
    examInfo: {
        // 小考试信息
        answerInstruction: '', // 作答说明
        examType: 1, // 1-考试 2-量表 3-GBA 4-hots
        examId: '', // 加密小考试id
        examName: '', // 小考试名称
        examRemarks: '', // 小考试答题说明
        operateGuide: '', // 小考试（测评独有）操作指引：富文本
        interruptDuration: 0, // 小考试（测评独有）试卷答题过程中 用户中断时长
        questionListType: 1, // 小考试（测评独有）答题列表类型
        sort: 1, // 排序 从1开始
        answerMode: 1, // 作答模式 1定时模式 2即时模式
        examStartTime: '', // 开始作答时间 2023-01-02 14:00:00
        examEndTime: '2023-01-02 14:00:00', // 结束作答时间 2023-01-02 14:00:00,
        questionCount: 0, // 题目数量
        status: 0, // 考试状态：0-待启用；1-待开始；2-考试中；3-已结束
        statusDesc: '考试中', // 考试状态描述
        hasCommitPaper: false, // 是否已交卷:true-已交卷
        remainSeconds: 0, // 考试结束剩余秒数
        adviseDurationStr: '', // 建议作答时长 90分钟
        hasInvigilator: false, // 是否有监考官
        openCountDown: false, // （考试独有）开启倒计时
        paperReadyClickTimeTs: 0, // 考生点击开始作答时间
        adviseAnswerTime: 0, // 建议答题时间
        answerListType: 1, // 答题列表类型 1-答题列表 2-认证能力
        examEndTimeTs: 0, // 考试结束时间戳
        examStartTimeTs: 0, // 考试开始时间戳
    },
};

export const useMonitorStore = defineStore('monitor', () => {
    const encryptExamId = ref('');
    const isPreview = ref(false);
    const debugInfo = ref<IExamDebugInfo>({
        canDebug: false,
        startTime: '',
        endTime: '',
        prepareCheck: 1,
    });

    const examBaseInfo = ref<ExamBaseInfo>({ ...examBaseInfoDefault });

    const needAntiCheat = ref(false); // 是否配置防作弊功能（1多人脸 2离开 3低头 4左右张望）
    const nebulaLoaded = ref(false); // 是否已经初始化活体检测sdk
    const baseInfoFormData = ref<BaseInfoFormData>({}); // 使用导入的类型

    const { currentTime } = timeCenter;
    const examEndCountdownTimer = createTimer();

    const seqExamCountdownTimer = createTimer();

    const ExamMode = computed(() => {
        return examBaseInfo.value.seqInfo?.canDebug ? 'PREPARE_MODE' : 'EXAM_MODE';
    });

    // 根据当前时间计算考试状态，依赖于currentTime的计算属性
    const examStatus = computed(() => {
        const examInfo = examBaseInfo.value.examInfo;
        if (!examInfo || !examInfo.examStartTimeTs || !examInfo.examEndTimeTs) {
            return examInfo?.status;
        }

        // 使用时间中心当前时间，这个依赖将使计算属性随时间更新
        const now = dayjs(currentTime.value);
        const startTime = dayjs(examInfo.examStartTimeTs);
        const endTime = dayjs(examInfo.examEndTimeTs);

        let newStatus = examInfo.status;

        // 根据时间计算状态
        if (now.isBefore(startTime)) {
            // 当前时间小于开始时间：状态为1（待开始）
            newStatus = 1;
        } else if (now.isAfter(startTime) && now.isBefore(endTime)) {
            // 当前时间在开始和结束时间之间：状态为2（考试中）
            newStatus = 2;
        } else if (now.isAfter(endTime)) {
            // 当前时间大于结束时间：状态为3（已结束）
            newStatus = 3;
        }

        return newStatus;
    });

    // 更新考试状态
    const updateExamStatus = () => {
        const examInfo = examBaseInfo.value.examInfo;
        const status = examStatus.value;

        // 状态描述映射
        const statusDescMap = {
            0: '待启用',
            1: '待开始',
            2: '考试中',
            3: '已结束',
        };

        // 如果状态有变化，更新状态和描述
        if (examInfo && status !== examInfo.status) {
            examInfo.status = status || 0;
            examInfo.statusDesc = statusDescMap[status as keyof typeof statusDescMap];
        }

        return status;
    };

    // 计算考试状态并更新
    const calculateExamStatus = () => {
        return updateExamStatus();
    };

    const fetchBaseInfo = async (params: { seqId: string; examId: string }) => {
        isPreview.value = false;
        setSeqId(params.seqId);

        const res = await Invoke.common.getBaseInfo(params);

        if (res.code === 0) {
            initBaseInfo(res);
        }

        return res;
    };

    const initBaseInfo = (res: any) => {
        examBaseInfo.value = {
            ...examBaseInfoDefault,
            ...res.data,
        };

        // 如果返回的包含小场次信息
        if (res.data.examInfo) {
            const { examEndTimeTs } = res.data.examInfo;
            seqExamCountdownTimer.start({
                finishTime: () => examEndTimeTs,
            });
        }
    };

    const fetchPreviewBaseInfo = async (params: { key: string }) => {
        isPreview.value = true;
        const res = await Invoke.preview.paperBase(params);

        if (res.code === 0) {
            initBaseInfo(res);
        }
    };

    const setSeqId = (seqId: string) => {
        Storage.set(STORAGE_KEY_KANJIAN_EXAM_SEQ_ID, seqId);
    };

    const setDebugInfo = (debugInfoData: IExamDebugInfo) => {
        debugInfo.value = debugInfoData;
    };

    return {
        examEndCountdown: examEndCountdownTimer,
        encryptExamId,
        debugInfo,
        setDebugInfo,
        examBaseInfo,
        needAntiCheat,
        nebulaLoaded,
        baseInfoFormData,
        ExamMode,
        fetchBaseInfo,
        fetchPreviewBaseInfo,
        seqExamCountdownTimer,
        setSeqId,
        calculateExamStatus,
        examStatus,
        isPreview,
        initBaseInfo,
    };
});
