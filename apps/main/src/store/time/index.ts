import { createTimeManager } from '@crm/exam-hooks';
import type { HeartBeatResponse } from '../type';
import { parseURL } from '@/utils';

/**
 * 心跳检查函数
 * 向服务器发送心跳请求，获取服务器时间和可能的剩余考试时间
 * @returns 返回服务器时间戳 number
 */
async function heartBeatCheck(): Promise<number> {
    const { search, pathname } = window.location;
    const { params } = parseURL(search);

    // 从路径中解析 seqId 和 examId
    // 支持格式：/exam-list/seqId 或 /exam-list/seqId/examId
    const pathSegments = pathname.split('/').filter(Boolean);
    const seqId = params.seqId || pathSegments[1] || '';
    const examId = params.examId || pathSegments[2] || '';

    const answerQuestion = !!pathname.includes('/monitor');

    if (['preview', 'login', 'feedback'].includes(pathSegments[0])) {
        return Date.now();
    }

    try {
        const res: HeartBeatResponse = await Invoke.common.postHeartBeat(
            {
                encSeqId: seqId,
                encExamId: examId,
                dataSource: 1, // 写死参数 表示当前环境是 PC
                type: 0, // 写死参数 表示是在线，与直接关闭浏览器的区别
                answerQuestion: answerQuestion,
            },
            { noNeedLogin: true },
        );

        return res.code === 0 ? res.data.ts : Date.now();
    } catch (error) {
        return Date.now();
    }
}

export const { timeCenter, createTimer } = createTimeManager({ syncTime: heartBeatCheck });
