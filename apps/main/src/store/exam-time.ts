import { defineStore } from 'pinia';
import { ref } from 'vue';
import { createTimer } from './time';
import { TimerStatus } from '@crm/exam-hooks';

export const useEndTimeStore = defineStore('examEndTime', () => {
    const examEndTimeStatus = ref(0); // 0: 未开始 1:进行中
    const examEndCountdownTimer = createTimer();

    // 开启场次倒计时
    const initExamEndTime = (timeTs: number) => {
        examEndCountdownTimer.start({
            key: 'examEndCountdown',
            finishTime: () => timeTs,
        });
    };

    // 关闭场次倒计时
    const clearExamEndTime = () => {
        examEndTimeStatus.value = 0;
        examEndCountdownTimer.destroy();
    };

    watch(
        () => examEndCountdownTimer.status,
        (status) => {
            if (status !== TimerStatus.idled) {
                examEndTimeStatus.value = 1;
            } else {
                examEndTimeStatus.value = 0;
            }
        },
    );

    return {
        examEndTimeStatus,
        examEndTime: examEndCountdownTimer,
        initExamEndTime,
        clearExamEndTime,
    };
});
