import { NEBULA } from '@/types/nebulartc';
import type { CameraStatus, PhoneStatus, ScreenStatus } from './type';

/**
 * Helper function to safely unsubscribe from RTC events.
 * @param client RTC client instance
 * @param eventName Event name to unsubscribe from
 * @param handler Dummy handler (original code used empty fn)
 */
export function safeOff(client: NEBULA.RtcClient | null, eventName: string | undefined, handler: () => void) {
    if (client && eventName) {
        try {
            client.off(eventName, handler);
        } catch (e) {
            logger.warn(`Failed to unsubscribe from event ${eventName}`, e);
        }
    }
}

/**
 * Helper function to clean up phone monitoring.
 * @param phoneState The reactive state object for the phone monitor.
 * @param hasPhone Boolean indicating if phone monitoring is enabled.
 */
export async function cleanupPhone(phoneState: PhoneStatus, hasPhone: boolean) {
    const client = phoneState.client;
    const stream = phoneState.remoteStream; // Phone uses remoteStream

    // Check if cleanup is necessary
    if (!(client && stream && phoneState.status !== 0 && hasPhone)) {
        return; // Nothing to clean up
    }

    try {
        // Safely unsubscribe from events
        const eventMap = client.TYPE?.EVENT;
        if (eventMap) {
            // Using a dummy handler as in the original code
            safeOff(client, eventMap.STREAM_ADDED, () => {});
            safeOff(client, eventMap.STREAM_REMOVED, () => {});
            safeOff(client, eventMap.STREAM_UPDATED, () => {});
            safeOff(client, eventMap.STREAM_SUBSCRIBED, () => {});
        }

        await client.unsubscribe(stream);
        await client.leave();
        phoneState.status = 0; // Mark as closed/inactive
        phoneState.remoteStream = null;
        phoneState.client = null;
    } catch (error: any) {
        // Reset state even on error to avoid inconsistent states
        phoneState.status = 1; // Mark as error/failed
        phoneState.remoteStream = null;
        phoneState.client = null;
    }
}

/**
 * Generic helper function for local stream cleanup (Screen, Camera).
 * @param monitorName Name for logging ('Screen' or 'Camera').
 * @param monitorState The reactive state object for the monitor.
 * @param isEnabled Boolean indicating if this monitor is enabled.
 */
export async function cleanupLocalMonitor(
    monitorName: 'Screen' | 'Camera',
    monitorState: ScreenStatus | CameraStatus, // Use specific types from type.ts
    isEnabled: boolean,
) {
    const client = monitorState.client;
    // Access localStream safely based on the type, but TS knows it exists here
    const stream = monitorState.localStream;

    // Check if cleanup is necessary
    if (!(client && stream && monitorState.status !== 0 && isEnabled)) {
        return; // Nothing to clean up
    }

    try {
        // Order matters: stop/close stream before unpublishing/leaving
        await stream.stop();
        await stream.close();
        await client.unpublish(stream);
        await client.leave();
        monitorState.status = 0; // Mark as closed/inactive
        monitorState.localStream = null; // localStream for both
        monitorState.client = null;
    } catch (error: any) {
        logger.error(`Error closing ${monitorName} monitor:`, error?.message || error);
        // Reset state even on error
        monitorState.status = 1; // Mark as error/failed
        monitorState.localStream = null;
        monitorState.client = null;
    }
}
