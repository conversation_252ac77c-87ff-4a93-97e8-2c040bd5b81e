// composables/useTrackableFlow.ts
import { ref } from 'vue'; // 可选：如果你想要响应式状态，比如 isRunning
import { trackerService } from './track-service';
import { logger, Logger } from '@/utils/logger'; // 导入 Logger 和全局 logger 实例

// 业务逻辑函数的类型，该函数将接收 flowId
// TArgs: 原始逻辑期望的参数元组类型
// TReturn: 原始逻辑的返回类型

// 定义 FlowContext 类型
export interface FlowContext<TArgs extends any[] = any[]> {
    flowId: string;
    logger: Logger; // 预配置了 flowId 的 logger 实例
    logStep: (stepName: string, payload?: { status?: 'success' | 'failure'; [key: string]: any }) => void; // 预绑定 flowId 的 logStep 函数
    args: TArgs; // 原始参数，如果在回调中需要的话
}

type FlowLogicCallback<TArgs extends any[], TReturn> = (
    flowContext: FlowContext<TArgs>,
    ...originalArgs: TArgs // 从 ...args 重命名为 ...originalArgs 以更清晰，虽然功能上如果保持为 ...args 也是一样的
) => Promise<TReturn> | TReturn;

/**
 * 为业务流创建可追踪的执行上下文。
 * @param flowName 用于追踪的业务流名称。
 * @param logicCallback 实际的业务逻辑函数。它必须接受 FlowContext 作为其第一个参数。
 * @param options 可追踪流的可选配置。
 * @param options.transformArgsForLogging 在记录之前转换参数的可选函数。
 * @param options.transformResultForLogging 在记录之前转换结果的可选函数。
 * @returns 包含 `execute` 函数的对象，用于运行被追踪的流。
 */
export function useTrackableFlow<TArgs extends any[], TReturn>(
    flowName: string,
    logicCallback: FlowLogicCallback<TArgs, TReturn>,
    options?: {
        onError?: (err: unknown) => void;
        transformArgsForLogging?: (args: TArgs) => any;
        transformResultForLogging?: (result: TReturn) => any;
    },
) {
    const isRunning = ref(false);
    const error = ref<unknown | null>(null);

    /**
     * 在追踪的流上下文中执行业务逻辑。
     * @param args 传递给底层业务逻辑函数的参数。
     * @returns 一个 promise，解析为业务逻辑的结果。
     */
    const execute = async (...args: TArgs): Promise<TReturn> => {
        if (isRunning.value) {
            // 在这里使用全局 logger 或通用 logger，因为流特定的 logger 尚未创建。
            const errorMessage = `[useTrackableFlow] 流 "${flowName}" 已经在运行中。`;
            logger.warn(errorMessage);
            throw new Error(errorMessage);
        }

        const flowId = trackerService.generateFlowId();
        // 创建一个流特定的 logger 实例。
        // 这个 logger 的上下文可以是例如 `flow:${flowName}:${flowId.substring(0,8)}`
        // 以在控制台输出中清晰标识它（如果需要）。
        const flowLogger = logger.withFlowId(flowId); // 来自 logger.ts 的 logger 实例

        const startTime = performance.now();
        isRunning.value = true;
        error.value = null;

        // 预绑定 logStep 与当前的 flowId
        const logStepInFlow = (stepName: string, payload?: any) => {
            trackerService.logStep(flowId, stepName, payload);
        };

        // 构造流上下文
        const flowContext: FlowContext<TArgs> = {
            flowId,
            logger: flowLogger,
            logStep: logStepInFlow,
            args,
        };

        const loggedArgs = options?.transformArgsForLogging ? options.transformArgsForLogging(args) : args;
        trackerService.logFlowStart(flowId, flowName, loggedArgs); // 记录可能转换后的参数

        try {
            // 执行提供的逻辑，传递 flowContext 和原始参数
            const result = await logicCallback(flowContext, ...args);
            const durationMs = performance.now() - startTime;
            const loggedResult = options?.transformResultForLogging ? options.transformResultForLogging(result) : result;
            trackerService.logFlowEnd(flowId, flowName, durationMs, 'success', loggedResult); // 记录可能转换后的结果
            return result;
        } catch (err) {
            const durationMs = performance.now() - startTime;
            // 在这里使用流特定的 logger，以便错误日志包含 flowId
            flowContext.logger.error(`[useTrackableFlow Error][${flowName}] 流执行失败。`, err);
            error.value = err;
            trackerService.logFlowEnd(flowId, flowName, durationMs, 'failure', err);
            options?.onError?.(err); // 调用 onError 回调
            throw err; // 重新抛出以便调用者可以处理它
        } finally {
            isRunning.value = false;
        }
    };

    return {
        execute,
        isRunning,
        error,
    };
}
