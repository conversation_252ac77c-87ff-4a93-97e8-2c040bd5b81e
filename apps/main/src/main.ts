import compoentsPlugin from '@crm/exam-components'; // 全局组件
import directivePlugin from '@crm/exam-directives'; // 自定义指令
import { setUUID } from '@crm/exam-utils';
import { createPinia } from 'pinia';
import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import { DEPLOY_ENV } from './shared';
import { preloadRoutes } from './utils';
import { apmSendCustom } from './utils/apm';

// 尽早调用 interceptConsole 来捕获包括 index.html 中的 console 调用
interceptConsole();
logger.info('Application bootstrap...');

// 设置日志同步中间件
Logger.use(async (record) => {
    try {
        // 确保 APM 已经初始化
        if (!(window as any).customReportData) {
            return record;
        }

        // 1. 从持久化存储获取未同步的日志
        const logs = await Logger.getPersistenceCache();
        const unsyncedLogs = logs.filter((log) => !log.synced);

        if (unsyncedLogs.length > 0) {
            // 2. 发送到远程
            await apmSendCustom(unsyncedLogs);
            // 3. 修改：只要尝试上报就标记为已同步，不再检查是否成功
            await Logger.updateLogSyncStatus(
                unsyncedLogs.map((log) => log.recordId),
                true,
            );
        }
    } catch (error) {
        logger.error('Failed to sync logs:', error);
        // 即使发生错误，也可以考虑标记为已同步，因为我们已经尝试过了
        // 但这里保持原有的错误处理逻辑，不标记为同步
    }

    return record;
});

export const app = createApp(App);

const pinia = createPinia(); // 创建pinia实例
app.use(pinia).use(router).use(compoentsPlugin).use(directivePlugin);
app.mount('#app');
Dialog._context = app._context; // 组件没有办法获取当前的 Vue Context

// 设置浏览器唯一标识
const uuid = setUUID();
const isProdEnv = DEPLOY_ENV === 'prod';
const isDevEnv = DEPLOY_ENV === 'dev';

// 初始化新干线埋点
BossAnalyticsInit({ isProd: isProdEnv });

logger.debug(`当前运行环境 ${DEPLOY_ENV}，浏览器唯一标识 ${uuid}`);

// 添加Apm-jsError监控
app.config.errorHandler = (err, vm, info) => {
    logger.error(err, info);
    if (isDevEnv) {
        try {
            (window as any).vueErrorHandler(err, vm, info);
        } catch (e) {
            logger.error(e);
        }
    }
};

// 解决开发环境下的路由懒加载刷新问题
if (isDevEnv) {
    preloadRoutes(router);
}
