<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>切片</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="阅卷6备份" transform="translate(-1226.000000, -725.000000)">
            <g id="编组-31" transform="translate(946.000000, 71.000000)">
                <g id="编组-26" transform="translate(268.000000, 642.000000)">
                    <g id="旋转" transform="translate(12.000000, 12.000000)">
                        <circle id="椭圆形备份-3" cx="12" cy="12" r="12"></circle>
                        <g id="编组" transform="translate(4.000000, 3.000000)" stroke="currentColor" stroke-linecap="round" stroke-width="1.5">
                            <rect id="矩形" stroke-linejoin="round" transform="translate(8.285534, 9.035500) rotate(-45.000000) translate(-8.285534, -9.035500) " x="5.78553391" y="6.5355" width="5" height="5" rx="1"></rect>
                            <path d="M16.31005,5 C14.83735,2.03655 11.7793,0 8.2456,0 C4.71185,0 1.7227,2.03655 0.25,5" id="路径"></path>
                            <line x1="0.25" y1="1" x2="0.25" y2="5" id="路径"></line>
                            <line x1="3.64015" y1="5" x2="0.250065" y2="5" id="路径"></line>
                            <path d="M0.25,13 C1.7227,15.96345 4.78075,18 8.3145,18 C11.8482,18 14.83735,15.96345 16.31005,13" id="路径"></path>
                            <line x1="16.31005" y1="17" x2="16.31005" y2="13" id="路径"></line>
                            <line x1="12.9199" y1="13" x2="16.31" y2="13" id="路径"></line>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>