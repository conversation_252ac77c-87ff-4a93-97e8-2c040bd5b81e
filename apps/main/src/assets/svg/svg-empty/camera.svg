<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>切片</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="状态" transform="translate(-326.000000, -90.000000)">
            <g id="编组-5备份-2" transform="translate(314.000000, 80.000000)">
                <g id="编组-21" transform="translate(12.000000, 10.000000)">
                    <rect id="矩形" x="0" y="0" width="20" height="20"></rect>
                    <g id="编组" transform="translate(4.000000, 3.000000)" stroke="#2D2D2D" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5">
                        <path d="M6,12 C9.31372,12 12,9.31372 12,6 C12,2.68628 9.31372,0 6,0 C2.68628,0 0,2.68628 0,6 C0,9.31372 2.68628,12 6,12 Z" id="路径"></path>
                        <path d="M6,8 C7.10457333,8 8,7.10457333 8,6 C8,4.89542667 7.10457333,4 6,4 C4.89542667,4 4,4.89542667 4,6 C4,7.10457333 4.89542667,8 6,8 Z" id="路径备份"></path>
                        <line x1="2" y1="14" x2="10" y2="14" id="路径"></line>
                    </g>
                    <line x1="10" y1="15" x2="10" y2="17" id="路径-4" stroke="#2D2D2D" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></line>
                </g>
            </g>
        </g>
    </g>
</svg>