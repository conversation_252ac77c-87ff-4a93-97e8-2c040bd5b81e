<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>切片</title>
    <g id="v2.3" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="通知+添加考生" transform="translate(-1264.000000, -415.000000)">
            <g id="编组-4备份-2" transform="translate(1023.000000, 0.000000)">
                <g id="编组-24" transform="translate(24.000000, 156.000000)">
                    <g id="编组-17" transform="translate(32.000000, 32.000000)">
                        <g id="编组-26" transform="translate(44.000000, 74.000000)">
                            <g id="编组" transform="translate(149.000000, 161.000000) rotate(-270.000000) translate(-149.000000, -161.000000) translate(141.000000, 153.000000)">
                                <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                                <path d="M11.3476154,6.38761543 L11.3476154,12.0876154 C11.3476154,12.3637578 11.1237578,12.5876154 10.8476154,12.5876154 L5.14761543,12.5876154 C4.87147306,12.5876154 4.64761543,12.3637578 4.64761543,12.0876154 L4.64761543,11.6876154 C4.64761543,11.4114731 4.87147306,11.1876154 5.14761543,11.1876154 L9.94761543,11.1876154 L9.94761543,11.1876154 L9.94761543,6.38761543 C9.94761543,6.11147306 10.1714731,5.88761543 10.4476154,5.88761543 L10.8476154,5.88761543 C11.1237578,5.88761543 11.3476154,6.11147306 11.3476154,6.38761543 Z" id="路径" fill="currentColor" fill-rule="nonzero" transform="translate(7.997615, 9.237615) scale(1, -1) rotate(-315.000000) translate(-7.997615, -9.237615) "></path>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>