<?xml version="1.0" encoding="UTF-8"?>
<!-- <svg width="221px" height="221px" viewBox="0 0 221 221" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>路径@3x</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
        <g id="考生考试9" transform="translate(-795.000000, -426.000000)" stroke="" stroke-width="4">
            <path d="M1013.9055,531.930144 C1011.51052,474.126735 963.891998,428 905.5,428 C845.577105,428 797,476.577105 797,536.5 C797,596.422895 845.577105,645 905.5,645 C964.899978,645 1013.15132,597.267012 1013.99008,538.066135" id="路径"></path>
        </g>
    </g>
</svg> -->
<svg width="440" height="440" viewBox="0 0 440 440" class="center">
    <defs>
        <linearGradient x1="1" y1="0" x2="0" y2="0" id="gradient1">
            <stop offset="0%" stop-color="#7cd2d0"></stop>
            <stop offset="100%" stop-color="#ecf9f9"></stop>
        </linearGradient>
       <linearGradient x1="1" y1="0" x2="0" y2="0" id="gradient2">
            <stop offset="0%" stop-color="#7cd2d0"></stop>
            <stop offset="100%" stop-color="#14ada9"></stop>
        </linearGradient>
    </defs>
    <g transform="matrix(0,-1,1,0,0,440)">
        <!-- <circle cx="220" cy="220" r="170" stroke-width="3" stroke="#f0f1f3" fill="none" stroke-dasharray="1069 1069"></circle> -->
        <circle cx="220" cy="220" r="170" stroke-width="7" stroke="url('#gradient1')" fill="none" stroke-dasharray="1069 1069"></circle>
        <circle cx="220" cy="220" r="170" stroke-width="7" stroke="url('#gradient2')" fill="none" stroke-dasharray="534.5 1069"></circle>
    </g>
</svg>