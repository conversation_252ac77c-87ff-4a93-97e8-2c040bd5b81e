import type { RouteRecordRaw } from 'vue-router';
import { createRouter, createWebHistory } from 'vue-router';
import routerDefend from './router-defend';

const routes: RouteRecordRaw[] = [
    {
        path: '/',
        name: 'root',
        component: () => import(/** webpackChunkName: main */ '@/views/layout/index.vue'), // 获取base信息 大场次
        children: [
            {
                name: 'examList', // 考试列表页面
                path: '/exam-list/:seqId',
                component: () => import('@/views/exam-list/index.vue'),
            },
            {
                name: 'monitor', // 考试流程页面
                path: '/monitor',
                component: () => import('@/views/monitor/index.vue'),
                children: [
                    {
                        name: 'ready', // 设备调试页面
                        path: 'ready',
                        component: () => import('@/views/monitor/ready/index.vue'),
                        children: [
                            {
                                name: 'done', // 考前调试完成页面
                                path: 'done',
                                component: () => import('@/views/monitor/ready/done/index.vue'),
                            },
                        ],
                    },
                    {
                        name: 'answering', // 答题页面
                        path: 'answering',
                        component: () => import('@/views/monitor/answering/index.vue'),
                        children: [
                            {
                                name: 'exam',
                                path: 'exam',
                                component: () => import('@/views/monitor/answering/exam/index.vue'),
                            },
                            {
                                name: 'evaluation',
                                path: 'evaluation',
                                component: () => import('@/views/monitor/answering/evaluation/index.vue'),
                            },
                            {
                                name: 'gba',
                                path: 'gba',
                                component: () => import('@/views/monitor/answering/gba/index.vue'),
                            },
                            {
                                name: 'hots',
                                path: 'hots',
                                component: () => import('@/views/monitor/answering/hots/index.vue'),
                            },
                        ],
                    },
                ],
            },

            // 考试时间异常公共页面
            {
                name: 'status', // name在业务内有引用，不可随意修改和删除 $route.name === 'status'
                path: '/status/:seqId',
                component: () => import('@/views/status/index.vue'),
            },
        ],
    },
    {
        path: '/preview',
        name: 'preview',
        component: () => import(/** webpackChunkName: preview */ '@/views/preview/index.vue'),
    },
    {
        path: '/login',
        name: 'login',
        component: () => import(/** webpackChunkName: common */ '@/views/login/index.vue'),
    },
    {
        path: '/feedback/:status',
        name: 'feedback',
        component: () => import('@/views/mpa/feedback/index.vue'),
    },
    {
        path: '/:label(no-permission[^/]*)/:id(.*)*',
        name: 'page403',
        component: () => import(/** webpackChunkName: error */ '@/views/error/403.vue'),
    },
    {
        path: '/:pathMatch(.*)*',
        name: 'page404',
        component: () => import(/** webpackChunkName: error */ '@/views/error/404.vue'),
    },
];

const router = createRouter({
    history: createWebHistory(import.meta.env.BASE_URL),
    routes,
});

// 路由守卫处理
routerDefend(router);

export default router;
