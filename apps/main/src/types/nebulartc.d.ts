// apps/main/src/types/nebulartc.d.ts
// 基于提供的文档截图和现有用法的推断补充类型定义

// 提前定义类型和接口，避免因变量提升导致的问题

declare namespace NEBULA {
    /**
     * SDK 日志输出级别。
     * 用于控制控制台打印的日志详细程度。
     * Note: 数值基于通用实践推断，请以实际 SDK 为准。
     */
    enum LogLevel {
        /** 输出所有级别的日志 (最详细) */
        DEBUG = 0,
        /** 输出 INFO, WARN, ERROR 级别的日志 */
        INFO = 1,
        /** 输出 WARN, ERROR 级别的日志 */
        WARN = 2,
        /** 仅输出 ERROR 级别的日志 */
        ERROR = 3,
        /** 关闭所有日志输出 (最高级别) */
        OFF = 4,
    }

    /**
     * 创建 RTC 客户端 (RtcClient) 所需的配置参数。
     */
    interface ClientConfig {
        /** 应用 ID，标识你的应用 */
        appId: string;
        /** 用户签名，用于鉴权 */
        userSig: string;
        /** 用户权限控制字符串 */
        userAuth: string;
        /** 需要加入的房间 ID */
        roomId: string;
        /** 用户的唯一标识符 */
        userId: string;
        /**
         * RTC 模式。
         * 'rtc': 实时通话模式，低延迟，适用于视频通话、语音通话等场景。
         * 'live': 直播模式，可能针对大观众、高并发场景优化。
         * 'meeting': 会议模式，可能包含特定的会议管理功能 (此模式由现有用法确认)。
         */
        mode: 'rtc' | 'live' | 'meeting';
    }

    /**
     * 加入房间时所需的配置参数。
     */
    interface JoinConfig {
        /** 需要加入的房间 ID */
        roomId: string;
        /**
         * 用户在房间中的角色。
         * 'publisher': 发布者，可以发布和订阅音视频流。
         * 'subscriber': 订阅者，只能订阅音视频流。
         * 'publisher_only': 只发布者，只能发布音视频流 (此角色由现有用法确认)。
         */
        role: 'publisher' | 'subscriber' | 'publisher_only';
    }

    /**
     * 创建本地音视频流 (RtcStream) 所需的配置参数。
     */
    interface StreamConfig {
        /** 本地流所属的用户 ID */
        userId: string;
        /** 是否采集音频，默认为 false */
        audio?: boolean;
        /** 是否采集视频，默认为 false */
        video?: boolean;
        /** 是否采集屏幕共享流，默认为 false */
        screen?: boolean;
        /** 是否开启动态码率，默认为 false */
        dynamicBitrate?: boolean;
        /** (推断) 是否使用分离的音视频流 (例如，摄像头和屏幕共享分开处理) */
        useSeparateStreams?: boolean;
        /** (新增) 是否开启本地视频镜像，默认为 false */
        mirror?: boolean;
        /** (新增) 指定要使用的摄像头设备 ID */
        cameraId?: string;
        /** (新增) 指定要使用的麦克风设备 ID */
        microphoneId?: string;
        /** (新增) 屏幕共享约束条件 */
        screenConstraints?: {
            video?: {
                displaySurface?: 'monitor' | 'window' | 'application' | 'browser';
                logicalSurface?: boolean;
                cursor?: 'never' | 'always' | 'motion';
            };
        };
    }

    /**
     * 订阅远端音视频流时的可选配置参数。
     */
    interface SubscribeConfig {
        /** 是否订阅音频流，默认为 true */
        audio?: boolean;
        /** 是否订阅视频流，默认为 true */
        video?: boolean;
    }

    /**
     * 视频属性的详细配置对象，用于精确控制视频质量。
     */
    interface VideoProfileConfig {
        /** 视频宽度 (像素) */
        width: number;
        /** 视频高度 (像素) */
        height: number;
        /** 视频帧率 (fps) */
        frameRate: number;
        /** 视频码率 (kbps) */
        bitrate: number;
    }

    /**
     * 视频属性的预设字符串快捷方式。
     * SDK 会根据预设值自动选择合适的宽度、高度、帧率和码率。
     * Note: 可能存在更多预设值，请参考 SDK 文档。
     */
    type VideoProfilePreset = '120p' | '240p' | '360p' | '480p' | '720p' | '1080p' | '1440p' | '4K';

    /**
     * 屏幕共享属性的详细配置对象。
     * 推断其结构与 VideoProfileConfig 相同，请以实际 SDK 为准。
     */
    type ScreenProfileConfig = VideoProfileConfig;

    /**
     * 屏幕共享属性的预设字符串快捷方式。
     * Note: 可能存在更多预设值，请参考 SDK 文档。
     */
    type ScreenProfilePreset = '480p' | '720p' | '1080p' | '4K';

    /**
     * RtcStream (音视频流) 相关事件的类型名称集合。
     */
    type StreamEventType = 'player-state-changed' | 'screen-sharing-stopped';

    /**
     * RtcClient (RTC 客户端) 相关事件的类型名称集合。
     */
    type ClientEventType =
        | 'error' // SDK 发生错误
        | 'connection-state-changed' // 连接状态变化
        | 'stream-added' // 远端流增加
        | 'stream-removed' // 远端流移除
        | 'stream-updated' // 远端流更新 (例如，远端用户开关了摄像头或麦克风)
        | 'stream-subscribed' // 成功订阅到远端流
        | 'peer-join' // 远端用户加入房间
        | 'peer-leave'; // 远端用户离开房间

    /**
     * 'error' 事件的回调参数类型。
     */
    interface ErrorEvent {
        /** 错误码 (可选，具体结构需确认) */
        code?: number;
        /** 错误描述信息 */
        message: string;
        /** 其他可能的错误相关属性 */
        [key: string]: any;
    }

    /**
     * 'player-state-changed' 事件 (由 RtcStream 触发) 的回调参数类型。
     * 表示流的播放状态发生变化。
     */
    interface PlayerStateChangedEvent {
        /** 当前播放状态: 'PLAYING' (播放中), 'PAUSED' (已暂停), 'STOPPED' (已停止) */
        state: 'PLAYING' | 'PAUSED' | 'STOPPED';
        /** 状态变化的流类型: 'video' 或 'audio' */
        type: 'video' | 'audio';
        /** (推断) 状态变化的原因 (可选) */
        reason?: string;
    }

    /**
     * 'connection-state-changed' 事件 (由 RtcClient 触发) 的回调参数类型。
     * 表示客户端与服务器的连接状态发生变化。
     */
    interface ConnectionStateChangedEvent {
        /** 变化之前的连接状态 */
        prevState: string; // 例如: 'DISCONNECTED', 'CONNECTING', 'CONNECTED', 'RECONNECTING', 'DISCONNECTING'
        /** 当前的连接状态 */
        state: string; // 例如: 同上。'disconnected', 'reconnecting', 'reconnected' 由现有用法确认。
    }

    /**
     * 'peer-join' 和 'peer-leave' 事件 (由 RtcClient 触发) 的回调参数类型。
     * 表示房间内其他用户的加入或离开。
     */
    interface PeerEvent {
        /** 加入或离开的用户的 ID */
        userId: string;
    }

    /**
     * RTC 音视频流实例，可以是本地流或远端流。
     * 代表一路具体的音频或视频数据。
     */
    interface RtcStream {
        /**
         * 设置本地视频流的属性 (分辨率、帧率、码率)。
         * @param profile 可以是预设字符串 (如 '720p') 或详细的配置对象。
         */
        setVideoProfile(profile: VideoProfilePreset | VideoProfileConfig): void;

        /**
         * 设置本地屏幕共享流的属性 (分辨率、帧率、码率)。
         * @param profile 可以是预设字符串 (如 '1080p') 或详细的配置对象。
         */
        setScreenProfile(profile: ScreenProfilePreset | ScreenProfileConfig): void;

        /**
         * 初始化本地流。
         * 对于摄像头和麦克风流，此操作会触发浏览器请求设备权限。
         * 必须在 `publish` 或 `play` 之前调用。
         * @returns Promise，在初始化成功后 resolve。
         */
        initialize(): Promise<void>;

        /**
         * (推断) 设置视频内容提示，用于帮助 SDK 优化视频编码策略。
         * @param hint 内容提示类型，例如 'detail' (细节优先，适用于文档共享)、'motion' (流畅度优先，适用于画面变化较快场景)。
         */
        setVideoContentHint(hint: 'detail' | 'motion' | string): void;

        /**
         * 在指定的 HTML 元素中播放该流。
         * @param elementIdOrDOM 目标 DOM 元素的 ID (字符串) 或 HTMLElement 对象。
         * @param options 可选的播放配置，例如 `{ objectFit: 'contain' }` 控制视频填充方式。
         * @returns Promise，在开始播放后 resolve。
         */
        play(elementIdOrDOM: string | HTMLElement, options?: { objectFit?: string; muted?: boolean }): Promise<void>;

        /**
         * 获取流中的音频轨道 (MediaStreamTrack)。
         * 如果流不包含音频，则返回 null。
         */
        getAudioTrack(): MediaStreamTrack | null;

        /**
         * 获取流中的视频轨道 (MediaStreamTrack)。
         * 如果流不包含视频，则返回 null。
         */
        getVideoTrack(): MediaStreamTrack | null;

        /**
         * 监听流相关的事件。
         * @param eventName 事件名称，例如 'player-state-changed'。
         * @param listener 事件回调函数。
         */
        on(eventName: 'player-state-changed', listener: (event: PlayerStateChangedEvent) => void): void;
        /** 监听屏幕共享停止事件 (通常由用户手动点击浏览器自带的停止按钮触发) */
        on(eventName: 'screen-sharing-stopped', listener: () => void): void;
        // 如果 SDK 支持其他流事件，可在此处补充
        on(eventName: string, listener: (...args: any[]) => void): void; // 保底签名，兼容未知事件

        /**
         * 检查当前流是否包含音频轨道。
         * @returns 如果包含音频则返回 true，否则返回 false。
         */
        hasAudio(): boolean;

        /**
         * 检查当前流是否包含视频轨道。
         * @returns 如果包含视频则返回 true，否则返回 false。
         */
        hasVideo(): boolean;

        /**
         * 停止流的播放。
         * 调用此方法会触发 'player-state-changed' 事件 (state: 'STOPPED')。
         * 注意：此方法通常不会停止设备采集 (摄像头灯可能还亮着)。要关闭设备需调用 `close()`。
         * @returns 可能返回 Promise (根据文档确认)。
         */
        stop(): void | Promise<void>;

        /**
         * 关闭流。
         * 对于本地流，此操作会停止设备采集 (例如关闭摄像头)，并释放相关资源。
         * 对于远端流，行为可能不同，请参考文档。
         * 流关闭后不可恢复，需要重新创建。
         */
        close(): void;

        /**
         * 获取流的唯一标识符。
         */
        getId(): string;

        /**
         * 获取该流所属用户的 ID。
         */
        getUserId(): string;

        /**
         * 获取流的类型。
         * @returns 'local' 表示本地流，'remote' 表示远端流。
         */
        getType(): 'local' | 'remote';

        /**
         * 视频流对象，可能为 null
         */
        videoStream: NEBULA.RtcStream | null;
    }

    /**
     * 'stream-added', 'stream-removed', 'stream-updated', 'stream-subscribed' 事件 (由 RtcClient 触发) 的回调参数类型。
     * 表示房间内远端流的变化。
     */
    interface StreamEvent {
        /** 发生变化的远端流所属用户的 ID */
        userId: string;
        /** 发生变化的远端流实例 */
        stream: RtcStream;
    }

    /**
     * RTC 客户端实例。
     * 是进行所有 RTC 操作的核心对象，例如加入/离开房间、发布/订阅流等。
     */
    interface RtcClient {
        /** (新增) SDK 版本号 */
        readonly VERSION?: string;
        /** (新增) 当前应用的AppId，推断为只读 */
        readonly appId?: string;
        /** (新增) 当前用户的UserId，推断为只读 */
        readonly userId?: string;

        /**
         * 监听客户端相关的事件。
         * @param eventName 事件名称，例如 'connection-state-changed', 'stream-added'。
         * @param listener 事件回调函数。
         */
        on(eventName: 'error', listener: (event: ErrorEvent) => void): void;
        on(eventName: 'connection-state-changed', listener: (event: ConnectionStateChangedEvent) => void): void;
        on(eventName: 'stream-added' | 'stream-removed' | 'stream-updated' | 'stream-subscribed', listener: (event: StreamEvent) => void): void;
        on(eventName: 'peer-join' | 'peer-leave', listener: (event: PeerEvent) => void): void;
        // 保底签名，兼容未知事件
        on(eventName: string, listener: (...args: any[]) => void): void;

        /**
         * 取消监听客户端相关的事件。
         * @param eventName 事件名称，例如 'connection-state-changed', 'stream-added'。
         * @param listener 事件回调函数。
         */
        off(eventName: string, listener: (...args: any[]) => void): void;

        /**
         * 加入指定的 RTC 房间。
         * @param config 加入房间所需的配置，包括房间 ID 和用户角色。
         * @returns Promise，在成功加入房间后 resolve。
         */
        join(config: JoinConfig): Promise<void>;

        /**
         * 离开当前所在的 RTC 房间。
         * 离开房间前会自动取消发布和订阅所有流。
         * @returns Promise，在成功离开房间后 resolve。
         */
        leave(): Promise<void>;

        /**
         * 创建本地音视频流。
         * 创建后需要调用 `initialize()` 进行初始化，然后才能 `publish()` 或 `play()`。
         * @param config 创建流所需的配置，例如指定采集音视频、用户 ID 等。
         * @returns Promise，resolve 本地创建的 RtcStream 实例。
         */
        createStream(config: StreamConfig): Promise<RtcStream>;

        /**
         * 将本地创建的流发布到当前房间中，让其他用户可以订阅。
         * 必须在 `join` 成功后，且本地流 `initialize` 成功后调用。
         * @param stream 要发布的本地 RtcStream 实例。
         * @returns Promise，在发布成功后 resolve。
         */
        publish(stream: RtcStream): Promise<void>;

        /**
         * 取消发布指定的本地流。
         * 其他用户将无法再订阅这个流。
         * @param stream 要取消发布的本地 RtcStream 实例。
         * @returns Promise，在取消发布成功后 resolve。
         */
        unpublish(stream: RtcStream): Promise<void>;

        /**
         * 订阅指定的远端流。
         * 通常在监听到 'stream-added' 事件后调用此方法。
         * @param stream 'stream-added' 事件中获取到的远端 RtcStream 实例。
         * @param config 可选的订阅配置，例如只订阅音频或视频。
         * @returns Promise，在订阅成功后 resolve (此时可以调用 stream.play() 进行播放)。
         */
        subscribe(stream: RtcStream, config?: SubscribeConfig): Promise<void>;

        /**
         * 取消订阅指定的远端流。
         * @param stream 要取消订阅的远端 RtcStream 实例。
         * @returns Promise，在取消订阅成功后 resolve。
         */
        unsubscribe(stream: RtcStream): Promise<void>;

        /**
         * 获取当前客户端已订阅的所有远端流列表。
         * @returns 返回 RtcStream 实例的数组。
         */
        getRemoteStreams(): RtcStream[];

        /**
         * SDK 内部定义的常量集合，通常用于事件名、角色名等。
         * 使用 `readonly` 确保这些常量值不被意外修改。
         */
        readonly TYPE: {
            /** 事件名称常量 */
            readonly EVENT: {
                readonly ERROR: 'error';
                readonly CONNECTION_STATE_CHANGED: 'connection-state-changed';
                readonly STREAM_ADDED: 'stream-added';
                readonly STREAM_REMOVED: 'stream-removed';
                readonly STREAM_UPDATED: 'stream-updated';
                readonly STREAM_SUBSCRIBED: 'stream-subscribed';
                readonly PEER_JOIN: 'peer-join';
                readonly PEER_LEAVE: 'peer-leave';
                /** 注意: 'player-state-changed' 和 'screen-sharing-stopped' 是流事件 (Stream Event) */
                readonly PLAYER_STATE_CHANGED: 'player-state-changed';
                readonly SCREEN_SHARING_STOPPED: 'screen-sharing-stopped';
            };
            // 如果 SDK 还定义了其他类型的常量 (例如角色 ROLE, 模式 MODE)，可在此处补充。
        };

        /**
         * (推断) 订阅指定用户的远端流。
         * 这看起来像一个非标准的方法，可能是项目中的自定义辅助函数。
         * 如果确认是 SDK 标准方法，请取消注释并完善文档。
         * @param params 包含要订阅的用户 userId 的对象。
         */
        subscribeUserRemoteStream(params: { userId: string }): void;

        /** (新增) 内部等待队列，类型待确认 */
        awaitQueue_?: any; // t.AwaitQueue
        /** (新增) 取消流信息，具体功能和类型待确认 */
        cancelStreamInfo?: (...args: any[]) => any;
        /** (新增) SDK 配置项 */
        config?: {
            logConfig?: any;
            media?: any;
            eagleEye?: any;
            matrix?: any;
            transport?: any;
            [key: string]: any;
        };
        /** (新增) 内部事件发射器，类型待确认 */
        emitter_?: any; // e {emitter: {…}}
        /** (新增) 事件起始信息，类型待确认 */
        eventStartInfo?: Map<string, any>;
        /** (新增) 获取代码，具体功能和类型待确认 */
        getCode?: (...args: any[]) => any;
        /** (新增) 获取本地音频统计数据 */
        getLocalAudioStats?: (...args: any[]) => any; // 根据实际返回值确定类型
        /** (新增) 获取本地流 */
        getLocalStream?: (...args: any[]) => RtcStream | null; // 假设返回 RtcStream 或 null
        /** (新增) 获取本地视频统计数据 */
        getLocalVideoStats?: (...args: any[]) => any;
        /** (新增) 获取媒体级别，具体功能和类型待确认 */
        getMediaLevel?: (...args: any[]) => any;
        /** (新增) 获取远端音频统计数据 */
        getRemoteAudioStats?: (...args: any[]) => any;
        /** (新增) 获取远端成员信息 */
        getRemoteMember?: (userId: string) => any; // 根据实际返回值确定类型
        /** (新增) 获取所有远端成员信息 (注意与 getRemoteUsers 的区别或联系) */
        getRemoteMembers?: (...args: any[]) => any[]; // 根据实际返回值确定类型
        /** (新增) 获取远端流静音状态 */
        getRemoteMutedState?: (userId: string, mediaType: 'audio' | 'video') => boolean; // 推断参数和返回值
        /** (新增) 获取远端视频统计数据 */
        getRemoteVideoStats?: (...args: any[]) => any;
        /** (新增) 获取当前用户ID (注意: 打印输出中有 userId 属性，此为方法) */
        getUserId?: () => string;
        /** (新增) 初始化 Mediasoup 设备，具体功能和类型待确认 */
        initMediasoupDevice?: (...args: any[]) => Promise<any>; // 可能返回 Promise
        /** (新增) 初始化发布资源，具体功能和类型待确认 */
        initPublishResource?: (stream: RtcStream) => Promise<any>; // 推断参数和返回值
        /** (新增) Mediasoup 设备实例，类型待确认 */
        mediasoupDevice?: any; // t.Device
        /** (新增) 静音流 (本地或远端) */
        muteStream?: (stream: RtcStream | { userId: string; type: 'audio' | 'video' }, mediaType: 'audio' | 'video') => void; // 参数类型待细化
        /** (新增) 发送 EagleEye 消息，具体功能和类型待确认 */
        sendEagleEyeMsg?: (message: any) => void;
        /** (新增) 发送流信息，具体功能和类型待确认 */
        sendStreamInfo?: (...args: any[]) => void;
        /** (新增) 发送传输通道，类型待确认，初始可能为 null */
        sendTransport?: any | null;
        /** (新增) 设置当前说话者 */
        setCurrentSpeaker?: (userId: string) => void;
        /**
         * (注意) 设置 RTC 服务的后台地址。
         * 此方法在 NEBULARTCInstance 上也存在，此处可能是快捷方式或内部使用。
         * @param url 环境 URL。
         */
        setEnv?: (url: string) => void;
        /** (新增) 设置事件信息，具体功能和类型待确认 */
        setEventInfo?: (info: any) => void;
        /** (新增) 当前说话者ID */
        speakerId?: string;
        /** (新增) 流测速信息，具体功能和类型待确认 */
        speedTestStreamInfo?: (config: any) => void;
        /** (新增) 订阅测速，具体功能和类型待确认 */
        speedTestSubscribe?: (...args: any[]) => Promise<any>;
        /** (新增) 切换用户角色 */
        switchRole?: (newRole: JoinConfig['role']) => Promise<void>; // 推断参数和返回值
        /** (新增) 系统信息 */
        systemInfo?: {
            browser?: any;
            engine?: any;
            os?: any;
            platform?: any;
            [key: string]: any;
        };
        /** (新增) 取消静音流 (本地或远端) */
        unmuteStream?: (stream: RtcStream | { userId: string; type: 'audio' | 'video' }, mediaType: 'audio' | 'video') => void; // 参数类型待细化
    }

    /**
     * NEBULARTC SDK 的主入口实例接口。
     * 通过全局函数 `NEBULARTC()` 获取。
     */
    interface NEBULARTCInstance {
        /**
         * 设置 RTC 服务的后台地址。
         * 例如信令服务器地址。
         * @param url 环境 URL。
         */
        setEnv(url: string): void;

        /**
         * SDK 的日志记录器对象。
         */
        Logger: {
            /** 日志级别常量：DEBUG */
            readonly DEBUG: LogLevel;
            /** 日志级别常量：INFO */
            readonly INFO: LogLevel;
            /** 日志级别常量：WARN */
            readonly WARN: LogLevel;
            /** 日志级别常量：ERROR */
            readonly ERROR: LogLevel;
            /** 日志级别常量：OFF (关闭) */
            readonly OFF: LogLevel;
            /**
             * 设置 SDK 的日志输出级别。
             * @param level 要设置的日志级别，使用 LogLevel 枚举值。
             */
            setLogLevel(level: LogLevel): void;
        };

        /**
         * 创建一个 RTC 客户端 (RtcClient) 实例。
         * @param config 创建客户端所需的配置信息。
         * @returns 返回创建的 RtcClient 实例。
         */
        createClient(config: ClientConfig): RtcClient;

        // SDK 主入口可能还包含其他静态方法，例如：
        // getDevices(): Promise<MediaDeviceInfo[]> - 获取可用的媒体设备 (摄像头、麦克风)
        // checkSystemRequirements(): Promise<boolean> - 检查当前环境是否满足 SDK 要求
        // 请根据实际 SDK 文档补充。
    }
}

// 声明全局的 NEBULARTC 函数
declare global {
    // cspell:ignore NEBULARTC
    /**
     * 全局函数，用于获取 NEBULARTC SDK 的主入口实例。
     * @returns NEBULARTC SDK 实例 (NEBULA.NEBULARTCInstance)
     */
    function NEBULARTC(): NEBULA.NEBULARTCInstance;
}

// 导出命名空间，以便在模块化项目中使用 `import { NEBULA } from '...';`
export { NEBULA };
