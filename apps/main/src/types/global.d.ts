import type API from './services/api';

export interface ResponseData<T = any> {
    code: number;
    success: boolean;
    message: any;
    data: null | T;
    pagination: any;
}

type RecursiveObject<T> = T extends string ? never : T extends object ? T : never;
type APIValues<TModel> = {
    [Key in keyof TModel]: TModel[Key] extends RecursiveObject<TModel[Key]> ? APIValues<TModel[Key]> : <T = any, U = any>(param?: T, options?: any) => Promise<U>;
};

export {};

declare global {
    interface Window {
        __MICRO_APP_BASE_APPLICATION__: boolean;
    }

    const Invoke: APIValues<typeof API>;
}
