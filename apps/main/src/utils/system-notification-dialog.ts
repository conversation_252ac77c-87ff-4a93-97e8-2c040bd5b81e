import { Dialog, Toast } from '@boss/design';
import { jumpLogin } from './jump-url';
import { ref } from 'vue';
import { HELP_PHONE } from '@crm/exam-constants';

export let openTopNotification = ref(false);
let DialogModal: any = null;

function onConfirm() {
    DialogModal = null;
    openTopNotification.value = false;
}

function initDialog(config: any) {
    if (openTopNotification.value) {
        return;
    }

    DialogModal = null;

    DialogModal = Dialog.open({
        showClose: false,
        layerClosable: false,
        unmountOnClose: true,
        showCancel: false,
        confirm: () => {
            onConfirm();
        },
        ...config,
    });

    return DialogModal;
}

// 用户被动踢出登录
export function userKickLoginOutDialog() {
    if (DialogModal && !openTopNotification.value) {
        DialogModal.close();
    }

    // 确保优先级，先清除历史系统弹窗，在触发
    setTimeout(() => {
        initDialog({
            title: '您已被移出本场考试',
            content: `即将退出登录，如有疑问请联系${HELP_PHONE}`,
            width: '420px',
            wrapClass: 'user-kick-login-out-dialog-wrap',
            confirmText: '知道了',
            confirm: () => {
                jumpLogin();
                onConfirm();
            },
        });
        openTopNotification.value = true;
    }, 500);
}

// 用户切换屏幕提示弹窗
export function userCutScreenDialog(config: any, force: boolean = false) {
    // 如果 force 为 true，先清理现有的对话框
    if (force) {
        console.log('强制创建对话框，清理现有状态');
        if (DialogModal) {
            try {
                DialogModal.close();
            } catch (error) {
                console.error('清理现有对话框失败:', error);
            }
        }
        DialogModal = null;
        openTopNotification.value = false;
    }

    if (openTopNotification.value || DialogModal) {
        console.log('已有对话框存在，返回 null');
        return null;
    }

    return initDialog({
        title: '切换屏幕',
        content: '检测到您刚刚有切换屏幕操作，切换超过一定次数会自动交卷，作答期间请勿离开答题页面！',
        type: 'warning',
        showCancel: false,
        showClose: false,
        layerClosable: false,
        enableEscClose: false,
        confirmText: '知道了',
        ...config,
        close() {
            onConfirm();
            config.close && config.close();
        },
    });
}

export function userForceRefresh() {
    if (openTopNotification.value) {
        return;
    }

    openTopNotification.value = true;

    Toast.warning({
        content: '网络异常，即将刷新页面',
        duration: 3000,
        onClose: () => {
            window.location.reload();
        },
    });
}
