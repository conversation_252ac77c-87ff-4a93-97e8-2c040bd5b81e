import { DEPLOY_ENV } from '@/shared';

const getHost = () => {
    let host = 'zhice-h5.zhipin.com';
    switch (DEPLOY_ENV) {
        case 'pre':
            host = 'pre-zhice-h5.zhipin.com';
            break;
        case 'prod':
            host = 'zhice-h5.zhipin.com';
            break;
        default:
            host = 'zhice-h5-qa.weizhipin.com';
            break;
    }

    return host;
};
// 获取人脸认证H5设备地址
export const getH5FaceCheckUrl = async ({ examId }: { examId: string }) => {
    let url = '';
    const res = await Invoke.exam.getH5Code({ encExamId: examId, source: 2 });

    const host = getHost();
    const { protocol } = window.location;
    if (res.code === 0 && res.data) {
        url = `${protocol}//${host}/h5/face-check/index?encryptExamId=${examId}&code=${res.data}`;
    }

    return url;
};

// 获取第二视角H5扫码地址
export const getH5ExamMonitor = async ({ examId, encryptUserId }: { examId: string; encryptUserId: string }) => {
    let url = '';
    const host = getHost();
    const res = await Invoke.exam.getH5Code({ encExamId: examId, source: 1 });
    const { protocol } = window.location;

    if (examId && encryptUserId) {
        url = `${protocol}//${host}/h5/exam/exam-monitor?encryptExamId=${examId}&code=${res.data}`;
    }

    return url;
};
