import { DEPLOY_ENV } from '@/shared';
import { Storage } from '@/utils/storage';

interface CustomReportDataReturn {
    code: number;
    message: string;
}

async function send(data: any, className: string, subClass: string): Promise<CustomReportDataReturn> {
    if (DEPLOY_ENV === 'dev') {
        return {
            code: 0,
            message: 'APM data sent successfully',
        };
    }

    try {
        if (!(window as any).customReportData) {
            return {
                code: 500,
                message: 'APM customReportData not initialized',
            };
        }

        (await (window as any).customReportData(data, className, subClass)) as CustomReportDataReturn | undefined;

        return {
            code: 0,
            message: 'APM data upload attempted',
        };
    } catch (error) {
        logger.warn('APM upload attempt failed but marking as attempted:', error);
        return {
            code: 0,
            message: 'APM data upload attempted (with error)',
        };
    }
}

export function clearPMcustomUid() {
    Storage.del('APM-customUid', null);
}

export function getPMcustomUid() {
    return Storage.get('APM-customUid');
}

export async function setAPMcustomUid() {
    try {
        const apmID = getPMcustomUid();
        if (apmID) {
            if ((window as any).APM) {
                (window as any).APM.setCustomUid(apmID);
            }
        } else {
            if (!window.location.href.includes('preview')) {
                const { code, data } = await Invoke.common.getInfo({}, { noErrorToast: true });
                if (code === 0 && data.apm) {
                    if ((window as any).APM) {
                        (window as any).APM.setCustomUid(data.apm);
                    }

                    // 将APM用户标识存入回话内，方便PC测评取值
                    Storage.set('APM-customUid', data.apm);
                }
            }
        }
    } catch (error) {
        logger.error('Failed to set APM custom uid:', error);
    }
}

// 自定义业务流程埋点
export async function apmSendCustom(data: any) {
    return send(data, 'custom-report', 'business-points');
}

// 自定义网络请求监控
// code-error --状态200 业务code异常
// status-error --状态非200
// internal-error -- axios响应错误回调内触发
export async function apmSendAxiosError(data: any, className: 'code-error' | 'status-error' | 'internal-error') {
    return send(data, 'axios-error', className);
}

// 路由错误监控
// page-404 -- 页面404
export async function apmSendRouteErr(data: any, subClass: string = 'page-404') {
    return send(data, 'route-error', subClass);
}
