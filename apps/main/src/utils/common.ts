import { Router } from 'vue-router';

export function preloadRoutes(router: Router) {
    /**
     * Preloads route components during idle time.
     *
     * This function filters the routes to find those with components and then
     * uses `requestIdleCallback` to preload these components when the browser is idle.
     * It specifically handles routes with multiple view components.
     *
     * @remarks
     * The function assumes that route components are functions and invokes them
     * to trigger the preload.
     */
    function preloadRoutes() {
        const routesToPreload = router.getRoutes().filter((route) => route.components);

        routesToPreload.forEach((route) => {
            requestIdleCallback(() => {
                if (route.components) {
                    // 预加载多视图路由组件
                    Object.values(route.components)
                        .filter((comp) => typeof comp === 'function')
                        // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
                        .forEach((comp) => (comp as Function)());
                }
            });
        });
    }

    // 在浏览器空闲时执行路由预加载
    if ('requestIdleCallback' in window) {
        requestIdleCallback(preloadRoutes);
    }
}
