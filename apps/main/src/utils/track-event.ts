import { DATASTAR_SERVER_URL_DEV, DATASTAR_SERVER_URL_PROD, DATASTAR_SERVER_URL_TOKEN } from '@crm/exam-constants';
import Warlock from '@datastar/warlock-jssdk';
import { getBowser } from '@crm/exam-utils';
import { useDebuggingStore } from '@/views/monitor/store';

// const isProd = true

export function BossAnalyticsInit({ isProd }: { isProd: boolean }) {
    if (import.meta.env.VITE_DEPLOY_ENV === 'dev') {
        return;
    }
    Warlock.init({
        server_url: isProd ? DATASTAR_SERVER_URL_PROD : DATASTAR_SERVER_URL_DEV,
        token: DATASTAR_SERVER_URL_TOKEN,
        app_ver: '1.0.0',
        app_name: 'kaoping',
        auto_track_page_view: true,
        auto_track_web_click: false,
        auto_track_ajax: false,
        page_title: true,
        element_selector: true,
        env: isProd ? 'prod' : 'dev',
    });
    BossAnalyticsExtendTrackProps({
        Browser: getBowser(),
    });
    Warlock.quick('autoTrackPageView');
}

export function BossAnalyticsTrack(eventName: string, data?: Record<string, any>) {
    if (import.meta.env.VITE_DEPLOY_ENV === 'dev') {
        return;
    }
    const debuggingStore = useDebuggingStore();
    try {
        const params: Record<string, any> = {
            ...(data || {}),
            camera: debuggingStore.STATUS.camera.status,
            phone: debuggingStore.STATUS.phone.status,
            screen: debuggingStore.STATUS.screen.status,
        };
        if (params.pData) {
            params.pData = JSON.stringify(params.pData);
        }
        Warlock.track(eventName, params);
    } catch (error) {
        logger.error(error);
    }
}

export function BossAnalyticsLogin(userId: string) {
    Warlock.login(userId);
}

// 拓展公共属性
export function BossAnalyticsExtendTrackProps(data: Record<string, any>) {
    Warlock.extendTrackProps(data);
}

// 前端自定义埋点使用到如下枚举参数
export enum TrackTypeEnum {
    成功 = 'success',
    失败 = 'fail',
    警告 = 'warning',
}
export enum ReportTypeEnum {
    登录 = 'login',
    ws = 'ws',
    摄像头 = 'camera',
    屏幕共享 = 'screen',
    手机 = 'phone',
}
