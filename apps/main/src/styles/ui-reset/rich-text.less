// 富文本样式
.lext-theme-ltr {
    text-align: left;
}
.lext-theme-rtl {
    text-align: right;
}
.lext-theme-paragraph {
    font-size: 13px;
    line-height: 26px;
    color: #333333;
    font-weight: normal;
    text-align: left;
}
.lext-theme-quote {
    font-size: 12px;
    line-height: 24px;
    color: #a5a5a5;
    font-weight: normal;
    text-align: left;
}
.lext-theme-h1 {
    font-size: 12px;
    line-height: 24px;
    color: #000000;
    font-weight: normal;
    text-align: left;
}
.lext-theme-h2 {
    font-size: 18px;
    line-height: 32px;
    color: #000000;
    font-weight: normal;
    text-align: left;
}
.lext-theme-h3 {
    font-size: 15px;
    line-height: 32px;
    color: #000000;
    text-align: left;
    font-weight: normal;
}
.lext-theme-h6 {
    font-size: 14px;
    line-height: 26px;
    color: #333;
    font-weight: normal;
    text-align: left;
}
.lext-theme-indent {
    --lexical-indent-base-value: 40px;
}
.lext-theme-textBold {
    font-weight: bold;
}
.lext-theme-textItalic {
    font-style: italic;
}
.lext-theme-textUnderline {
    text-decoration: underline;
}
.lext-theme-textStrikethrough {
    text-decoration: line-through;
}
.lext-theme-textUnderlineStrikethrough {
    text-decoration: underline line-through;
}
.lext-theme-textSubscript {
    font-size: 0.8em;
    vertical-align: sub !important;
}
.lext-theme-textSuperscript {
    font-size: 0.8em;
    vertical-align: super;
}
.lext-theme-textCode {
    background-color: rgb(240, 242, 245);
    padding: 1px 0.25rem;
    font-family: Menlo, Consolas, Monaco, monospace;
    font-size: 94%;
}
.lext-theme-hashtag {
    background-color: rgba(88, 144, 255, 0.15);
    border-bottom: 1px solid rgba(88, 144, 255, 0.3);
}
.lext-theme-link {
    color: rgb(33, 111, 219);
    text-decoration: none;
}
.lext-theme-link:hover {
    text-decoration: underline;
    cursor: pointer;
}
.lext-theme-code {
    background-color: rgb(240, 242, 245);
    font-family: Menlo, Consolas, Monaco, monospace;
    display: block;
    padding: 8px 8px 8px 52px;
    line-height: 1.53;
    font-size: 13px;
    margin: 0;
    margin-top: 8px;
    margin-bottom: 8px;
    overflow-x: auto;
    position: relative;
    tab-size: 2;
}
.lext-theme-code:before {
    content: attr(data-gutter);
    position: absolute;
    background-color: #eee;
    left: 0;
    top: 0;
    border-right: 1px solid #ccc;
    padding: 8px;
    color: #777;
    white-space: pre-wrap;
    text-align: right;
    min-width: 25px;
}
.lext-theme-table {
    border-collapse: collapse;
    border-spacing: 0;
    overflow-y: scroll;
    overflow-x: scroll;
    table-layout: fixed;
    width: max-content;
    margin: 30px 0;
}
.lext-theme-tableSelection *::selection {
    background-color: transparent;
}
.lext-theme-tableSelected {
    outline: 2px solid rgb(60, 132, 244);
}
.lext-theme-tableCell {
    border: 1px solid #bbb;
    width: 75px;
    min-width: 75px;
    vertical-align: top;
    text-align: start;
    padding: 6px 8px;
    position: relative;
    outline: none;
}
.lext-theme-tableCellSortedIndicator {
    display: block;
    opacity: 0.5;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background-color: #999;
}
.lext-theme-tableCellResizer {
    position: absolute;
    right: -4px;
    height: 100%;
    width: 8px;
    cursor: ew-resize;
    z-index: 10;
    top: 0;
}
.lext-theme-tableCellHeader {
    background-color: #f2f3f5;
    text-align: start;
}
.lext-theme-tableCellSelected {
    background-color: #c9dbf0;
}
.lext-theme-tableCellPrimarySelected {
    border: 2px solid rgb(60, 132, 244);
    display: block;
    height: calc(100% - 2px);
    position: absolute;
    width: calc(100% - 2px);
    left: -1px;
    top: -1px;
    z-index: 2;
}
.lext-theme-tableCellEditing {
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.4);
    border-radius: 3px;
}
.lext-theme-tableAddColumns {
    position: absolute;
    top: 0;
    width: 20px;
    background-color: #eee;
    height: 100%;
    right: -25px;
    animation: table-controls 0.2s ease;
    border: 0;
    cursor: pointer;
}
.lext-theme-tableAddColumns:after {
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    display: block;
    content: ' ';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.4;
}
.lext-theme-tableAddColumns:hover {
    background-color: #c9dbf0;
}
.lext-theme-tableAddRows {
    position: absolute;
    bottom: -25px;
    width: calc(100% - 25px);
    background-color: #eee;
    height: 20px;
    left: 0;
    animation: table-controls 0.2s ease;
    border: 0;
    cursor: pointer;
}
.lext-theme-tableAddRows:after {
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    display: block;
    content: ' ';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.4;
}
.lext-theme-tableAddRows:hover {
    background-color: #c9dbf0;
}
@keyframes table-controls {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}
.lext-theme-tableCellResizeRuler {
    display: block;
    position: absolute;
    width: 1px;
    background-color: rgb(60, 132, 244);
    height: 100%;
    top: 0;
}
.lext-theme-tableCellActionButtonContainer {
    display: block;
    right: 5px;
    top: 6px;
    position: absolute;
    z-index: 4;
    width: 20px;
    height: 20px;
}
.lext-theme-tableCellActionButton {
    background-color: #eee;
    display: block;
    border: 0;
    border-radius: 20px;
    width: 20px;
    height: 20px;
    color: #222;
    cursor: pointer;
}
.lext-theme-tableCellActionButton:hover {
    background-color: #ddd;
}
.lext-theme-characterLimit {
    display: inline;
    background-color: #ffbbbb !important;
}
.lext-theme-ol1 {
    padding: 0;
    margin: 0;
    list-style-position: inside;
}
.lext-theme-ol2 {
    padding: 0;
    margin: 0;
    list-style-type: upper-alpha;
    list-style-position: inside;
}
.lext-theme-ol3 {
    padding: 0;
    margin: 0;
    list-style-type: lower-alpha;
    list-style-position: inside;
}
.lext-theme-ol4 {
    padding: 0;
    margin: 0;
    list-style-type: upper-roman;
    list-style-position: inside;
}
.lext-theme-ol5 {
    padding: 0;
    margin: 0;
    list-style-type: lower-roman;
    list-style-position: inside;
}
.lext-theme-ul {
    padding: 0;
    margin: 0;
    list-style-position: inside;
}
.lext-theme-listItem {
    font-size: 13px;
    line-height: 26px;
    color: #333333;
    font-weight: normal;
    list-style: disc;
    margin-left: 26px;
    text-align: left;
}
.lext-theme-listItemChecked,
.lext-theme-listItemUnchecked {
    position: relative;
    margin-left: 8px;
    margin-right: 8px;
    padding-left: 24px;
    padding-right: 24px;
    list-style-type: none;
    outline: none;
}
.lext-theme-listItemChecked {
    text-decoration: line-through;
}
.lext-theme-listItemUnchecked:before,
.lext-theme-listItemChecked:before {
    content: '';
    width: 16px;
    height: 16px;
    top: 2px;
    left: 0;
    cursor: pointer;
    display: block;
    background-size: cover;
    position: absolute;
}
.lext-theme-listItemUnchecked[dir='rtl']:before,
.lext-theme-listItemChecked[dir='rtl']:before {
    left: auto;
    right: 0;
}
.lext-theme-listItemUnchecked:focus:before,
.lext-theme-listItemChecked:focus:before {
    box-shadow: 0 0 0 2px #a6cdfe;
    border-radius: 2px;
}
.lext-theme-listItemUnchecked:before {
    border: 1px solid #999;
    border-radius: 2px;
}
.lext-theme-listItemChecked:before {
    border: 1px solid rgb(61, 135, 245);
    border-radius: 2px;
    background-color: #3d87f5;
    background-repeat: no-repeat;
}
.lext-theme-listItemChecked:after {
    content: '';
    cursor: pointer;
    border-color: #fff;
    border-style: solid;
    position: absolute;
    display: block;
    top: 6px;
    width: 3px;
    left: 7px;
    right: 7px;
    height: 6px;
    transform: rotate(45deg);
    border-width: 0 2px 2px 0;
}
.lext-theme-nestedListItem {
    list-style-type: none;
}
.lext-theme-nestedListItem:before,
.lext-theme-nestedListItem:after {
    display: none;
}
.lext-theme-tokenComment {
    color: slategray;
}
.lext-theme-tokenPunctuation {
    color: #999;
}
.lext-theme-tokenProperty {
    color: #905;
}
.lext-theme-tokenSelector {
    color: #690;
}
.lext-theme-tokenOperator {
    color: #9a6e3a;
}
.lext-theme-tokenAttr {
    color: #07a;
}
.lext-theme-tokenVariable {
    color: #e90;
}
.lext-theme-tokenFunction {
    color: #dd4a68;
}
.lext-theme-mark {
    background: rgba(255, 212, 0, 0.14);
    border-bottom: 2px solid rgba(255, 212, 0, 0.3);
    padding-bottom: 2px;
}
.lext-theme-markOverlap {
    background: rgba(255, 212, 0, 0.3);
    border-bottom: 2px solid rgba(255, 212, 0, 0.7);
}
.lext-theme-mark.selected {
    background: rgba(255, 212, 0, 0.5);
    border-bottom: 2px solid rgba(255, 212, 0, 1);
}
.lext-theme-markOverlap.selected {
    background: rgba(255, 212, 0, 0.7);
    border-bottom: 2px solid rgba(255, 212, 0, 0.7);
}
.lext-theme-embedBlock {
    user-select: none;
    padding-top: 6px;
    padding-bottom: 0px;
}
.lext-theme-embedBlockFocus {
    /* background-color: #eee; */
}
.lext-theme-layoutContainer {
    display: grid;
    gap: 10px;
    margin: 10px 0;
}
.lext-theme-layoutItem {
    border: 1px dashed #ddd;
    padding: 8px 16px;
}
img[format=center] {
    display: block;
    margin: 0 auto;
}
img[format=right] {
    float: right;

    & + * {
        clear: both;
    }
}
