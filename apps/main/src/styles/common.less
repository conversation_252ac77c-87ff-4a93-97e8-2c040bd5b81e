.svg-icon {
    width: 24px;
    height: 24px;
}
.op-action {
    display: inline-flex;
    align-items: center;
    padding: 2px 5px;
    line-height: 20px;
    color: #808080;
    vertical-align: middle;
    border-radius: 4px;
    cursor: pointer;
    svg {
        // margin-top: 2px;
        margin-right: 2px;
    }
    &:hover {
        background: #f2f2f2;
        color: #4d4d4d;
    }
}

// 旋转animate rotate动画
@keyframes keyframes-rotate {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.animate_rotate {
    animation: keyframes-rotate 3s linear 0s infinite both;
}
