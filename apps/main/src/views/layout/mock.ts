export const baseInfo = {
    code: 0,
    message: 'success',
    data: {
        // 考生加密id
        encryptUserId: 'xxxx~~',
        // 考生姓名
        userName: 'xxx',
        mobile: 'xxx',
        // 大场次信息
        seqInfo: {
            // 大场次加密Id
            seqId: 'xxx~~',
            // 大场次名称
            seqName: 'xxx',
            // 大场次开始时间
            seqStartTime: '2023-01-01 14:00:00',
            // 大场次结束时间
            seqEndTime: '2023-01-02 14:00:00',
            // 大场次作答说明
            seqRemarks: '大场次作答说明',
            // 是否考前准备
            canDebug: false,
            wsConnectSecret: '', // ws连接密码
        },
        // 小考试信息
        examInfo: {
            examType: 1, // 1-考试 2-量表 3-GBA 4-hots
            examId: 'abadsfa', // 加密小考试id
            examName: 'aklajds', // 小考试名称
            examRemarks: '小考试答题说明', // 小考试答题说明
            operateGuide: '<h1>富文本</h1>', // 小考试（测评独有）操作指引：富文本
            interruptDuration: 6000, // 小考试（测评独有）试卷答题过程中 用户中断时长
            questionListType: 1, // 小考试（测评独有）答题列表类型

            sort: 1, // 排序 从1开始
            answerMode: 1, // 作答模式 1定时模式 2即时模式
            examStartTime: '2023-01-01 14:00:00', // 开始作答时间
            examEndTime: '2023-01-02 14:00:00', // 结束作答时间,
            questionCount: 123, // 题目数量
            // 考试状态：0-待启用；1-待开始；2-考试中；3-已结束
            status: 1,
            // 考试状态描述
            statusDesc: '考试中',
            // 是否已交卷:true-已交卷
            hasCommitPaper: false,
            // 考试结束剩余秒数
            remainSeconds: 6000,
            adviseDurationStr: '90分钟',
            hasInvigilator: true, // 是否有监考官
            openCountDown: true, // （考试独有）开启倒计时
        },
    },
};
export const config = {
    code: 0,
    message: 'success',
    data: {
        // 能否调试
        canDebug: true,
        recognizeFaceConfirm: true, // 是否人脸验证服务确认
        // 启用人身核验：true-启用；false-不启用
        personalVerification: true,
        // 人脸识别失败次数
        recognizeFaceFailedCount: 0,
        // 系统人脸识别结果：0-未进行；1-成功;2-失败；3-审核中
        systemRecognizeResult: 1,
        // 人脸认证时的截图文件
        recognizeFile: {
            name: '文件名',
            type: '文件类型',
            size: 123, // 文件大小 单位字节
            url: 'http://xxx', // 文件url
            uri: '/path/xxx', // 文件相对路径
        },

        // 启用电脑摄像头监控：true-启用；false-不启用
        computerCameraMonitor: true,
        // 启用手机第二视角监控：true-启用；false-不启用
        mobilePerspectiveMonitor: true,
        // 启用电脑屏幕监控：true-启用；false-不启用
        computerScreenMonitor: true,
        // 考生信息是否可修改：true-可修改；false-不可修改
        examineeInfoCanModify: true,
        examineeInfoList: [],
        // 是否开启沟通入口：true-开启；false-不开启
        openCommunicateInlet: true,
        // 监控房间id
        monitorRoomId: 'xxx',

        // ！！！以下字段其实跟/wapi/web/exam/getConfig.json接口完全一致
        cameraMonitoringRule: {
            // 1 设置开启替考检测
            substituteExam: 1,
            // 替考检测限制
            substituteExamLimit: 3,
            // 替考检测限制时间窗口
            substituteExamLimitTimeWindow: 3,
            // 当前替考检测限制次数
            substituteExamCurrentCount: 1,
            // 是否确认过替考检测限制弹窗提醒
            substituteExamWarningConfirm: true,

            // 1 设置开启多人脸检测
            multipleFaces: 1,
            // 多人脸检测限制
            multipleFacesLimit: 3,
            // 多人脸检测限制时间窗口
            multipleFacesLimitTimeWindow: 3,
            // 当前多人脸检测限制次数
            multipleFacesCurrentCount: 1,
            // 是否确认过多人脸检测限制弹窗提醒
            multipleFacesWarningConfirm: true,

            // 1 设置开启离开检测
            leaveSeat: 1,
            // 离开检测限制
            leaveSeatLimit: 3,
            // 离开检测限制时间窗口
            leaveSeatLimitTimeWindow: 3,
            // 当前离开检测限制次数
            leaveSeatCurrentCount: 1,
            // 是否确认过离开检测限制弹窗提醒
            leaveSeatWarningConfirm: true,

            // 1 设置开启低头检测
            lowerHead: 1,
            // 低头检测限制
            lowerHeadLimit: 3,
            // 低头检测限制时间窗口
            lowerHeadLimitTimeWindow: 3,
            // 当前低头检测限制次数
            lowerHeadCurrentCount: 1,
            // 是否确认过低头检测限制弹窗提醒
            lowerHeadWarningConfirm: true,

            // 1 设置开启左右张望检测
            lookAround: 1,
            // 左右张望检测限制
            lookAroundLimit: 3,
            // 左右张望检测限制时间窗口
            lookAroundLimitTimeWindow: 3,
            // 当前左右张望检测限制次数
            lookAroundCurrentCount: 1,
            // 是否确认过左右张望检测限制弹窗提醒
            lookAroundWarningConfirm: true,
        },

        switchScreenRule: {
            // 离开页面是否强制交卷：1-强制交卷;0-不强制交卷
            forceSubmitWhenLevePage: 1,
            // 最大切屏次数，超过强制交卷
            maxSwitchCount: 5,
            // 切屏警告：0-默认;1-设置
            switchScreenWarning: 1,
            // 当前登录切屏次数
            currentSwitchCount: 2,
            // 是否确认过弹窗提醒
            warningConfirm: false,
        },
        reLoginRule: {
            maxLoginCount: 2,
            // 超过最大登录次数是否强制交卷：1-强制交卷;0-不强制交卷
            forceSubmitWhenOverLimit: 1,
            // 重新登录警告：0-默认;1-设置
            reLoginWarning: 1,
            // 当前重复登录次数，第一次登录为0
            currentReLoginCount: 1,
            // 是否确认过弹窗提醒
            warningConfirm: true,
        },
    },
};
