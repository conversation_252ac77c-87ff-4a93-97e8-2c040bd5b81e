<template>
    <div class="monitor-status-wrap">
        <div v-if="hasComputer" v-show="false">
            <div v-if="computerMonitorStaus === 2" class="inner inner-success">
                <video ref="computerRef" :srcObject="cumputerLocalStream?.videoStream" autoplay />
            </div>
            <div v-else-if="computerMonitorStaus === 1" class="inner inner-error"></div>
        </div>
        <div v-if="hasScreen" v-show="false">
            <div v-if="screenMonitorStaus === 2" class="inner inner-success">
                <video ref="screenRef" :srcObject="screenLocalStream?.videoStream" autoplay />
            </div>
            <div v-else-if="screenMonitorStaus === 1" class="inner inner-error"></div>
        </div>
        <div v-if="hasComputer" class="monitor-status-item">
            <b-popover :showArrow="false" :popupOffset="4" :disabled="computerMonitorStaus === 0" contentClass="monitor-pop monitor-pop-screen">
                <div class="trigger" :class="getTrggerClassName(computerMonitorStaus)">
                    <SvgIcon name="svg-empty-camera" class="monitor-svg" width="20" height="20" />
                    <span class="status-text">{{ getStatusText(computerMonitorStaus) }}</span>
                </div>
                <template #content>
                    <div v-if="computerMonitorStaus === 2" class="inner inner-success">
                        <video :srcObject="cumputerLocalStream?.videoStream" autoplay />
                    </div>
                    <div v-else-if="computerMonitorStaus === 1" class="inner inner-error">
                        {{ computerErrorText }}
                    </div>
                </template>
            </b-popover>
        </div>
        <!-- 屏幕共享 -->
        <div v-if="hasScreen" class="monitor-status-item">
            <b-popover :showArrow="false" :popupOffset="4" :disabled="screenMonitorStaus === 0" contentClass="monitor-pop monitor-pop-screen">
                <div class="trigger" :class="getTrggerClassName(screenMonitorStaus)">
                    <SvgIcon name="svg-empty-screen" class="monitor-svg" width="20" height="20" />
                    <span class="status-text">{{ getStatusText(screenMonitorStaus) }}</span>
                </div>
                <template #content>
                    <div v-if="screenMonitorStaus === 2" class="inner inner-success">
                        <video :srcObject="screenLocalStream?.videoStream" autoplay />
                    </div>
                    <div v-else-if="screenMonitorStaus === 1" class="inner inner-error">
                        {{ screenErrorText }}
                    </div>
                </template>
            </b-popover>
        </div>
        <!-- 第三手机视角 -->
        <div v-if="hasPhone" class="monitor-status-item">
            <b-popover :showArrow="false" :popupOffset="4" contentClass="monitor-pop monitor-pop-phone" @popupVisibleChange="onPopupVisibleChange">
                <div class="trigger" :class="getTrggerClassName(phoneMonitorStaus)">
                    <SvgIcon name="svg-empty-mobile" class="monitor-svg" width="20" height="20" />
                    <span class="status-text">{{ getStatusText(phoneMonitorStaus) }}</span>
                </div>
                <template #content>
                    <div v-if="phoneMonitorStaus === 2" id="phoneRemoteStream" ref="phoneRemoteStreamRef" class="inner inner-success" />
                    <div v-else-if="phoneMonitorStaus === 0 || phoneMonitorStaus === 1" class="inner inner-error">
                        <H5ExamMonitorQrCode :width="104" :height="104" />
                        <span style="margin-top: 8px">请重新扫码</span>
                    </div>
                </template>
            </b-popover>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, nextTick, ref, toRefs } from 'vue';
import { useRoute } from 'vue-router';
import { useShot } from './useShot';
import H5ExamMonitorQrCode from '@/components/h5-exam-monitor-qr-code.vue';
import { useDebuggingStore } from '../../../monitor/store';

const debuggingStore = useDebuggingStore();
const { hasComputer, hasPhone, hasScreen } = toRefs(debuggingStore);

const $route = useRoute();
const encryptExamId = computed(() => ($route.params.examId || $route.query.examId || $route.query.seqId) as string);

function getTrggerClassName(status: number) {
    switch (status) {
        case 0:
            return 'default';
        case 1:
            return 'error';
        case 2:
            return 'success';
        default:
            return 'default';
    }
}

function getStatusText(status: number) {
    switch (status) {
        case 0:
            return '未开启';
        case 1:
            return '监控异常';
        case 2:
            return '正在监控';
        default:
            return '未开启';
    }
}

// 第一视角问题
const computerMonitorStaus = computed(() => debuggingStore.STATUS.camera.status);
const cumputerLocalStream = computed(() => debuggingStore.STATUS.camera.localStream);
const computerErrorText = computed(() => debuggingStore.STATUS.camera.errorText);
const computerRef = ref();
useShot(computerMonitorStaus, computerRef, encryptExamId, 1);

// 第二视角相关
const phoneRemoteStreamRef = ref();
const phoneMonitorStaus = computed(() => debuggingStore.STATUS.phone.status); // 0 未开启、1 失败、2 成功
const phoneRemoteStream = computed(() => debuggingStore.STATUS.phone.remoteStream);

function onPopupVisibleChange(flag: boolean) {
    // 播放视频
    nextTick(() => {
        if (flag && phoneRemoteStream.value && phoneMonitorStaus.value === 2 && phoneRemoteStreamRef.value) {
            try {
                phoneRemoteStream.value
                    .play('phoneRemoteStream', { muted: true })
                    .then(() => {})
                    .catch((error: any) => {
                        logger.error('第二视角视频流播放失败', error);
                    });
            } catch (error) {
                logger.error('调用play方法出错', error);
            }
        } else if (flag) {
            // 当视频流不存在但弹窗被打开时
            logger.info('弹窗被打开但视频流不可用', {
                hasStream: !!phoneRemoteStream.value,
                status: phoneMonitorStaus.value,
                hasRef: !!phoneRemoteStreamRef.value,
            });
        }
    });
}

// 第三视角相关
const screenMonitorStaus = computed(() => debuggingStore.STATUS.screen.status); // 0 未开启、1 失败、2 成功
const screenLocalStream = computed(() => debuggingStore.STATUS.screen.localStream);
const screenErrorText = computed(() => debuggingStore.STATUS.screen.errorText);
const screenRef = ref();
useShot(screenMonitorStaus, screenRef, encryptExamId, 3);
</script>

<style lang="less">
@import './index.less';
</style>
