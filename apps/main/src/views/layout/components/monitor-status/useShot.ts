import type { ComputedRef, Ref } from 'vue';
import { _addUserPhoto, _fileUpload, _postErrAction } from '@/services/apis/exam';
import { base64ToFile, checkCameraBlockage, getCurrentFrame } from '@crm/exam-utils';
import { nextTick, onBeforeUnmount, watch } from 'vue';
import { logger } from '@/utils/logger';

export function useShot(status: ComputedRef<number>, ele: Ref<any>, encryptExamId: ComputedRef<string>, dataSource: 1 | 3) {
    let timer: number;
    const step = 30000; // 30s轮询
    let hasBlockage = false; // 标记位，记录上次检测是否被遮挡
    let hasAddUserPhoto = false; // 标记位，记录是否上传过用户照片

    const shot = async () => {
        const element = ele.value;
        if (!element) {
            logger.warn('[useShot] Video element not available for blockage detection');
            return;
        }

        // 检查video元素是否已准备好
        if (!element.videoWidth || !element.videoHeight || element.readyState < 2) {
            logger.warn('[useShot] Video element not ready for blockage detection');
            return;
        }

        try {
            const base64String = getCurrentFrame(element);
            if (!base64String) {
                logger.warn('[useShot] Unable to get frame from video element');
                return;
            }

            // 30s判断一次摄像头是否被遮挡
            const isBlockage = await checkCameraBlockage(base64String);
            logger.info('[useShot] isBlockage', isBlockage, 'hasBlockage', hasBlockage);
            if ((isBlockage && !hasBlockage) || (!isBlockage && hasBlockage)) {
                logger.info(`[useShot] Camera blockage status changed: ${isBlockage ? 'blocked' : 'unblocked'}`);
                _postErrAction({
                    encryptExamId: encryptExamId.value,
                    errType: isBlockage ? 204 : 1,
                    source: 1,
                });
            }
            // 更新标记位
            hasBlockage = isBlockage as boolean;

            // 截一张有效图作为监控兜底帧
            if (!isBlockage && !hasAddUserPhoto) {
                const file = base64ToFile(base64String, `${Date.now()}`);
                const fd = new FormData();
                fd.append('file', file);
                fd.append('biz', 'USER_EXAM_MONITOR_VIDEO');
                // 每隔30s截图上传一次
                const { code, data } = await _fileUpload(fd);
                if (code === 0 && data && data.uri) {
                    _addUserPhoto({
                        fileId: data.encryptId,
                        fileName: data.name,
                        dataSource,
                    });
                    hasAddUserPhoto = true;
                }
            }
        } catch (error) {
            logger.error('[useShot] Error during shot processing:', error);
        }
    };

    const onLoadeddata = () => {
        logger.info('[useShot] Video loaded, starting blockage detection timer');
        timer = setInterval(shot, step) as any;
    };

    const clearFun = () => {
        if (ele.value) {
            ele.value.removeEventListener('loadeddata', onLoadeddata);
        }
        if (timer) {
            logger.info('[useShot] Clearing blockage detection timer');
            clearInterval(timer);
        }
    };

    watch(
        () => [status.value, ele.value],
        async ([newVal, newEle]) => {
            logger.info(`[useShot] Camera status changed to: ${newVal}`);
            // 只有在状态为2（监控成功）且元素存在时才启动检测
            // 避免在状态为1时启动检测（此时video元素不会渲染）
            if (newVal === 2 && newEle) {
                nextTick(() => {
                    logger.info('[useShot] ele.value', ele.value);
                    if (ele.value) {
                        ele.value.addEventListener('loadeddata', onLoadeddata);
                        // 如果video已经加载完成，立即开始检测
                        if (ele.value.readyState >= 2) {
                            onLoadeddata();
                        }
                    }
                });
            } else {
                clearFun();
            }
        },
    );

    onBeforeUnmount(() => {
        clearFun();
    });

    return {};
}
