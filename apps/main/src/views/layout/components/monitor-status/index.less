.monitor-status-wrap {
    display: flex;
    margin-right: 24px;
    .monitor-status-item {
        position: relative;
        .trigger {
            position: relative;
            display: flex;
            align-items: center;
            width: 104px;
            height: 40px;
            padding-left: 12px;
            border-radius: 8px;
            .status-text {
                display: block;
                line-height: 20px;
                color: #1f1f1f;
            }
            &.default {
                background: #f2f2f2;
                .status-text {
                    margin-left: 10px;
                }
            }
            &.success {
                background: #e5f6f6;
                cursor: pointer;
                &:hover {
                    background: #caebeb;
                }
                .status-text {
                    margin-left: 4px;
                }
            }
            &.error {
                background: #ffeae8;
                cursor: pointer;
                &:hover {
                    background: #fcc2bf;
                }
                .status-text {
                    margin-left: 4px;
                }
            }
        }
        & + .monitor-status-item {
            margin-left: 8px;
        }
    }
}

.monitor-pop-screen {
    width: 320px;
    border-radius: 12px;
}
.monitor-pop-phone {
    width: 120px;
    border-radius: 8px;
}
.monitor-pop.b-popover-popup-content {
    padding: 0;
    border: none;
    background: rgba(0, 0, 0, 0.6);
    box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    height: 180px;
    .b-popover-content {
        width: 100%;
        height: 100%;
    }
    .inner {
        width: 100%;
        height: 100%;
        &-success {
            video {
                display: block;
                margin: 0 auto;
                max-width: 100%;
                max-height: 100%;
            }
        }
        &-error {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            color: #fff;
            text-shadow: 2px 4px 8px rgba(0, 0, 0, 0.1);
        }
    }
}
