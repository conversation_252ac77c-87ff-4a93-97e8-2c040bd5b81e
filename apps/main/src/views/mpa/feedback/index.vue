<template>
    <!-- 确认参加 && 确认不参加 -->
    <div v-if="showNormal" class="feedback-container">
        <img :src="status === '1' ? ACCEPT_IMG : REJECT_IMG" width="200" height="200" alt="" />
        <p>{{ status === '1' ? ACCEPT_TEXT : REJECT_TEXT }}</p>
    </div>
    <!-- 请求中 && 请求异常 -->
    <b-pagetip v-else :status="pageStatus" :errorText="errorText" loadingType="dot" />
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';

const $route = useRoute();
const { query, params } = $route;
const { encryptId = '', encryptExamId = '' } = query;
const { status = '0' } = params; // 1 确认参加 2 确认不参加

const errorText = ref('');
const pageStatus = ref('loading') as any;

const ACCEPT_TEXT = '您已确认参加答题，请在规定时间点击邮件内链接进行作答！';
const REJECT_TEXT = '您已确认拒绝参加，感谢您的反馈。';
const ACCEPT_IMG = 'https://img.bosszhipin.com/static/zhipin/kanjian/zhice/kaoshi/feedback-accept.png';
const REJECT_IMG = 'https://img.bosszhipin.com/static/zhipin/kanjian/zhice/kaoshi/feedback-reject.png';

const showNormal = computed(() => {
    return (status === '1' || status === '2') && pageStatus.value === 'success';
});

// 反馈结果上报
async function examNoticeCallback() {
    await Invoke.feedback
        .postExamNoticeCallback(
            {
                status,
                encryptId,
                encryptExamId,
            },
            { noNeedLogin: true },
        )
        .then(({ code, message }: any) => {
            if (code === 0) {
                pageStatus.value = 'success';
            } else {
                pageStatus.value = 'error';
                errorText.value = message;
            }
        })
        .catch(() => {
            pageStatus.value = 'error';
            errorText.value = '系统异常，请刷新重试';
        });
}

examNoticeCallback();
</script>

<style lang="less" scoped>
.feedback-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    p {
        margin-top: 28px;
        font-size: 13px;
        color: #363f55;
    }
}
</style>
