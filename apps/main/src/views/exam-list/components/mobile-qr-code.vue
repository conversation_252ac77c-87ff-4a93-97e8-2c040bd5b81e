<template>
    <div class="url-qr-code-wrap">
        <UrlQrCode v-if="url" :width="200" :height="200" :url="url" :options="options" />
    </div>
</template>

<script setup lang="ts">
import UrlQrCode from '@/components/url-qr-code.vue';

defineOptions({
    name: 'MobileQrCode',
});

interface IProps {
    url?: string;
}

withDefaults(defineProps<IProps>(), {
    url: '',
});

defineEmits<{
    'update:modelValue': [value: boolean];
}>();
const options = {
    errorCorrectionLevel: 'L',
    type: 'terminal',
    quality: 4,
    width: 200,
    height: 200,
    scale: 8,
    margin: 1,
    color: {
        dark: '#000',
        light: '#FFF',
    },
};
</script>

<style lang="less" scoped>
.url-qr-code-wrap {
    transform: scale(0.5);
}
</style>
