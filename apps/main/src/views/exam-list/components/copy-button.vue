<template>
    <b-button style="background: transparent" :disabled="disabled" shape="round" status="primary" type="outline" @click="handleCopy">
        {{ buttonText }}
    </b-button>
</template>

<script setup lang="ts">
interface Props {
    text: string;
    buttonText?: string;
    disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    buttonText: '复制链接',
});

async function handleCopy() {
    if (props.disabled) {
        return;
    }
    try {
        if (navigator.clipboard && window.isSecureContext) {
            // 优先使用 Clipboard API
            await navigator.clipboard.writeText(props.text);
            showSuccess();
        } else {
            // 兜底使用 execCommand
            const textArea = document.createElement('textarea');
            textArea.value = props.text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                textArea.remove();
                showSuccess();
            } catch (err) {
                logger.error('复制失败:', err);
                textArea.remove();
            }
        }
    } catch (err) {
        logger.error('复制失败:', err);
    }
}

function showSuccess() {
    Toast.success('复制成功');
}
</script>
