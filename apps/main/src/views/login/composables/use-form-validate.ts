import { LoginFormData } from '../types';
import { checkPhone, checkSmsCode } from '../util';
import { reactive } from 'vue';

export function useFormValidate() {
    const formData = reactive<LoginFormData>({
        phone: { value: '', focus: false, errMsg: '' },
        smsCode: { value: '', focus: false, errMsg: '' },
    });

    const validateItem = (type: 'phone' | 'smsCode') => {
        if (type === 'phone') {
            formData.phone.errMsg = checkPhone(formData.phone.value).msg;
        } else if (type === 'smsCode') {
            formData.smsCode.errMsg = checkSmsCode(formData.smsCode.value).msg;
        }
    };

    const validateAll = () => {
        const checkPhoneValid = checkPhone(formData.phone.value);
        const checkSmsCodeValid = checkSmsCode(formData.smsCode.value);
        formData.phone.errMsg = checkPhoneValid.msg;
        formData.smsCode.errMsg = checkSmsCodeValid.msg;

        return {
            valid: checkPhoneValid.valid && checkSmsCodeValid.valid,
            checkPhoneValid,
            checkSmsCodeValid,
            invalidFields: {
                phone: checkPhoneValid.valid,
                smsCode: checkSmsCodeValid.valid,
            },
        };
    };

    return {
        formData,
        validateItem,
        validateAll,
    };
}
