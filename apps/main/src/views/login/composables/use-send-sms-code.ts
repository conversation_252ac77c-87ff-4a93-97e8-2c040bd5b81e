import { createTimer } from '@/store/time';

export const useSendSmsCode = (initialCountDown = 60) => {
    const showCounter = ref(false);
    const btnText = ref('发送验证码');

    // 消耗用时倒计时
    const sendSmsbtnCountdown = createTimer();

    const remaining = computed(() => {
        return Number(sendSmsbtnCountdown.remainingTime.seconds);
    });

    const counterText = computed(() => {
        if (showCounter.value) {
            return `${remaining.value}s后重新发送`;
        }
        return btnText.value;
    });

    function startCountdown() {
        sendSmsbtnCountdown.start({
            key: 'SendSmsbtnCountdown',
            finishTime: (t) => t + initialCountDown * 1000,
            onFinished: async () => {},
        });
    }

    return {
        counterText,
        startCountdown,
    };
};
