import { ref } from 'vue';
import { useRoute } from 'vue-router';

export type LoginType = 'sms' | 'password';

const LOGIN_TYPE_STORAGE_KEY = 'loginModePreference';

export function useLoginType() {
    const route = useRoute();
    const loginType = ref<LoginType>('sms');

    // 初始化逻辑
    const queryLoginType = route?.query?.loginType as string | undefined;
    if (queryLoginType) {
        if (queryLoginType === '1') {
            loginType.value = 'sms';
        } else if (queryLoginType === '2') {
            loginType.value = 'password';
        } else {
            const stored = localStorage.getItem(LOGIN_TYPE_STORAGE_KEY) as LoginType | null;
            loginType.value = stored === 'password' ? 'password' : 'sms';
        }
        localStorage.setItem(LOGIN_TYPE_STORAGE_KEY, loginType.value);
    } else {
        const stored = localStorage.getItem(LOGIN_TYPE_STORAGE_KEY) as LoginType | null;
        if (stored === 'sms' || stored === 'password') {
            loginType.value = stored;
        } else {
            loginType.value = 'sms';
            localStorage.setItem(LOGIN_TYPE_STORAGE_KEY, loginType.value);
        }
    }

    function setLoginType(type: LoginType) {
        loginType.value = type;
        localStorage.setItem(LOGIN_TYPE_STORAGE_KEY, type);
    }

    return {
        loginType,
        setLoginType,
    };
}
