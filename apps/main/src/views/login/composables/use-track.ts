export function useTrack() {
    const trackLogin = () => {
        BossAnalyticsTrack('zhice-pc-login', {
            pData: { reportType: ReportTypeEnum.登录, nameZh: '点击登录' },
        });
    };

    const trackLoginSuccess = (phone: string) => {
        BossAnalyticsTrack('zhice-pc-exam-login', {
            pData: {
                type: TrackTypeEnum.成功,
                message: phone?.slice(-4),
                reportType: ReportTypeEnum.登录,
                nameZh: '登录成功',
            },
        });
    };

    const trackLoginFail = (phone: string, errorText: string) => {
        BossAnalyticsTrack('zhice-pc-exam-login', {
            pData: {
                type: TrackTypeEnum.失败,
                message: phone?.slice(-4),
                errorText,
                reportType: ReportTypeEnum.登录,
                nameZh: '登录失败',
            },
        });
    };

    const trackAgreement = () => {
        BossAnalyticsTrack('zhice-pc-login-agree1', {
            pData: { reportType: ReportTypeEnum.登录, nameZh: '点击协议' },
        });
    };

    const trackPrivacyPolicy = () => {
        BossAnalyticsTrack('zhice-pc-login-agree2', {
            pData: { reportType: ReportTypeEnum.登录, nameZh: '点击协议' },
        });
    };

    return {
        trackLogin,
        trackLoginSuccess,
        trackLoginFail,
        trackAgreement,
        trackPrivacyPolicy,
    };
}
