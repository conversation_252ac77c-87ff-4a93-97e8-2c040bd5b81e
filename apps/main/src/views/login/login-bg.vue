<template>
    <div ref="loginBgRef" class="kanjian-login">
        <div class="login-bg">
            <img src="@/assets/images/login/login-bg.png" :class="className" alt="" />
        </div>
        <h1 class="system-name">瞰荐职测</h1>
        <p class="system-sub-name">考试系统</p>
    </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, ref } from 'vue';

defineOptions({
    name: 'LoginBg',
});
const loginBgRef = ref();
const className = ref('');

function setLoginBgClass() {
    className.value = (loginBgRef.value?.clientWidth || 0) / (loginBgRef.value?.clientHeight || 1) > 0.8 ? 'login-h' : 'login-w';
}

onMounted(() => {
    setLoginBgClass();
    window.addEventListener('resize', setLoginBgClass);
});

onUnmounted(() => {
    window.removeEventListener('resize', setLoginBgClass);
});
</script>

<style lang="less" scoped>
.kanjian-login {
    position: relative;
    height: 100%;
    padding: 40px 34px;
    background: linear-gradient(179deg, rgba(124, 200, 194, 0.5) 0%, rgba(247, 255, 254, 0.2) 100%);

    user-select: none;

    /* 防止移动端缩放和点击效果 */
    touch-action: manipulation;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
    .login-bg {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
        width: 100%;
        height: 100%;
        background: url('@/assets/images/login/bg.png') no-repeat top left;
        background-size: 70% auto;

        img {
            position: absolute;
            left: 50%;
            transform: translate(-50%, -50%);
            top: 50%;
            transition: all 0.3s;
            &.login-h {
                max-height: 70%;
            }
            &.login-w {
                width: 80%;
            }
        }
    }

    .system-name,
    .system-sub-name {
        position: relative;
        z-index: 3;
    }

    .system-name {
        margin-bottom: 6px;
        color: #29292d;
        font-size: 25px;
        line-height: 25px;
        font-weight: 500;
    }
    .system-sub-name {
        font-size: 13px;
        font-weight: 500;
        color: #29292d;
        line-height: 12px;
        letter-spacing: 3px;
    }
}
</style>
