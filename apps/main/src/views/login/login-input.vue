<template>
    <div class="login-input-item">
        <span class="login-form-label">{{ label }}</span>
        <div class="input-wrapper">
            <input type="text" :value="value" :maxlength="maxlength" :placeholder="placeholder" @blur="onBlur" @input="onInput" />
            <slot />
        </div>
        <span v-if="errMsg" class="err-tip">{{ errMsg }}</span>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

defineOptions({
    name: 'LoginInput',
});

defineProps({
    label: {
        type: String,
        default: '',
    },
    errMsg: {
        type: String,
        default: '',
    },
    placeholder: {
        type: String,
        default: '请输入',
    },
    maxlength: {
        type: [String, Number],
        default: 11,
    },
    type: {
        type: String,
        default: 'text',
    },
    value: {
        type: String,
        default: '',
    },
});

const emits = defineEmits(['update:value', 'blur', 'input']);

function onBlur() {
    emits('blur');
}
function onInput(e: any) {
    emits('input');
    emits('update:value', ((e.target as HTMLInputElement).value || '').trim());
}
</script>

<style lang="less" scoped>
.login-input-item {
    position: relative;
    margin-bottom: 32px;

    /* 防止移动端缩放和点击效果 */
    touch-action: manipulation;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
    .input-wrapper {
        position: relative;
    }
    input {
        width: 100%;
        height: 48px;
        padding: 0 12px;
        border: 1px solid #d3d8e6;
        border-radius: 8px;
        outline: none;
        font-size: 14px;
        color: #2d2d2d;
        caret-color: #12ada9;
        &:focus {
            border-color: #12ada9;
        }
        &:hover {
            border-color: #12ada9;
        }
        &::-webkit-input-placeholder {
            font-size: 14px;
            color: #a5a3ba;
        }
    }

    .login-form-label {
        display: block;
        margin-bottom: 8px;
        font-size: 13px;
        color: #5d7080;
        line-height: 18px;

        user-select: none;
    }
    .err-tip {
        position: absolute;
        left: 0;
        bottom: -18px;
        font-size: 12px;
        color: #e34d59;

        user-select: none;
    }
}
</style>
