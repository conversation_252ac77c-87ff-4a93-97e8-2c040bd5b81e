import { ref } from 'vue';

export function useCountDown() {
    let timer = 0;
    const countdown = ref(0);
    const total = 60;

    const runTime = () => {
        if (countdown.value > 0) {
            timer = window.setTimeout(() => {
                countdown.value--;
                runTime();
            }, 1000);
        } else {
            // 倒计时结束之后 验证码需要重新校验
            clearTimeout(timer);
        }
    };

    const resetCountdown = () => {
        countdown.value = total;
    };

    return {
        countdown,
        resetCountdown,
        runTime,
    };
}
