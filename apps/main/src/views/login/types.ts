export interface LoginFormData {
    phone: {
        value: string;
        focus: boolean;
        errMsg: string;
    };
    smsCode: {
        value: string;
        focus: boolean;
        errMsg: string;
    };
}

export interface LoginParams {
    mobile?: string;
    smsCaptcha?: string;
    account?: string;
    password?: string;
    loginType: number;
    encryptExamId: string;
    verifyType?: string;
    [key: string]: any;
}

export interface LoginResponse {
    code: number;
    data: {
        token: string;
        [key: string]: any;
    };
    message: string;
}
