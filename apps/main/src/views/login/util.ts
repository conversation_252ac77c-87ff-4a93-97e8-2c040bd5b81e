// 手机号正则
function validMobile(mobile: string): boolean {
    const reg = /^1\d{10}$/;
    return reg.test(mobile);
}

// 校验手机号格式
export function checkPhone(value: string) {
    let msg = '';
    if (!value) {
        msg = '请输入手机号';
    } else if (!validMobile(value)) {
        msg = '请输入正确手机号';
    } else {
        msg = '';
    }
    return {
        valid: !msg,
        msg,
    };
}

// 校验验证码
export function checkSmsCode(value: string) {
    const msg = !value ? '请输入验证码' : '';

    return {
        valid: !msg,
        msg,
    };
}
