import { NEBULA } from '@/types/nebulartc';

export interface CameraMonitorRule {
    // 替考检测
    substituteExam: number;
    substituteExamLimit: number;
    substituteExamLimitTimeWindow: number;
    // 多人脸检测
    multipleFaces: number;
    multipleFacesLimit: number;
    multipleFacesLimitTimeWindow: number;
    // 离开检测
    leaveSeat: number;
    leaveSeatLimit: number;
    leaveSeatLimitTimeWindow: number;
    // 低头检测
    lowerHead: number;
    lowerHeadLimit: number;
    lowerHeadLimitTimeWindow: number;
    // 左右张望检测
    lookAround: number;
    lookAroundLimit: number;
    lookAroundLimitTimeWindow: number;
}

// Type for examinee info fields (adjust structure based on actual data)
/**
 * 考生信息字段类型
 * @description 结构需要根据实际数据进行调整
 */
export interface ExamineeInfoField {
    /**
     * 字段名称 (需要根据实际使用情况验证)
     */
    fieldName: string;
    /**
     * 字段标签
     */
    label: string;
    /**
     * 字段值 (使用 unknown 以确保类型安全)
     */
    value: unknown; // Use unknown for safety
    // Add other properties as needed
    /**
     * 其他可能的属性
     */
}

// Type for base info form data (initially empty object)
/**
 * 基础信息表单数据类型 (初始为空对象)
 */
export type BaseInfoFormData = Record<string, unknown>;

// Type for parameters passed to updatePhoneCamera
/**
 * 传递给 updatePhoneCamera 函数的参数类型
 */
export interface UpdatePhoneCameraParams {
    /**
     * 加密的考试 ID
     */
    encryptExamId: string;
    /**
     * 状态 (0 表示关闭, 可能有其他值)
     */
    status: number; // 0 for close, potentially others
    // Add other potential parameters if known
    /**
     * 其他可能的参数
     */
}

// Monitor Status Types using RTC Placeholders
/**
 * 摄像头状态
 */
export interface CameraStatus {
    /**
     * RTC 客户端实例，可能为 null
     */
    client: NEBULA.RtcClient | null;
    /**
     * 整体状态: 0: 未开启, 1: 失败, 2: 成功
     */
    status: number;
    /**
     * 视频状态: 0: 未开启, 1: 失败, 2: 成功
     */
    videoStatus: number;
    /**
     * 音频状态: 0: 未开启, 1: 失败, 2: 成功
     */
    audioStatus: number;
    /**
     * 错误信息文本
     */
    errorText: string;
    /**
     * 视频错误信息文本
     */
    videoErrorText: string;
    /**
     * 音频错误信息文本
     */
    audioErrorText: string;
    /**
     * 本地流对象，可能为 null
     */
    localStream: NEBULA.RtcStream | null;
}

/**
 * 手机状态
 */
export interface PhoneStatus {
    /**
     * RTC 客户端实例，可能为 null
     */
    client: NEBULA.RtcClient | null;
    /**
     * 状态: 0: 未开启, 1: 失败, 2: 成功
     */
    status: number;
    /**
     * 远端流对象，可能为 null
     */
    remoteStream: NEBULA.RtcStream | null;
    /**
     * 错误信息文本
     */
    errorText: string;
    /**
     * 本地流对象，可能为 null
     */
    localStream: NEBULA.RtcStream | null;
}

/**
 * 屏幕状态
 */
export interface ScreenStatus {
    /**
     * RTC 客户端实例，可能为 null
     */
    client: NEBULA.RtcClient | null;
    /**
     * 状态: 0: 未开启, 1: 失败, 2: 成功
     */
    status: number;
    /**
     * 错误信息文本
     */
    errorText: string;
    /**
     * 本地流对象，可能为 null
     */
    localStream: NEBULA.RtcStream | null;
    /**
     * 视频轨道，可能为 null
     */
    videoTrack: MediaStreamTrack | null;
}

/**
 * 监控状态聚合
 */
export interface MonitorStatus {
    /**
     * 摄像头监控状态
     */
    camera: CameraStatus;
    /**
     * 手机监控状态
     */
    phone: PhoneStatus;
    /**
     * 屏幕监控状态
     */
    screen: ScreenStatus;
}

// Re-export types from @crm/exam-types if used frequently
export type { ICameraMonitorRule, IExamDebugInfo } from '@crm/exam-types';

// Interfaces moved from use-monitor-store.ts
/**
 * 人脸识别文件信息
 */
export interface RecognizeFile {
    /**
     * 文件名
     */
    name: string;
    /**
     * 文件类型
     */
    type: string;
    /**
     * 文件大小 (字节)
     */
    size: number;
    /**
     * 文件 URL
     */
    url: string;
    /**
     * 文件 URI
     */
    uri: string;
}

/**
 * 切屏规则
 */
export interface SwitchScreenRule {
    /**
     * 离开页面时是否强制提交试卷 (0: 否, 1: 是)
     */
    forceSubmitWhenLevePage: number;
    /**
     * 最大允许切屏次数
     */
    maxSwitchCount: number;
    /**
     * 切屏警告次数阈值
     */
    switchScreenWarning: number;
    /**
     * 是否需要警告确认
     */
    warningConfirm: boolean;
    /**
     * 是否需要全屏作答
     */
    fullScreen: number;
}

/**
 * 重新登录规则
 */
export interface ReLoginRule {
    /**
     * 最大允许登录次数
     */
    maxLoginCount: number;
    /**
     * 超过限制时是否强制提交试卷 (0: 否, 1: 是)
     */
    forceSubmitWhenOverLimit: number;
    /**
     * 重新登录警告次数阈值
     */
    reLoginWarning: number;
    /**
     * 当前已重新登录次数
     */
    currentReLoginCount: number;
    /**
     * 是否需要警告确认
     */
    warningConfirm: boolean;
}

/**
 * 考试配置信息
 */
export interface ExamConfig {
    /**
     * 是否允许调试
     */
    canDebug: boolean;
    /**
     * 人脸识别是否需要确认
     */
    recognizeFaceConfirm: boolean;
    /**
     * 是否进行个人信息核验
     */
    personalVerification: boolean;
    /**
     * 人脸识别失败次数
     */
    recognizeFaceFailedCount: number;
    /**
     * 系统识别结果
     */
    systemRecognizeResult: number;
    /**
     * 人脸识别文件信息
     */
    recognizeFile: RecognizeFile;
    /**
     * 是否启用电脑摄像头监控
     */
    computerCameraMonitor: boolean;
    /**
     * 是否启用移动端视角监控
     */
    mobilePerspectiveMonitor: boolean;
    /**
     * 是否启用电脑屏幕监控
     */
    computerScreenMonitor: boolean;
    /**
     * 考生信息是否可修改
     */
    examineeInfoCanModify: boolean;
    /**
     * 考生信息列表
     */
    examineeInfoList: ExamineeInfoField[]; // Use the new type
    /**
     * 是否开启沟通入口
     */
    openCommunicateInlet: boolean;
    /**
     * 监控房间 ID
     */
    monitorRoomId: string;
    /**
     * 摄像头监控规则
     */
    cameraMonitoringRule: CameraMonitorRule;
    /**
     * 切屏规则
     */
    switchScreenRule: SwitchScreenRule;
    /**
     * 重新登录规则
     */
    reLoginRule: ReLoginRule;
}

/**
 * 场次信息
 */
export interface SeqInfo {
    /**
     * 场次 ID
     */
    seqId: string;
    /**
     * 场次名称
     */
    seqName: string;
    /**
     * 场次开始时间
     */
    seqStartTime: string;
    /**
     * 场次结束时间
     */
    seqEndTime: string;
    /**
     * 场次备注
     */
    seqRemarks: string;
    /**
     * 是否允许调试
     */
    canDebug: boolean;
    /**
     * WebSocket 连接密钥
     */
    wsConnectSecret: string;
}

/**
 * 考试信息
 */
export interface ExamInfo {
    /**
     * 考试类型
     */
    examType: number;
    /**
     * 考试 ID
     */
    examId: string;
    /**
     * 考试名称
     */
    examName: string;
    /**
     * 考试备注
     */
    examRemarks: string;
    /**
     * 操作指南
     */
    operateGuide: string;
    /**
     * 中断时长 (秒)
     */
    interruptDuration: number;
    /**
     * 题目列表类型
     */
    questionListType: number;
    /**
     * 排序
     */
    sort: number;
    /**
     * 答题模式
     */
    answerMode: number;
    /**
     * 考试开始时间
     */
    examStartTime: string;
    /**
     * 考试结束时间
     */
    examEndTime: string;
    /**
     * 题目数量
     */
    questionCount: number;
    /**
     * 考试状态: 0: 待启用, 1: 待开始, 2: 考试中, 3: 已结束
     */
    status: number; // 0: 待启用, 1: 待开始, 2: 考试中, 3: 已结束
    /**
     * 状态描述
     */
    statusDesc: string;
    /**
     * 是否已提交试卷
     */
    hasCommitPaper: boolean;
    /**
     * 剩余秒数
     */
    remainSeconds: number;
    /**
     * 建议时长字符串
     */
    adviseDurationStr: string;
    /**
     * 是否有监考员
     */
    hasInvigilator: boolean;
    /**
     * 是否开启倒计时
     */
    openCountDown: boolean;
    /**
     * 考生点击开始作答时间
     */
    paperReadyClickTimeTs: number;
    /**
     * 建议答题时间
     */
    adviseAnswerTime: number;
    /**
     * 答题列表类型
     */
    answerListType: number;
    /**
     * 作答说明 GBA HOTS
     */
    answerInstruction: string;
}

/**
 * 考试基础信息 (包含场次和考试信息)
 */
export interface ExamBaseInfo {
    /**
     * 加密的用户 ID
     */
    encryptUserId: string;
    /**
     * 用户名
     */
    userName: string;
    /**
     * 手机号
     */
    mobile: string;
    /**
     * 场次信息
     */
    seqInfo?: SeqInfo;
    /**
     * 考试信息
     */
    examInfo?: ExamInfo;
}

// Types for HeartBeat functionality in store/time/index.ts
/**
 * 心跳请求参数
 */
export interface HeartBeatParams {
    /**
     * 加密的场次 ID
     */
    encSeqId: string;
    /**
     * 加密的考试 ID
     */
    encExamId: string;
    /**
     * 数据来源 (1 表示 PC 端)
     */
    dataSource: number; // 1 for PC
    /**
     * 类型 (0 表示在线)
     */
    type: number; // 0 for online
    /**
     * 是否正在答题
     */
    answerQuestion: boolean;
}

/**
 * 心跳响应数据
 */
export interface HeartBeatResponse {
    /**
     * 响应码
     */
    code: number;
    /**
     * 响应数据
     */
    data: {
        /**
         * 服务器时间戳
         */
        ts: number;
        // Use unknown for other potential properties, safer than any
        /**
         * 其他可能的属性 (使用 unknown 以确保类型安全)
         */
        [key: string]: unknown;
    };
    /**
     * 响应消息 (可选)
     */
    message?: string;
}
