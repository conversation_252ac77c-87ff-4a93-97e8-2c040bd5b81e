<template>
    <b-dialog title="摄像头和麦克风的异常处理" :width="800" :layerClosable="false" :footer="false" @close="onClose">
        <b-tip
            >请保证使用Chrome浏览器120以上版本，并先按以下方法进行调试。如需更多帮助请拨打 <b>{{ HELP_PHONE }}</b></b-tip
        >
        <div>【重新检测】前，请点击浏览器地址栏右侧的摄像头按钮</div>
        <b-section type="title" :bgColor="true">
            <b-title type="preline" size="xsmall"> 1. 如下图选择“始终允许https：//***.coms使用您的摄像头和麦克风的选项”，点击完成按钮 </b-title>
        </b-section>
        <img class="img-hint" src="@/assets/images/monitor/media-hint.jpg" alt="" />
        <b-section type="title" :bgColor="true">
            <b-title type="preline" size="xsmall"> 2. 如下图请点击浏览器地址栏左侧锁头按钮，麦克风权限选择开启 </b-title>
        </b-section>
        <img class="img-hint" src="@/assets/images/monitor/media-hint-2.png" alt="" />
    </b-dialog>
</template>

<script lang="ts" setup>
import { HELP_PHONE } from '@crm/exam-constants';

const emit = defineEmits(['close']);

function onClose() {
    emit('close');
}
</script>

<style lang="less" scoped>
.img-hint {
    display: block;
    width: 736px;
    margin: 16px auto 0;
    border-radius: 6px;
}
</style>
