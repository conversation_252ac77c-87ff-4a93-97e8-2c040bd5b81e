<template>
    <div v-if="show" class="paper-time-wrap warning">
        <p class="time-inner">
            <SvgIcon name="toast-warning" width="16" height="16" />
            <span class="text">距离试卷关闭时间还有</span>
            <span class="number time">{{ countdownTime?.minutes[0] }}</span>
            <span class="number time">{{ countdownTime?.minutes[1] }}</span>
            <span class="time" style="margin-top: -2px">:</span>
            <span class="number time">{{ countdownTime?.seconds[0] }}</span>
            <span class="number time">{{ countdownTime?.seconds[1] }}</span>
            <span class="text">请自主把握时间，在关闭前完成所有题目作答</span>
        </p>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import { useEndTimeStore } from '@/store/exam-time';

const endTimeStore = useEndTimeStore();

// 场次结束时间
const countdownTime = computed(() => endTimeStore.examEndTime?.remainingTime);

const show = ref(false);
const tipsTime = 10 * 60 * 1000;

watch(
    () => [endTimeStore.examEndTime.remainingTime.total, endTimeStore.examEndTimeStatus],
    ([time, status]) => {
        if (time !== undefined && time <= tipsTime && status === 1) {
            show.value = true;
        }
    },
    {
        immediate: true,
    },
);
</script>

<style lang="less" scoped>
.paper-time-wrap {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 200;
    width: 100vw;
    height: 53px;
    border-radius: 0 0 8px 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;

    &.warning {
        background: #fff0eb;
    }

    .time-inner {
        margin: 0 auto;
        display: flex;
        align-items: center;

        .text {
            margin: 0 6px;
            color: #1f1f1f;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
        }

        .time {
            margin-left: 4px;
            color: #f06a39;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 18px;

            &:first-child {
                margin-left: 0;
            }

            &.number {
                width: 25px;
                font-family: 'Kanzhun';
                padding: 4px 0;
                text-align: center;
                background: #ffffff;
                border: 1px solid #ffd2c1;
                border-radius: 6px;
            }
        }
    }
}
</style>
