.round-content {
    position: relative;
    padding: 20px 32px 18px;
    background: linear-gradient(179.99998deg, #77caff 0%, #cce8ff 51%, #86bfff 100%);
    box-shadow: inset 0 4px 35px 0 rgba(0, 74, 123, 0.23);
    backdrop-filter: blur(18.5px);
    width: 100%;
    height: 100%;
    overflow: hidden;

    &::after {
        content: '';
        position: absolute;
        left: 0;
        height: 261px;
        right: 0;
        bottom: -5px;
        background-image: url('https://img.bosszhipin.com/static/file/2024/h6xy4k3ehq1725606964354.png.webp');
        background-size: 100% auto;
        z-index: -1;
    }

    .title-wrap {
        display: flex;
        align-items: center;
        position: relative;

        .progress-wrap {
            position: absolute;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 179px;
            overflow: hidden;
            width: 340px;
            height: 20px;
            top: 8px;
            left: 205px;

            .bar {
                width: 242px;
                height: 20px;
                background: linear-gradient(
                    179.99998deg,
                    #cdfbff 0%,
                    #619eff 11%,
                    #a6d4ff 45%,
                    #619eff 90%,
                    #cfffff 100%
                );
                box-shadow: inset 1px 0 3px 0 #ffffff;
                border-radius: 17px;
                transition: all 0.5s;
            }

            .text {
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translateX(-50%) translateY(-50%);
                color: #000000;
                font-size: 13px;
                font-style: normal;
                font-weight: 400;
                line-height: 13px;
            }
        }
        .total-fund-container {
            min-width: 143px;
            flex-shrink: 0;
            position: absolute;
            right: 0;
            display: flex;
            align-items: center;
            padding: 0 12px 0 3px;
        }

        .gold-num-fade {
            position: absolute;
            right: 30px;
            top: 65px;
        }
    }

    .game-box-wrap {
        position: relative;
        display: flex;
        gap: 60px;
        justify-content: center;
        margin: 44px 12px 0;

        .question-item {
            width: 172px;
            height: 155px;
            background: #fbfeff;
            border: 5px solid #ffffff;
            border-radius: 20px;
            text-align: center;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: start;

            .title {
                width: 100%;
                height: 31px;
                line-height: 31px;
                background: rgba(181, 225, 255, 0.65);
                border-radius: 20px 20px 0 0;
                color: #000000;
                font-size: 14px;
                font-weight: 500;
                text-align: center;
                margin-bottom: 9px;
            }
        }

        .success::after {
            content: '';
            width: 100%;
            height: 100%;
            border-radius: 20px;
            position: absolute;
            top: 8px;
            left: 0;
            display: inline-block;
            background-image: url('https://img.bosszhipin.com/static/zhipin/313500031537167438.svg');
            background-size: 76px 68px;
            background-position: center;
            background-repeat: no-repeat;
        }

        .fail::after {
            content: '';
            width: 100%;
            height: 100%;
            border-radius: 20px;
            position: absolute;
            top: 8px;
            left: 0;
            display: inline-block;
            background-image: url('https://img.bosszhipin.com/static/zhipin/523500031537147862.svg');
            background-size: 76px 68px;
            background-position: center;
            background-repeat: no-repeat;
        }

        .no-title {
            justify-content: center;

            .title {
                background: #dbf0ff;
            }
        }
        .no-title {
            &::after {
                top: 0;
            }
        }

        .transition-state {
            .title {
                animation: fadeOpacity 0.3s ease-in-out;
                color: rgba(181, 225, 255, 0);
            }
            img {
                animation: fadeImgOpacity 0.3s ease-in-out;
                opacity: 0;
            }
        }
        .result-state {
            img {
                opacity: 0;
            }
            .title {
                color: rgba(181, 225, 255, 0);
            }
        }
        .timeout {
            background: #f6faff;
            border: 5px solid #aaceff;
            border-radius: 20px;

            .title {
                border-radius: 16px 16px 0 0;
                animation: fadeOpacity 0.3s ease-in-out;
                color: rgba(181, 225, 255, 0);
            }
            img {
                animation: fadeImgOpacity 0.3s ease-in-out;
                opacity: 0;
            }
        }
    }

    .option-button-wrap {
        margin-top: 26px;
        display: flex;
        justify-content: center;
        gap: 41px;
        position: relative;
        z-index: 1;

        .button-inner {
            width: 112px;

            :deep(.button-wrap) {
                height: 40px;

                .text {
                    text-shadow: none;
                }
            }

            .button-inner-text {
                .icon {
                    width: 20px;
                    height: 20px;
                    background-size: 100% auto;

                    &.green-tick {
                        background-image: url(https://img.bosszhipin.com/static/file/2024/34hjehwz2k1726036366579.png.webp);
                    }

                    &.orange-tick {
                        background-image: url(https://img.bosszhipin.com/static/file/2024/g8xr90q1k81726036367212.png.webp);
                    }

                    &.cross {
                        background-image: url(https://img.bosszhipin.com/static/file/2024/kvfc7nvx351726036366218.png.webp);
                    }
                }
            }

            .key-down-desc {
                color: #000000;
                font-size: 12px;
                font-weight: 400;
                margin-bottom: 6px;
                text-align: center;
            }
        }
    }
}
@keyframes fadeOpacity {
    from {
        color: rgba(0, 0, 0, 1); /* 透明度100% */
    }
    to {
        color: rgba(0, 0, 0, 0); /* 透明度10% */
    }
}
@keyframes fadeImgOpacity {
    from {
        opacity: 1; /* 透明度100% */
    }
    to {
        opacity: 0; /* 透明度10% */
    }
}
