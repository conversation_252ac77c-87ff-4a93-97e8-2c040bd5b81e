<template>
    <div class="assign-kit-container">
        <div class="input-container">
            <div class="button-minus" @click="change({ action: '-', step: 1 })" />
            <input v-model="inputValue" type="text" @input="updateModelValue" @focus="focused = true" @blur="focused = false" />
            <div class="button-plus" @click="change({ action: '+', step: 1 })" />
        </div>
        <div class="slider-container">
            <b-slider v-model="inputValue" :showInput="false" :showTooltip="false" :step="1" :min="0" :max="currentRoundReward" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { onKeyStroke } from '@crm/vueuse-pro';
import { ref, watch } from 'vue';

defineOptions({
    name: 'AssignKit',
});
defineProps({
    propName: {
        type: Object,
        default: () => ({}),
    },
    currentRoundReward: {
        type: Number,
        default: 0,
    },
});
const emit = defineEmits(['change']);
const inputValue = ref(0);
function updateModelValue(event: Event) {
    const newValue = (event.target as HTMLInputElement).value;
    let result: string[] = [];
    if (newValue.length > 0) {
        for (let i = 0; i < newValue.length; i++) {
            if (newValue[i] === '.') {
                break;
            } else {
                if (/\d/.test(newValue[i])) {
                    result.push(newValue[i]);
                }
            }
        }
    }
    result = result.length > 0 ? result : ['0'];
    inputValue.value = Number(result.join(''));
}
watch(
    () => inputValue.value,
    () => {
        emit('change', inputValue.value);
    },
);
/**
 * slider 组件自己处理了极值
 * @param config
 */
function change(config: { action: '+' | '-'; step: number }) {
    if (config.action === '+') {
        inputValue.value = inputValue.value + config.step;
    } else if (config.action === '-') {
        inputValue.value = inputValue.value - config.step;
    }
}

const focused = ref(false);
onKeyStroke(['ArrowDown'], (e) => {
    e.preventDefault();
    if (focused.value) {
        change({ action: '-', step: 1 });
    }
});
onKeyStroke(['ArrowUp'], (e) => {
    e.preventDefault();
    if (focused.value) {
        change({ action: '+', step: 1 });
    }
});
</script>

<style lang="less" scoped>
.assign-kit-container {
    width: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .input-container {
        width: 114px;
        height: 36px;
        border: 1px solid #feb305;
        border-radius: 18px;
        overflow: hidden;
        background-color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;

        input {
            border: none;
            width: 30px;
            height: 20px;
            text-align: center;
        }

        .button-minus,
        .button-plus {
            position: absolute;
            width: 36px;
            height: 36px;
            cursor: pointer;
        }

        .button-minus {
            top: 0px;
            left: 0;
            background-image: url(https://img.bosszhipin.com/static/file/2024/bnt7zsn8li1726733559416.svg);
        }

        .button-plus {
            top: 0px;
            right: -2px;
            background-image: url(https://img.bosszhipin.com/static/file/2024/t7qkkpip7w1726733559212.svg);
        }
    }

    .slider-container {
        width: 100%;
        margin-top: 17px;

        :deep(.b-slider) {
            .b-slider-runway {
                height: 12px;
                background: rgba(254, 230, 151, 0.8);
                box-shadow: inset 0px 1px 1px 0px rgba(240, 160, 0, 0.64);
                border-radius: 6px;
                margin: 0;

                .b-slider-bar {
                    height: 100%;
                    background: #fe9924;
                    box-shadow: inset 1px 1px 2px 0px #bf5d00;
                    border: 1px solid #ffbc4f;
                    border-radius: 6px;
                }

                .b-slider-button-wrap {
                    width: 26px;
                    height: 26px;
                    background-image: url(https://img.bosszhipin.com/static/file/2024/sdvawamnnm1726733559546.svg);

                    .b-slider-button {
                        display: none;
                    }
                }
            }
        }
    }
}
</style>
