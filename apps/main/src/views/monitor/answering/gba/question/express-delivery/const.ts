// 定义快递柜项目类型
export interface CabinetItem {
    index: number;
    key: number;
    isShow: boolean;
    showType: string;
    chooseTimeTs?: number;
}

// 定义展示列表项目类型
export interface ShowListItem {
    index: number;
    key: number;
    isShow: boolean;
    showType: string;
    chooseTimeTs?: number;
}

// 定义选择列表项目类型
export interface SelectListItem {
    index: number;
    key: number;
    isShow: boolean;
    showType: string;
    chooseTimeTs: number;
}

// 定义回合数据类型
export interface RoundData {
    encSnapshotId: string; // 快照id
    showList: ShowListItem[]; // 需要展示的快递
    selectList: SelectListItem[]; // 用户选择的数据
    difficulty: number; // 难度
    difficultyStage: number; // 当前难度阶段
    roundStage: number; // 阶段  1: 记忆阶段  2: 复现阶段 用于匹配 roundStageMap 字段
    isChoose: boolean; // 是否可以作答
    errNum: number; // 回答错误次数
    profit: number; // 可获得奖励,
    repeatTimeTs: number; // 复现开始时间戳
    timeoutTs: number; // 作答超时 为0正常作答  大于0超时 值为超时时间搓
    showTimeTs: number; // 展示时间，开始记忆时间
    repeatDetail: {
        // 复现详情
        chooseTimeTs: number[]; // 用户选择的时间戳
    };
    result: number; // 结果  0:失败  1:成功
}

export function defineCabinetList(): CabinetItem[] {
    return Array.from({ length: 20 })
        .fill(0)
        .map((_item, index) => {
            return {
                index,
                key: index,
                isShow: false,
                showType: '',
            };
        });
}

export function defineRoundData(): RoundData {
    return {
        encSnapshotId: '', // 快照id
        showList: [], // 需要展示的快递
        selectList: [], // 用户选择的数据
        difficulty: 3, // 难度
        difficultyStage: 1, // 当前难度阶段
        roundStage: 1, // 阶段  1: 记忆阶段  2: 复现阶段 用于匹配 roundStageMap 字段
        isChoose: false, // 是否可以作答
        errNum: 0, // 回答错误次数
        profit: 0, // 可获得奖励,
        repeatTimeTs: 0, // 复现开始时间戳
        timeoutTs: 0, // 作答超时 为0正常作答  大于0超时 值为超时时间搓
        showTimeTs: 0, // 展示时间，开始记忆时间
        repeatDetail: {
            // 复现详情
            chooseTimeTs: [], // 用户选择的时间戳
        },
        result: 0, // 结果  0:失败  1:成功
    };
}

export const roundStageMap: Record<number, { text: string; color: string; tips: string }> = {
    1: {
        text: '记忆阶段',
        color: '#DE4600',
        tips: '记忆阶段',
    },
    2: {
        text: '复现阶段',
        color: '#389900',
        tips: '请复现顺序与位置',
    },
};
