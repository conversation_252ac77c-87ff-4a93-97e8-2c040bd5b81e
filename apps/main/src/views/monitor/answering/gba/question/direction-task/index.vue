<template>
    <div class="round-content">
        <div class="title-wrap">
            <Text font-weight="600" stroke-width="0" :text="`${numberToChinese(gbaStore.currentIndex + 1, false)}、${gbaStore.currentQuestion.showName}`" />
            <div class="progress-wrap">
                <p :style="{ width: `${(roundPassed / roundList.length) * 100}%` }" class="bar" />
                <p class="text">剩余回合 {{ roundList.length - roundPassed }} 轮</p>
            </div>
            <TotalFund :goldNum="goldNum" :reservedDecimalPlaces="checkNumberType(goldNum)" strokeWidth="0" />
            <FadeInText class="gold-num-fade" :fundAnimationConfig="fundAnimationConfig" />
        </div>
        <div class="game-box-wrap">
            <div class="question-item-box">
                <div
                    v-for="item in Array.from({ length: 15 }, (_, i) => i + 1)"
                    :key="item"
                    :class="{
                        'position-center': item === 8 && isStart,
                        fail: item === 8 && result === 0,
                        success: item === 8 && result === 1,
                        'transition-state': transitionState,
                    }"
                    class="question-item"
                >
                    <template v-if="isStart">
                        <img
                            v-if="item === 8"
                            :class="`position-${currentRoundSnapshot.reactDirectId}`"
                            :src="
                                currentRoundSnapshot.reactDirectId === 5
                                    ? 'https://img.bosszhipin.com/static/zhipin/kanjian/763215614637132748.svg'
                                    : 'https://img.bosszhipin.com/static/zhipin/kanjian/963215614637198385.svg'
                            "
                        />
                        <img
                            v-else
                            :class="`position-${currentRoundSnapshot.disturbDirectId}`"
                            :src="
                                currentRoundSnapshot.disturbDirectId === 5
                                    ? 'https://img.bosszhipin.com/static/zhipin/kanjian/763215614637132748.svg'
                                    : 'https://img.bosszhipin.com/static/zhipin/kanjian/963215614637198385.svg'
                            "
                        />
                    </template>
                </div>
            </div>
            <div class="option-button-box">
                <div v-if="showBtnGroup" class="option-button-wrap" :class="{ disabled: btnGroupDisabled }">
                    <template v-for="item in optionList" :key="item.id">
                        <div class="btn" :class="`button-inner-${item.id}`" @click="handleSelect(item)" />
                    </template>
                </div>
                <p class="key-down-desc">快捷键"方向键"</p>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { createTimer, timeCenter } from '@/store/time';
import { ref, computed, onMounted, reactive } from 'vue';
import { useNotification } from '../../component/notification-tips/index';
import { useGbaStore } from '../../store';
import { onKeyStroke } from '@crm/vueuse-pro';
import { sum } from 'lodash-es';
import { nextTick } from 'vue';
import FadeInText from '../../component/fadein-text.vue';
import Text from '../../component/text.vue';
import TotalFund from '../../component/total-fund.vue';
import { delay, OPTION_LIST } from './const';

import { numberToChinese } from '@/utils/number-to-chinese';

const props = defineProps({
    isSimulate: {
        type: Boolean,
        default: false,
    },
});
const emits = defineEmits(['roundOver', 'gameOver', 'roundStart', 'roundChange']);
const gbaStore = useGbaStore();
const { currentTime } = timeCenter;
const useNotificationTips = useNotification();

const isStart = ref(false);

// 方向键
const optionList = ref<Array<any>>(OPTION_LIST());
const fundAnimationConfig = ref({
    show: false,
    text: '+5',
    action: '-',
});

// 游戏回合数据
const roundList = computed(() => {
    if (props.isSimulate) {
        // 模拟模式下 使用模拟数据
        return gbaStore.currentQuestion.simulateList;
    } else {
        return gbaStore.currentQuestion.roundList;
    }
});

// 已处理回合数
const roundPassed = ref(0);
// 当前回合数据
const currentRoundData: any = computed(() => roundList.value[roundPassed.value]);
// 当前回合游戏数据快照
const currentRoundSnapshot = computed(() => currentRoundData.value?.roundSnapshot || {});

function defaultSelectData() {
    return {
        encSnapshotId: '', // 当前回合id
        paramId: 0, // 用户选择的数据（矿山id）
        chooseTimeTs: 0, // 用户选择的时间点
        showTimeTs: 0, // 当前回合首次展示的时间
        timeoutTs: 0, // 当前回合超时时间点 (与 chooseTime 互斥);
    };
}

// 缓存已选择的数据，用于数据提交与金币飞入坐标
let selectData: any = reactive(defaultSelectData());
// 是否展示参与or不参与按钮
const showBtnGroup = ref(true);
const btnGroupDisabled = ref(true);
const goldNum = ref(0);
const result = ref();

// 题目选择到显示结果过渡状态
const transitionState = ref();
const roundCountdown = createTimer();

// 点击方向键
async function handleSelect(row: { id: number }) {
    if (btnGroupDisabled.value) {
        return;
    }
    // 停止倒计时
    roundCountdown.stop();
    selectData.chooseTimeTs = currentTime.value;
    selectData.paramId = row.id;

    btnGroupDisabled.value = true;
    if (currentRoundSnapshot.value.disturbDirectId !== 5) {
        result.value = row.id === currentRoundSnapshot.value.reactDirectId ? 1 : 0;
    } else {
        result.value = 0;
    }
    transitionState.value = true;
    if (result.value === 1) {
        fundAnimationConfig.value = {
            show: true,
            text: `+${currentRoundSnapshot.value.score || 0}`,
            action: '+',
        };
        await delay(0.3);
        goldNum.value = goldNum.value + currentRoundSnapshot.value.score;
    }
    roundEnd();
}

// 设置回合默认数据
function setRoundData(index: number = -1) {
    const roundPassedIndex = index > -1 ? index : roundPassed.value;
    const roundData: any = roundList.value[roundPassedIndex];
    // 设置已选择的数据
    selectData = {
        ...defaultSelectData(),
        showTimeTs: currentTime.value, // 设置回合曝光时间
        encSnapshotId: roundData.encryptId, // 回合ID
        timeoutTs: 0,
    };
}

function resetStatus() {
    btnGroupDisabled.value = false;
    result.value = null;
    transitionState.value = false;
    fundAnimationConfig.value = {
        show: false,
        text: '5',
        action: '+',
    };
}
function checkNumberType(num: number) {
    return num % 1 === 0 ? 0 : 1;
}
// 回合结束
async function roundEnd(isTimeOut: boolean = false) {
    if (!currentRoundSnapshot.value) {
        return;
    }
    const params = {
        ...selectData,
    };
    if (isTimeOut) {
        params.timeoutTs = currentTime.value;
        delete params.paramId;
    }
    emits('roundOver', params);
    await delay((gbaStore.currentQuestion.resultAppearTime || 0) / 1000);
    roundPassed.value += 1;

    nextTick(() => {
        roundContinue();
    });
}

// 继续下一个回合
function roundContinue() {
    resetStatus();
    // 回合结束-游戏结束
    if (roundPassed.value >= roundList.value.length) {
        gameOver();
        btnGroupDisabled.value = true;
        emits('roundChange', { type: 'end' });
        return;
    }
    emits('roundChange', { currentQuestionId: currentRoundData.value.encryptId });
    nextTick(() => {
        setRoundData();
        roundCountdown?.start({
            key: 'roundCountdown',
            finishTime: (t) => t + gbaStore.currentQuestion.chooseTime,
            onFinished: async () => {
                selectData.timeoutTs = currentTime.value;
                // 倒计时结束时，如果玩家尚未做出选择，则提示"作答超时"，进入到下一轮次
                btnGroupDisabled.value = true;
                if (currentRoundSnapshot.value.disturbDirectId !== 5) {
                    result.value = 0;
                    useNotificationTips.open({
                        type: 'tips-message',
                        text: '作答超时',
                        onClose: () => {
                            roundEnd(true);
                        },
                    });
                } else {
                    result.value = 1;
                    fundAnimationConfig.value = {
                        show: true,
                        text: `+${currentRoundSnapshot.value.score || 0}`,
                        action: '+',
                    };

                    nextTick(async () => {
                        await delay(0.3);
                        goldNum.value = goldNum.value + currentRoundSnapshot.value.score || 0;
                        roundEnd(true);
                    });
                }

                transitionState.value = true;
            },
        });
    });
}

// 所有回合结束 游戏结束
function gameOver() {
    if (!props.isSimulate) {
        useNotificationTips.open({
            type: 'template-message',
            action: 'direction-task-end',
            theme: 'orange-red',
            number: goldNum.value,
            onNext: () => {
                emits('gameOver');
            },
        });
    } else {
        emits('gameOver');
    }
}

// 通过键盘点击ArrowDoUp、ArrowDown、ArrowLeft、ArrowRight来选择是否参与
onKeyStroke(['ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowUp'], (e) => {
    e.preventDefault();
    const data = optionList.value.find((item) => item.shortcutKeys === e.key);
    if (data) {
        handleSelect(data);
    }
});
onMounted(() => {
    // 查看是否还有未答回合
    const index = roundList.value.findIndex((item) => item.answerStatus === 0);
    // 计算已答回合金额
    const number = sum(roundList.value.map((item) => item?.roundSnapshot?.reward || 0));
    goldNum.value = number || 0;
    // 如果所有回合回答完毕 直接展示结算弹窗
    if (index < 0) {
        roundPassed.value = roundList.value.length;
        setRoundData(roundList.value.length - 1);
        gameOver();
    } else {
        roundPassed.value = index;
        setRoundData();
        useNotificationTips.open({
            type: 'countdown-start-message',
            onClose: () => {
                roundContinue();
                isStart.value = true;
            },
        });
    }
});
</script>

<style lang="less" scoped>
@import 'index';
</style>
