import { sum } from 'lodash-es';
import { defineStore } from 'pinia';
import { ref } from 'vue';
import { gbaQuestionType } from './type';
import { useEndTimeStore } from '@/store/exam-time';

interface mineParamItem {
    // 矿山选项
    paramId: number | string; // 选项id  对应--- assetsCongig 下 paramId 获取图片
    profit: number; // 收益
    loss: number; // 损失
}

interface TroundItem {
    encryptId: string; // 回合ID
    answerStatus: number; // 作答状态 // 0:未作答, 1:已作答
    reward: number; // 当前回合下获得的金币 可能是负数
    paramList: Array<mineParamItem>;
    optionList: Array<any>;
    roundSnapshot: any;
    errNum?: number; // 错误次数，可选属性
}

interface TgbaQuestion {
    id: number | string;
    name: string;
    showName: string;
    answerDesc: string; // 单题作答说明
    answerDescTime: number; // 单题作答说明 展示时长
    answerTime: number; // 回合时间
    answerStatus: number; // 作答状态 // 0:未作答, 1:已作答
    assetsCongig: Array<{ paramId: number | string; paramImg: string }>; // 资源映射
    roundList: Array<TroundItem>;
    simulateList: Array<TroundItem>; // 模拟数据
    reward: number; // 当前回合下 已经获得的金币
    chooseTime: number; // 共享任务题目，选择时长
    distributeTime: number; // 共享任务题目，分配时长
    rewardMap: object; // 净利润
    areaConfigList: Array<{ areaId: number | string; title: string }>; // 资源映射
    resultAppearTime: number;
    score: number;
    repeatTime: number; // 快递题回合超时时长
    roundSnapshot: any;
}

export const useGbaStore = defineStore('gbaStore', () => {
    const endTimeStore = useEndTimeStore();

    // 所有题目
    const questionList = ref<Array<TgbaQuestion>>([]);

    // 当前正在答题的下标;
    const currentIndex = ref(0); // 当前正在答题的下标;

    // 当前正在答的题
    const currentQuestion = ref<TgbaQuestion>({
        id: 0,
        name: '',
        showName: '',
        answerDesc: '',
        answerDescTime: 0,
        answerStatus: 0,
        answerTime: 0,
        assetsCongig: [],
        roundList: [],
        simulateList: [],
        reward: 0,
        chooseTime: 0,
        distributeTime: 0,
        rewardMap: {},
        areaConfigList: [],
        resultAppearTime: 0,
        score: 0,
        roundSnapshot: {},
        repeatTime: 0,
    });

    // 获取场次与试卷基础信息与配置
    const getGbaAnswerList = async (params: { encryptExamId: string }) => {
        const res = await Invoke.gba.gbaAnswerList(params);
        if (res.code !== 0) {
            return;
        }

        initData(res.data);

        return res;
    };

    // 获取预览数据
    const getGbaAnswerListPreview = async (params: { key: string | null }) => {
        const { res } = await Invoke.preview.paperGbaPreview(params);
        if (res.code !== 0) {
            return;
        }
        initData(res.data);
    };

    // 修改当前题下 回合排序及状态
    const modifyCurrentQuestionRoundList = (params: { encSnapshotId: string; answerContent: any }) => {
        const list = [...currentQuestion.value.roundList];
        const index = list.findIndex((item) => item.encryptId === params.encSnapshotId);

        if (index < 0) {
            return;
        }

        // 同步数据
        currentQuestion.value.roundList = roundDataModify(params, list);
    };

    // 同步逻辑处理
    const roundDataModify = (params: { encSnapshotId: string; answerContent: any }, roundList: any[]) => {
        const list = [...roundList];
        const index = list.findIndex((item) => item.encryptId === params.encSnapshotId);
        const { profit, result, errNum } = params.answerContent || {};
        const roundItem = list[index];

        // 满足错误次数修改状态
        if (result === 0 && currentQuestion.value.id === gbaQuestionType.DELIVERRY) {
            roundItem.answerStatus = 2; // 改为 答题中;
            roundItem.errNum = errNum; // 计算回答错误次数roundItem.answerStatus = 1
            if (roundItem.errNum >= 3) {
                // 满足错误次数修改状态
                roundItem.answerStatus = 1;
                const { stage } = roundItem?.roundSnapshot || {};
                // 同一难度的题 状态设置为 跳过
                list.forEach((item) => {
                    const { roundSnapshot } = item || {};
                    if (roundSnapshot.stage === stage && item.answerStatus === 0) {
                        item.answerStatus = 3;
                    }
                });
            }
        } else {
            // 修改答题已答
            roundItem.answerStatus = 1;
            // 同步获得的金币
            roundItem.reward = profit || 0;

            list[index] = roundItem;

            // 非快递题 需要排序
            if (currentQuestion.value.id !== gbaQuestionType.DELIVERRY) {
                list.sort((a, b) => a.answerStatus + b.answerStatus);
            }
        }

        return [...list];
    };

    // 进入下一题
    const questionNext = () => {
        currentIndex.value += 1;
        const nextQuestion = questionList.value[currentIndex.value];
        if (nextQuestion) {
            currentQuestion.value = nextQuestion;
        }
    };

    // 同步游戏最后的到金币;(暂时没有用到)
    const setGemeReward = (id: string, rewardSum: number) => {
        const index = questionList.value.findIndex((item) => item.id === id);
        if (index >= 0 && questionList.value[index]) {
            questionList.value[index].reward = rewardSum;
        }
    };

    // 格式化回合数据
    const formatRoundList = (roundList: Array<any>) => {
        return (roundList || []).map((roundItem: any) => {
            const { encryptId, answerStatus, roundSnapshot, errNum, answerTimeout } = roundItem;
            return {
                encryptId, // 回合ID
                answerStatus,
                answerTimeout,
                errNum,
                paramList: roundSnapshot.paramList,
                optionList: roundSnapshot.optionList,
                reward: roundSnapshot.reward,
                roundSnapshot,
            };
        });
    };

    // 初始化试卷数据
    const initData = (data: any) => {
        const questionProductList = data?.questionProductList || [];

        // 处理题型数据
        const newQuestionList = questionProductList.map((item: any) => {
            const {
                id,
                name,
                showName,
                answerDesc,
                answerDescTime,
                answerTime,
                chooseTime,
                distributeTime,
                answerStatus,
                paramConfigList,
                questionVOList,
                questionVOMockList,
                rewardMap,
                areaConfigList = [],
                resultAppearTime,
                score,
                repeatTime,
            } = item;
            // 处理回合数据
            const newRoundList = formatRoundList(questionVOList);

            // 处理作答说明模拟数据
            const newSimulateList = formatRoundList(questionVOMockList);

            // 计算当前题获得总金币
            const reward = sum(newRoundList.map((d: { reward: number }) => d.reward));

            return {
                id,
                name,
                showName,
                answerDesc,
                answerDescTime,
                answerStatus, // 0:未答 1:已答  2:答题中  3:跳过
                answerTime,
                chooseTime,
                resultAppearTime,
                distributeTime,
                assetsCongig: paramConfigList,
                areaConfigList,
                roundList: newRoundList,
                simulateList: newSimulateList,
                reward,
                rewardMap,
                score,
                repeatTime,
            };
        });

        // 设置最晚交卷时间;
        if (data?.examEndTimeTs) {
            endTimeStore.initExamEndTime(data?.examEndTimeTs);
        }

        // 赋值
        questionList.value = [...newQuestionList];
        const index = newQuestionList.findIndex((item: { answerStatus: number }) => item.answerStatus === 0);
        currentIndex.value = index < 0 ? newQuestionList.length - 1 : index;
        currentQuestion.value = newQuestionList[currentIndex.value];
    };

    // 清空
    const restoreSimulateList = () => {
        const { simulateList } = currentQuestion.value;
        currentQuestion.value = {
            ...currentQuestion.value,
            simulateList: simulateList.map((item) => ({
                ...item,
                answerStatus: 0,
            })),
        };
    };

    return {
        questionList,
        currentIndex,
        currentQuestion,
        getGbaAnswerList,
        questionNext,
        modifyCurrentQuestionRoundList,
        roundDataModify,
        setGemeReward,
        getGbaAnswerListPreview,
        restoreSimulateList,
    };
});
