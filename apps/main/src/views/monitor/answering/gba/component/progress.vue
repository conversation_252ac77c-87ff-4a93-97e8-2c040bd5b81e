<template>
    <div class="progress-bar-wrap">
        <p
            class="bar"
            :style="{
                width: `${(passed / total) * 100}%`,
                background: color,
            }"
        ></p>
        <p class="text">剩余回合 {{ total - passed }} 轮</p>
    </div>
</template>
<script setup lang="ts" name="Progress">
defineProps({
    passed: {
        // 经过的回合
        type: Number,
        default: 0,
    },
    total: {
        type: Number,
        default: 0,
    },
    color: {
        type: String,
        default: 'linear-gradient(179.99998deg, #FFF3DD 0%, #FFA200 11%, rgba(255, 207, 96, 0.76) 45%, #FF9C09 92%, #FFF0B4 100%)',
    },
});
</script>
<style lang="less" scoped>
.progress-bar-wrap {
    position: relative;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 179px;
    overflow: hidden;
    width: 340px;
    height: 20px;
    padding-right: 1px;

    .bar {
        width: 244px;
        height: 20px;
        // background: linear-gradient(179.99998deg, #FFF3DD 0%, #FFA200 11%, rgba(255, 207, 96, 0.76) 45%, #FF9C09 92%, #FFF0B4 100%);
        background: linear-gradient(179.99998deg, #b2f56f 0%, #4fc435 11%, #aee929 45%, #4fc435 92%, #b2f56f 100%);
        box-shadow: inset 1px 0 3px 0 #ffffff;
        // border-radius: 17px 0 0 17px;
        border-radius: 17px;
        transition: all 0.5s;
    }

    .text {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translateX(-50%) translateY(-50%);
        color: #000000;
        font-size: 13px;
        font-style: normal;
        font-weight: 400;
        line-height: 13px;
    }
}
</style>
