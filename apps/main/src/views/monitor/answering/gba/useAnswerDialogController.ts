import type { Ref } from 'vue';
import { useEventSubscription } from '@/hooks/useEventSubscription';
import { timeCenter } from '@/store/time';
import { computed, ref } from 'vue';
import { Timer } from '@crm/exam-hooks';

type Question = any;

type AnswerDialogType = 'auto-pop' | 'click-pop';

export interface QuestionType {
    status: 'idled' | 'ready' | 'activating' | 'caching' | 'answered';
    question: Question;
}

/**
 * 管理问题的回答对话框的状态和行为。
 *
 * 文档：https://zhishu.zhipin.com/docs/cZUe1f89F6G
 *
 * @param currentQuestion - 当前问题的引用。
 * @returns 一个包含控制回答对话框的方法和属性的对象。
 */
export function useAnswerDialogController(currentQuestion: Ref<Question>, questionCountdown: Timer) {
    const { currentTime } = timeCenter;
    const remainingTime = computed(() => questionCountdown.remainingTime.total || currentTime.value);

    const dialogType = ref<AnswerDialogType>('auto-pop');
    const questionList = ref<QuestionType[]>([]);
    const dialogOpened = ref<boolean>(false);

    const readyQueue = computed(() => {
        return questionList.value.filter((item) => item.status === 'ready');
    });
    const autoPopQueue = computed(() => questionList.value.filter((item) => item.status === 'activating'));
    // const autoPopQueue = computed(() => questionList.value.filter(item => item.status === 'ready'))
    const clickPopQueue = computed(() => questionList.value.filter((item) => item.status === 'activating' || item.status === 'caching'));

    const init = (questions: QuestionType[]) => {
        questionList.value = questions.map((item) => ({
            status: 'idled',
            question: item,
        }));
    };

    const start = () => {
        questionList.value.forEach((item) => {
            const { questionSnapshot, answered, popUpCountDownSecond } = item.question;
            const { parentId, product10QuestionParamVO } = currentQuestion.value;
            const answerTime = (product10QuestionParamVO?.answerTime || 0) * 1000;

            if (questionSnapshot.parentId === parentId && answered !== 1) {
                if (answerTime - remainingTime.value >= popUpCountDownSecond * 1000) {
                    item.status = 'caching';
                } else {
                    item.status = 'ready';
                }
            } else {
                item.status = 'idled';
            }
        });
    };

    const reset = () => {
        questionList.value.forEach((item) => {
            item.status = 'idled';
        });
        dialogOpened.value = false;
    };

    const openDialog = (type: AnswerDialogType) => {
        dialogOpened.value = true;
        dialogType.value = type;
    };

    const subscribeAddDisplayQuestionEvent = () => {
        let intervalId: ReturnType<typeof setInterval> | null = null;

        const checkPopupCondition = () => {
            if (readyQueue.value.length === 0) return;

            const { product10QuestionParamVO } = currentQuestion.value;
            const answerTime = (product10QuestionParamVO?.answerTime || 0) * 1000;

            // 计算已过去的时间
            const elapsedTime = answerTime - remainingTime.value;

            // 检查所有ready状态的问题，找出符合条件的
            const questionsToActivate: any[] = [];

            readyQueue.value.forEach((readyQuestion) => {
                const popUpCountDownSecond = readyQuestion.question.popUpCountDownSecond || 0;
                const shouldPopup = elapsedTime >= popUpCountDownSecond * 1000;

                if (shouldPopup) {
                    questionsToActivate.push(readyQuestion);
                }
            });

            if (questionsToActivate.length > 0) {
                // 将所有符合条件的问题状态设置为activating
                questionsToActivate.forEach((questionToActivate) => {
                    const questionFind = questionList.value.find((item) => item.question.encryptId === questionToActivate.question.encryptId);
                    if (questionFind) {
                        questionFind.status = 'activating';
                    }
                });

                // 打开弹窗
                openDialog('auto-pop');

                // 弹窗后，将定时器间隔调整为较长时间，避免重复触发
                clearInterval(intervalId!);
                intervalId = setInterval(checkPopupCondition, 1000); // 继续1秒检查一次，因为可能还有其他时间点的问题
            }
        };

        // 使用定时器持续检查弹窗条件
        intervalId = setInterval(checkPopupCondition, 1000); // 每秒检查一次

        // 清理定时器的函数
        const cleanup = () => {
            if (intervalId) {
                clearInterval(intervalId);
                intervalId = null;
            }
        };

        return cleanup;
    };

    // const originalSubscribeMethod = subscribeAddDisplayQuestionEvent;

    // 重新定义为原来的事件订阅方式，但保留备用
    const subscribeAddDisplayQuestionEventOriginal = () =>
        useEventSubscription(
            'pushQuestion',
            () => {
                const { product10QuestionParamVO } = currentQuestion.value;
                const answerTime = (product10QuestionParamVO?.answerTime || 0) * 1000;
                const lastQuestion = readyQueue.value[readyQueue.value.length - 1];
                const popUpCountDownSecond = lastQuestion?.question.popUpCountDownSecond || 0;
                const condition = readyQueue.value.length > 0 && answerTime - remainingTime.value >= popUpCountDownSecond * 1000;

                return condition;
            },
            () => {
                const lastQuestion = readyQueue.value[readyQueue.value.length - 1];
                if (lastQuestion) {
                    const foundQuestion = questionList.value.find((item) => item.question.encryptId === lastQuestion.question.encryptId);
                    if (foundQuestion) {
                        foundQuestion.status = 'activating';
                    }
                }

                openDialog('auto-pop');
            },
        );

    const next = (question: Question) => {
        const questionFind = questionList.value.find((item) => item.question.encryptId === question.encryptId);

        if (questionFind) {
            questionFind.status = 'answered';
        }

        const shouldKeepOpen = (dialogType.value === 'auto-pop' ? autoPopQueue.value : clickPopQueue.value).length > 0;

        dialogOpened.value = shouldKeepOpen;
    };

    const closeDialog = () => {
        questionList.value.forEach((item) => {
            if (item.status === 'activating') {
                item.status = 'caching';
            }
        });
        dialogOpened.value = false;
    };

    const validate = () => {
        if (readyQueue.value.length > 0 || clickPopQueue.value.length > 0) {
            questionList.value.forEach((item) => {
                if (item.status === 'ready' || item.status === 'activating') {
                    item.status = 'caching';
                }
            });
            openDialog('click-pop');
            return false;
        }
        return true;
    };

    return {
        dialogType,
        autoPopQueue,
        clickPopQueue,
        dialogOpened,
        init,
        start,
        openDialog,
        closeDialog,
        next,
        reset,
        validate,
        subscribeAddDisplayQuestionEvent,
        subscribeAddDisplayQuestionEventOriginal,
    };
}
