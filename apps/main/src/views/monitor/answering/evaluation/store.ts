import { computed, ref } from 'vue';
import { defineStore } from 'pinia';
import { uniqueId } from 'lodash-es';
import { questionTypeMap } from './constant';
import { useMonitorStore } from '@/store/use-monitor-store';
import { createTimer } from '@/store/time';
import { useEndTimeStore } from '@/store/exam-time';

export const usePaperStore = defineStore('paperStore', () => {
    const loading = ref(true);
    const isPreview = ref(false); // 是否是预览模式
    const allQuestionList = ref<any[]>([]);
    const backgroundInfoList = ref<any[]>([]); // 背景信息
    const questionGroupInfo = ref(null); // 题组信息
    const currentQuestionIndex = ref(0);
    const currentQuestion = computed(() => allQuestionList.value[currentQuestionIndex.value]);
    const sceneDetailPopupShow = ref(false); // 控制情景题小题内触发背景信息展示
    const config = ref({
        reviewRuleType: 1, // 回看规则：1:可回看一题   2:不可回看    3:不限制回看
        timedRuleType: 1, // 计时规则 ：1:正计时   2:倒计时
        submit: 0, // 用户是否已提交  1:是   0:否
        next_method: 1, // 进入下一题：1:选择后自动进入   2手动点击
        answerLeaveMillionSecs: 0, // 用户中断时间
    });
    const monitorStore = useMonitorStore();
    const endTimeStore = useEndTimeStore();

    // 消耗用时倒计时
    const answeringTimeCountdown = createTimer();
    // 消耗用时正计时
    const answeringTime = createTimer();

    const initData = (data: any) => {
        const { questionList, answerLeaveMillionSecs, paperReviewRuleType, paperTimedRuleType, paperSubmit, paperNextQuestionMethod, questionGroupGuideInfo } = data || {};

        const newQuestionList = <any[]>[];
        const newBackgroundInfoList = <any[]>[];
        let newQuestionGroupInfo: any = null;

        // 处理题组信息
        if (questionGroupGuideInfo && Object.keys(questionGroupGuideInfo).length) {
            newQuestionGroupInfo = questionGroupGuideInfo;
        }

        questionList.forEach((item: any) => {
            const { questionSnapshot, encryptId, children, answerStatus } = item;
            const { questionType } = questionSnapshot;
            const fullName = questionTypeMap[questionType]?.fullName ?? '';
            const isHasGroupInfo = !!Object.keys(newQuestionGroupInfo || {}).length; // 是否有题组信息

            // 自定义数据
            const itemData = {
                encryptId,
                ...questionSnapshot, // 题组信息
                questionType, // 题组类型
                serialNumber: newQuestionList.length + 1, // 序号
                questionTypeName: fullName, // 题型前端显示名称
                status: answerStatus, // 当前答题状态
            };

            if ([11, 20].includes(questionType) && children && children.length) {
                const backgroundInfoId = uniqueId('backgroundInfo_');
                newBackgroundInfoList.push({
                    ...questionSnapshot,
                    encryptId: backgroundInfoId,
                });

                children.forEach((obj: any, index: number) => {
                    newQuestionList.push({
                        encryptId: obj.encryptId,
                        serialNumber: itemData.serialNumber + index,
                        status: obj.answerStatus,
                        questionType: obj.questionType || 1, // 题组类型
                        backgroundInfoEncryptId: backgroundInfoId, // 绑定背景信息ID
                        backgroundInfoType: questionType, // 父级信息题组类型
                        backgroundInfoChildrenSerialNumber: index + 1, // 背景信息子项索引
                        answerContent: '',
                        ...obj.questionSnapshot,
                        isHasGroupInfo,
                    });
                });
            } else {
                newQuestionList.push({
                    ...itemData,
                    isHasGroupInfo,
                });
            }
        });

        // 格式化数据以后，计算所有已答题答案；
        const currentIndex = newQuestionList.findIndex((item) => item.status === 0);
        currentQuestionIndex.value = currentIndex < 0 ? newQuestionList.length - 1 : currentIndex;

        allQuestionList.value = newQuestionList;
        backgroundInfoList.value = newBackgroundInfoList;
        questionGroupInfo.value = newQuestionGroupInfo;
        config.value = {
            answerLeaveMillionSecs,
            reviewRuleType: paperReviewRuleType,
            timedRuleType: paperTimedRuleType,
            submit: paperSubmit,
            next_method: paperNextQuestionMethod,
        };

        // 计算答题时间 正计时与倒计时
        initTime(data.countDownEndTimeTs, data.timingStartTimeTs, data.examEndTimeTs);
    };

    // 计算答题时间 正计时与倒计时
    const initTime = (countDownEndTimeTs: number, timingStartTimeTs: number, examEndTimeTs: number) => {
        // 设置最晚交卷时间;
        endTimeStore.initExamEndTime(examEndTimeTs);

        // 预览没有结束时间，则倒计时为建议答题时间
        answeringTimeCountdown.start({
            key: '答题倒计时',
            finishTime: () => countDownEndTimeTs,
            onFinished: async () => {
                // 场次倒计时结束
            },
        });

        // 消耗用时正计时
        answeringTime.start({
            key: '场次正计时结束',
            beginTime: () => timingStartTimeTs,
        });
    };

    // 获取数据
    const getData = async (query: { encryptExamId: string }) => {
        loading.value = true;
        isPreview.value = false;
        const { code, data } = await Invoke.exam.getEvaluationInfo({
            ...query,
        });

        if (code !== 0) {
            return;
        }

        initData(data);
        loading.value = false;
    };

    const jumpToQuestion = (index: number) => {
        if (index >= allQuestionList.value.length) {
            return;
        }
        currentQuestionIndex.value = index;
    };

    const setAnswer = (encryptId: string, answerContent: string) => {
        const question = allQuestionList.value.find((item) => item.encryptId === encryptId);
        if (question) {
            const config = questionTypeMap[question.questionType];
            if (config?.answersType && config?.answersType === 'array') {
                question.answerContentList = answerContent;
            } else {
                question.answerContent = answerContent;
            }

            question.status = 1;
        }
    };

    const getPreviewQuestion = async (params: any) => {
        const { code, data } = await Invoke.preview.getAnswerList({ ...params });
        if (code === 0 && data) {
            initData({
                ...data,
            });
        } else {
            // showFailToast('获取预览数据失败');
        }
    };

    const postCommitAnswer = async (params: any) => {
        if (isPreview.value) {
            setAnswer(params.evaPaperSnapshotId, params.answerContent);
            return true;
        }

        const { examId } = monitorStore.examBaseInfo.examInfo || {};

        const { code } = await Invoke.common.postFormAddAnswer({
            encryptExamId: examId,
            ...params,
        });

        if (code === 0) {
            setAnswer(params.evaPaperSnapshotId, params.answerContent);
            return true;
        } else {
            return false;
        }
    };

    return {
        answeringTimeCountdown,
        answeringTime,
        loading,
        isPreview,
        allQuestionList,
        currentQuestionIndex,
        currentQuestion,
        backgroundInfoList,
        config,
        sceneDetailPopupShow,
        questionGroupInfo,
        getData,
        jumpToQuestion,
        setAnswer,
        initData,
        getPreviewQuestion,
        postCommitAnswer,
    };
});
