<template>
    <div class="question-panel-wrap">
        <div class="question-panel-header">
            <p class="tit">未答题目</p>
            <div class="tag-info-wrap">
                <div class="tag-wrap">
                    <p class="question-panel-item icon active"></p>
                    <p>正在作答</p>
                </div>
                <div class="tag-wrap">
                    <p class="question-panel-item icon"></p>
                    <p>未作答</p>
                </div>
            </div>
        </div>
        <div class="question-panel-content">
            <div class="question-panel-item-wrap">
                <p
                    v-for="item in questionList"
                    :key="item.id"
                    class="question-panel-item"
                    :class="{
                        active: item.id === currentQuestion.id,
                    }"
                    @click="onSwitchQuestion(item)"
                >
                    {{ item.serialNumber }}
                </p>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { usePaperStore } from '../store';
const emit = defineEmits(['switchQuestion']);
const paperStore = usePaperStore();

const questionList = computed(() => {
    return paperStore.allQuestionList.filter((item: { status: number }) => item.status === 0);
});

const currentQuestion = computed(() => {
    return paperStore.currentQuestion;
});

const onSwitchQuestion = (data: any) => {
    emit('switchQuestion', data);
};
</script>

<style lang="less" scoped>
.question-panel-wrap {
    height: 100%;
    padding: 16px;
    background-color: #ffffff;
    border-radius: 12px 12px 0 0;
    font-style: normal;
}

.question-panel-header {
    width: 100%;
    display: flex;
    justify-content: space-between;

    .tit {
        flex: 1;
        color: #292929;
        font-size: 14px;
        font-weight: 500;
        line-height: 26px;
    }

    .tag-info-wrap {
        display: flex;

        .tag-wrap {
            display: flex;
            align-items: center;
            color: #5e5e5e;
            font-size: 13px;
            font-weight: 400;
            line-height: 18px;

            .icon {
                margin-right: 6px;
            }

            &:last-child {
                margin-left: 6px;
            }
        }
    }
}
.question-panel-content {
    margin-top: 10px;
    height: 100%;
    overflow: auto;

    .question-panel-item-wrap {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
        padding-bottom: 40px;
    }
}
.question-panel-item {
    width: 36px;
    height: 36px;
    background-color: #f7f8fb;
    border-radius: 8px;
    text-align: center;
    color: #292929;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 36px;

    &.icon {
        width: 10px;
        height: 10px;
        border-radius: 2px;

        &:hover {
            cursor: default;
        }
    }

    &.active {
        background: rgba(0, 166, 167, 0.1);
        border: 1px solid var(--primary-color);
        color: var(--primary-color);
    }

    &:hover {
        background: rgba(0, 166, 167, 0.1);
        cursor: pointer;
    }
}
</style>
