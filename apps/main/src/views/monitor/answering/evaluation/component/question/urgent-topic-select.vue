<template>
    <div class="single-choice-question-wrap">
        <QuestionTitle :title="questionTitle" :questionInfo="questionInfo" :isForceSelect="true" />
        <div class="single-choice-wrap">
            <div class="single-choice-item" v-for="item of questionOption" :key="item.encryptId" @click="changeAnswer(item)">
                <OptionItem
                    :imgList="item.files"
                    :isChecked="!!findCheckedList(item.encryptId)"
                    :icon="String(findCheckedList(item.encryptId))"
                    :content="`${item.letters}. ${item.optionContent}`"
                />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import QuestionTitle from './component/question-title.vue';
import OptionItem from './component/option-item.vue';

const props = defineProps({
    questionInfo: {
        type: Object,
        default: () => ({}),
    },
    answer: {
        type: [String, Array],
        default: () => '',
    },
});

const questionOption = ref<any[]>([]);
const checked = ref<any[]>([]);

const questionTitle = computed(() => {
    const title = props.questionInfo.questionTitle;

    const titStr = title.replace(/___/g, `<span class="answer"></span>`);

    return titStr;
});

function findCheckedList(id: string) {
    let flag = 0;
    const falgIndex = checked.value.findIndex((item) => item?.id === id);
    if (falgIndex > -1) {
        flag = falgIndex + 1;
    }
    return flag;
}

async function changeAnswer(data: any) {
    const { encryptId, letters } = data || {};

    const flagIndex = checked.value.findIndex((item) => item?.id === encryptId);

    if (flagIndex > -1) {
        checked.value[flagIndex] = { letters: '', id: '' };
        return;
    }

    for (let i = 0; i < checked.value.length; i++) {
        const id = checked.value[i]?.id || '';

        if (id === '') {
            checked.value[i] = {
                letters,
                id: encryptId,
            };
            break;
        }
    }
}

function checkAnswer() {
    const { encryptId, questionType } = props.questionInfo;
    let flag = true;
    let message = '';

    const data = checked.value.find((item) => item.id === '');

    if (data) {
        message = '请选择完所有的题目';
        flag = false;
    }
    const params = {
        evaPaperSnapshotId: encryptId,
        questionType,
        answerContent: checked.value.map((i: any) => i.id),
    };

    return [flag, message, params];
}

watch(
    () => props.questionInfo,
    (data) => {
        const questionList: any[] = [];
        const { questionOptionList, needSelectedCount } = data;
        // 丰富选项数据 满足展示条件
        if (questionOptionList && questionOptionList.length) {
            questionOptionList.forEach((item: { encryptId: any; optionContent: any }, index: number) => {
                const { encryptId, optionContent } = item;
                const letters = String.fromCharCode(64 + Number.parseInt(String(index + 1), 10));
                questionList.push({
                    encryptId,
                    optionContent,
                    letters,
                });
            });
        }

        questionOption.value = [...questionList];

        // 回显答案
        const checkedList = Array.from({ length: needSelectedCount });
        const currentAnswerVal = props.answer || [];
        for (let i = 0; i < checkedList.length; i++) {
            const answerId = currentAnswerVal && currentAnswerVal.length ? currentAnswerVal[i] : false;

            let data = { letters: '', id: '' };

            if (answerId) {
                const questionData = questionList.find((item) => item.encryptId === answerId);
                if (questionData) {
                    data = { letters: questionData.letters ?? '', id: answerId as string };
                }
            }

            checkedList[i] = data;
        }

        checked.value = [...checkedList];
    },
    { immediate: true },
);

watch(
    () => checked.value,
    async () => {
        setTimeout(() => {
            const doms = document.querySelectorAll('.answer');

            checked.value.forEach((data, index) => {
                if (data && doms[index]) {
                    doms[index].innerHTML = data.letters || '&nbsp';
                }
            });
        }, 0);
    },
    { deep: true, immediate: true },
);

defineExpose({
    check: checkAnswer,
});
</script>

<style lang="less" scoped>
.single-choice-wrap {
    .single-choice-item {
        margin-bottom: 12px;

        &:last-child {
            margin-bottom: 20px;
        }
    }
}
</style>
