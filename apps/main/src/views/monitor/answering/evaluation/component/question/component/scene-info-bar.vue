<template>
    <div class="scene-info-wrap">
        <div class="info">
            <p class="name">情景题</p>
            <p class="number">
                （<span>{{ paperStore.currentQuestion.backgroundInfoChildrenSerialNumber }}</span>
                <span>/</span>
                <span>{{ childList.length }}</span
                >）
            </p>
        </div>
        <VanSticky :offsetTop="70" @change="stickyStatusChange">
            <p
                class="btn-tag"
                :class="{
                    fixed: tagFixed,
                }"
                @click="openDetailPopup"
            >
                <span class="icon" />
                背景信息
            </p>
        </VanSticky>
    </div>
</template>

<script setup lang="ts">
import { usePaperStore } from '../../../store';
import { computed, ref } from 'vue';

const paperStore = usePaperStore();

const childList = computed(() => {
    const { allQuestionList, currentQuestion } = paperStore;
    const { backgroundInfoEncryptId } = currentQuestion;
    const list = allQuestionList.filter((item) => item.backgroundInfoEncryptId === backgroundInfoEncryptId);
    return list;
});

const tagFixed = ref(false);

function stickyStatusChange(isFixed: boolean) {
    tagFixed.value = isFixed;
}

function openDetailPopup() {
    paperStore.sceneDetailPopupShow = true;
}
</script>

<style lang="less" scoped>
.scene-info-wrap {
    margin-right: -20px;
    display: flex;
    padding-bottom: 12px;

    .info {
        flex: 1;
        display: flex;

        .name {
            font-size: 18px;
            font-weight: 600;
            color: #141414;
            line-height: 26px;
        }

        .number {
            font-size: 12px;
            font-weight: 400;
            color: #858585;
            line-height: 26px;
        }
    }

    .btn-tag {
        width: 90px;
        display: flex;
        align-items: center;
        padding: 5px;
        background: #fff;
        font-size: 14px;
        font-weight: 400;
        color: #0d9ea3;
        line-height: 20px;
        transition: all 0.2s;
        border-radius: 4px 0 0 4px;

        &.fixed {
            background: #d5f3f4;
        }

        .icon {
            margin-right: 4px;
            width: 16px;
            height: 16px;
            background: url('https://img.bosszhipin.com/static/file/2024/flphkvxqbl1715328607333.png.webp');
            background-size: auto 100%;
        }
    }
}
</style>
