import forcedChoice from './component/question/forced-select.vue';
import InputAnswersMultiple from './component/question/input-answers-multiple.vue';
import InputAnswers from './component/question/input-answers.vue';
import questionGroupInfo from './component/question/question-group-info.vue';
// import sceneQuestion from './components/question/scene-question.vue';
import singleChoice from './component/question/single-choice.vue';
import sortQuestion from './component/question/sort-question.vue';
import UrgentTopicSelect from './component/question/urgent-topic-select.vue';

export const QUESTION_TYPE: any = {
    CHOICE: 1,
    SCALES: 9,
    FORCED: 10,
    SCENE: 11,
    SORT: 15,
    URGENT_TOPIC_SELECT: 16,
    INPUT_ANSWERS: 18,
    INPUT_ANSWERS_MULTIPLE: 19,
    DATA_ANALYSIS: 20, // 资料分析题
};

export const questionTypeMap: any = {
    [QUESTION_TYPE.CHOICE]: {
        fullName: '单选题',
        shortName: '单选',
        templateName: singleChoice,
    },
    [QUESTION_TYPE.SCALES]: {
        fullName: '量表题',
        shortName: '量表',
        templateName: singleChoice,
    },
    [QUESTION_TYPE.FORCED]: {
        fullName: '迫选题',
        shortName: '迫选题',
        templateName: forcedChoice,
        answersType: 'array',
    },
    [QUESTION_TYPE.SCENE]: {
        fullName: '情景题',
        shortName: '情景题',
        // templateName: sceneQuestion,
    },
    [QUESTION_TYPE.URGENT_TOPIC_SELECT]: {
        fullName: '迫选题',
        shortName: '迫选题',
        templateName: UrgentTopicSelect,
        answersType: 'array',
    },
    [QUESTION_TYPE.SORT]: {
        fullName: '排序题',
        shortName: '排序题',
        templateName: sortQuestion,
        answersType: 'array',
    },
    [QUESTION_TYPE.INPUT_ANSWERS]: {
        fullName: '问答题',
        shortName: '问答',
        templateName: InputAnswers,
    },
    [QUESTION_TYPE.INPUT_ANSWERS_MULTIPLE]: {
        fullName: '多选问答题',
        shortName: '多选问答',
        templateName: InputAnswersMultiple,
    },
    [QUESTION_TYPE.DATA_ANALYSIS]: {
        fullName: '资料分析题',
        shortName: '资料分析',
    },
    group: {
        templateName: questionGroupInfo,
    },
};

export enum PAPER_REVIEW_TYPE {
    可回看一题 = 1,
    不可回看 = 2,
    不限制回看 = 3,
}

export const paperQuestionNextMethod = {
    auto: 1,
    manual: 2,
};

export const EVALUATION_PREVIEW_IMAGE_KEY = 'EVALUATION_PREVIEW_IMAGE_KEY';
