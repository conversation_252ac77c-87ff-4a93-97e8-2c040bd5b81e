import { random } from 'lodash-es';

// 回答区域组件
import PlanQuestion from './component/plan-question.vue';

// 问题区域映射
import Workout from './question/workout.vue';
import Plant from './question/plant.vue';
import Gene from './question/gene.vue';
import Coupon from './question/coupon.vue';
import ConnectLines from './answer/connect-lines/index.vue';

export const QUESTION_TYPE: any = {
    CHOICE: 1,
    SCALES: 9,
    FORCED: 10,
    SCENE: 11,
    HOTS_LIGATURE: 13, // 高阶思想 - 连线题
    HOTS_SCHEME: 14, // 高阶思想 - 方案题
};

export const answerTypeMap: any = {
    [QUESTION_TYPE.HOTS_LIGATURE]: {
        fullName: '连线题',
        shortName: '连线',
        templateName: ConnectLines,
    },
    [QUESTION_TYPE.HOTS_SCHEME]: {
        fullName: '方案题',
        shortName: '方案',
        templateName: PlanQuestion,
    },
};

const instructionsData = {
    [QUESTION_TYPE.HOTS_LIGATURE]: '单击某一方块，选择“加强/减弱”，再单击另一方块，即可完成连线，单击“线条”可删除连线。',
    [QUESTION_TYPE.HOTS_SCHEME]: '单击方案所在行，即为选中，再次点击，取消选中。',
};

// 1= 普通  2=基因 3=大棚  4=购物狂欢
export const questionTypeMap: any = {
    1: {
        templateName: Workout,
        instructions: {
            ...instructionsData,
        },
    },
    2: {
        templateName: Gene,
        instructions: {
            ...instructionsData,
        },
    },
    3: {
        templateName: Plant,
        instructions: {
            ...instructionsData,
        },
    },
    4: {
        templateName: Coupon,
        instructions: {
            ...instructionsData,
            [QUESTION_TYPE.HOTS_LIGATURE]: '单击某一方块，再单击另一方块，即可完成连线，单击“线条”可删除连线。',
        },
    },
};

// 模拟生成训练结果
export function previewData({ history = <any>[], optinon = <any>[], result = <any>[], childQuestionId = '' }) {
    const newHistoryList = [...history];
    const newResult = [...result];
    const num = newHistoryList[0]?.num || 0;

    const historyItem = {
        encryptId: `encryptId${num + 1}`,
        childQuestionId,
        num: num + 1,
        input: [] as any[],
        output: [] as any[],
    };

    optinon.forEach((item: any, index: number) => {
        if (!item) {
            return;
        }

        // 输入参数
        const { paramName, showName, value } = item;

        // 结果赋值
        const resultListItem = newResult[index];
        const historyList = [...(resultListItem?.historyList || [])];

        // 模拟单项返回结果
        const val = random(1, 10);
        if (historyList.length >= 10) {
            historyList.shift();
        }

        historyList.push(val);

        if (newResult[index]) {
            newResult[index] = {
                ...resultListItem,
                maxValue: 10,
                value: val,
                historyList,
            };
        }

        // 单次输入记录
        const inputItem = {
            paramName,
            showName,
            paramValue: value,
        };

        // 单次输出记录
        const outputItem = {
            paramName: resultListItem?.paramName,
            showName: resultListItem?.showName,
            paramValue: val,
        };

        historyItem.input.push(inputItem);

        if (outputItem.paramName) {
            historyItem.output.push(outputItem);
        }
    });

    if (newHistoryList.length >= 10) {
        newHistoryList.pop();
    }
    newHistoryList.unshift(historyItem);

    return {
        history: newHistoryList,
        result: newResult,
    };
}
