<template>
    <div class="switch-btn" :class="{ checked: modelValue }" @click="onSwitch">
        <span class="text">OFF</span>
        <span class="text">ON</span>
        <span class="btn">{{ modelValue ? 'ON' : 'OFF' }}</span>
    </div>
</template>

<script setup lang="ts">
const props = defineProps({
    modelValue: {
        type: Number,
        default: 0,
    },
});
const emits = defineEmits(['update:modelValue']);
function onSwitch() {
    emits('update:modelValue', Number(!props.modelValue));
}
</script>

<style lang="less" scoped>
.switch-btn {
    cursor: pointer;
    margin: 0 auto;
    padding: 0 6px;
    position: relative;
    display: flex;
    justify-content: space-between;
    width: 72px;
    height: 18px;
    border: 1px solid #dfdfdf;
    border-radius: 12px;
    background: rgb(239, 239, 239);

    &.checked {
        background: var(--primary-color-6);
        border: 1px solid var(--primary-color-6);

        .text {
            color: rgb(112, 201, 200);
        }
        .btn {
            left: calc(100% + 1px);
            transform: translateX(-100%);
        }
    }

    .text {
        flex: 1;
        text-align: center;
        color: rgb(152, 152, 152);
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 18px;
    }

    .btn {
        transition: all 0.25s;
        position: absolute;
        top: -5px;
        left: -1px;
        width: 36px;
        padding: 3px 0;
        height: 27px;
        background: #fff;
        border-radius: 14px;
        border: 1px solid #dfdfdf;
        text-align: center;
        color: #000000;
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        line-height: 20px;
    }
}
</style>
