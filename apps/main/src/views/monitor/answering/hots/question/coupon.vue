<template>
    <div class="question-option" v-if="data.inputParamList.length">
        <div class="option-wrap">
            <div v-for="(item, index) in optionList" class="option-item" :key="item.groupName">
                <p class="group-name" :style="{ background: colorConfig[index]?.color }">{{ item.groupName }}</p>
                <div class="group-info-wrap">
                    <template v-for="obj in item.list" :key="obj.showName">
                        <div class="info-wrap" @click="onChecked(obj)" :class="{ checked: !!obj.value }">
                            <div class="info">
                                <p class="tit">{{ obj.showName }}</p>
                                <div class="money-info">
                                    <div class="money" style="line-height: 56px"><span>¥</span>{{ obj.paramPrice }}</div>
                                    <SvgIcon :name="colorConfig[index]?.icon" class="icon" :width="56" :height="56"></SvgIcon>
                                </div>
                            </div>
                            <p class="btn">{{ !!obj.value ? '已加购' : '购买' }}</p>
                            <SvgIcon name="box-checked" class="checked-icon" :width="21" :height="20"></SvgIcon>
                        </div>
                    </template>
                </div>
            </div>
        </div>
        <div class="btn-line-wrap">
            <b-button @click="onClickMockCommit" class="start-btn" type="primary" shape="round">{{ data.mockButtonName }}</b-button>
        </div>
        <div class="result-wrap">
            <div class="info-item" v-for="(item, index) in resultList" :key="item.paramName">
                <div class="info">
                    <SvgIcon v-if="resultConfig[index]" :name="resultConfig[index]?.icon" class="icon" :width="24" :height="24"></SvgIcon>
                    <p>{{ item.showName }}</p>
                </div>
                <div class="money-info">
                    <span class="desc">减</span>
                    <div class="money"><span>¥</span>{{ item.historyList[item.historyList.length - 1] || 0 }}</div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { throttle } from 'lodash-es';
import { useHotsStore } from '../store';
const hotsStore = useHotsStore();

const emits = defineEmits(['onMockCommit']);
const {
    data,
    resultList,
    lastInput,
    isPreview: _isPreview = false,
} = defineProps<{
    data: any;
    resultList: any[];
    lastInput: any[];
    isPreview?: boolean;
}>();

const optionList = ref<any[]>([]);
const colorConfig = [
    {
        color: '#FFF0CC',
        icon: 'clothing',
    },
    {
        color: '#FFE7E5',
        icon: 'cosmetics',
    },
    {
        color: '#E5F8FF',
        icon: 'shampoo',
    },
] as const;

const resultConfig = [
    {
        icon: 'full-reduction',
    },
    {
        icon: 'store-optimization',
    },
    {
        icon: 'red-envelope',
    },
    {
        icon: 'vip',
    },
];

const parentData = computed(() => {
    const { currentQuestion, sceneQuestionData } = hotsStore;
    const { parentEncryptId } = currentQuestion;
    return sceneQuestionData[parentEncryptId];
});

const onChecked = (data: any) => {
    data.value = Number(!data.value);
};

// 模拟训练
const onClickMockCommit = throttle(async () => {
    const { currentQuestion } = hotsStore;
    let input = <any>[];
    optionList.value.forEach((item) => {
        if (item.list && item.list.length) {
            item.list.forEach((obj: any) => {
                input.push({
                    paramName: obj.paramName,
                    paramValue: obj.value,
                });
            });
        }
    });

    emits('onMockCommit', {
        // encExamId: basePaperStore.encExamId,
        // encSeqTmpId: basePaperStore.encSeqTmpId,
        questionId: parentData.value.data.id,
        childQuestionId: currentQuestion.id,
        input,
    });
}, 500);

watch(
    () => lastInput,
    (list) => {
        // 设置最后一次入参
        if (list.length) {
            list.forEach((item) => {
                optionList.value.forEach((element, _index) => {
                    if (element.list && element.list.length) {
                        const data = element.list.find((obj: any) => obj.paramName === item.paramName);
                        if (data) {
                            data.value = item.paramValue;
                        }
                    }
                });
            });
        }
    },
    {
        deep: true,
        immediate: true,
    },
);

watch(
    () => data,
    (_val) => {
        let list = <any>[];
        data.inputParamList.forEach((item: any, _index: number) => {
            let classIndex = list.findIndex((obj: any) => obj.groupName === item.groupName);
            const itemInfo = {
                ...item,
                value: 0,
            };

            if (classIndex === -1) {
                list.push({
                    groupName: item.groupName,
                    list: [itemInfo],
                });
            } else {
                list[classIndex].list.push({ ...itemInfo });
            }
        });
        optionList.value = list;
    },
    {
        deep: true,
        immediate: true,
    },
);
</script>

<style lang="less" scoped>
.question-option {
    font-family: 'PingFang SC';
    display: flex;
    justify-content: space-between;

    .option-wrap {
        position: relative;
        flex: 1;

        .option-item {
            margin-bottom: 24px;
            display: flex;

            &:last-child {
                margin-bottom: 0;
            }

            .group-name {
                width: 46px;
                padding: 0 12px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                text-align: center;
                border-radius: 8px;
                color: #1f1f1f;
                font-size: 14px;
                font-style: normal;
                font-weight: 500;
                line-height: 20px;
            }

            .group-info-wrap {
                flex: 1;
                margin-left: 20px;
                display: flex;

                .info-wrap {
                    position: relative;
                    margin-right: 20px;
                    width: 90px;
                    border: 1px solid #f3f3f3;
                    border-radius: 8px;
                    background: #fff;
                    text-align: center;
                    overflow: hidden;
                    transition: all 0.2s;
                    cursor: pointer;
                    user-select: none;

                    &.checked {
                        border: 1px solid var(--primary-color-6);

                        .checked-icon {
                            display: block;
                            top: -1px;
                        }

                        .btn {
                            color: #fff;
                            background: var(--primary-color-6);
                        }
                    }

                    &:last-child {
                        margin-right: 0;
                    }

                    .checked-icon {
                        position: absolute;
                        display: none;
                        top: -1px;
                        left: 0;
                        z-index: 2;
                    }

                    .info {
                        padding: 12px 0 3px;

                        .tit {
                            color: #4d4d4d;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: 400;
                            line-height: 22px;
                            text-align: center;
                        }

                        .money-info {
                            position: relative;
                            top: 0;
                            left: 0;
                            margin: 0 auto;
                            height: 56px;
                            width: 56px;

                            .icon {
                                position: absolute;
                                top: 0;
                                left: 0;
                                width: 56px;
                                height: 56px;
                            }
                        }
                    }

                    .btn {
                        padding: 5px 0;
                        color: #00abac;
                        background: #e7f9f9;
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 500;
                        line-height: 22px;
                        text-align: center;
                    }
                }
            }
        }
    }

    .btn-line-wrap {
        position: relative;
        padding: 28px;
        width: 65px;
        display: flex;
        justify-content: center;

        &::before {
            content: '';
            height: 100%;
            width: 1px;
            border-right: 1px dashed #dfdfdf;
        }

        .start-btn {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translateX(-50%) translateY(-50%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            width: 64px;
            height: 64px;
            background: linear-gradient(108.057274deg, #00a5a6 6%, #00bfc0 91%);
            border: none;
            box-shadow:
                inset 1px 1px 3px 0px rgba(255, 255, 255, 0.32),
                2px 2px 4px 0px rgba(0, 168, 169, 0.2),
                -3px -2px 4px 0px #ffffff;
            border-radius: 66px;
            color: #ffffff;
            font-family: 'PingFang SC';
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            text-align: center;
        }
    }

    .result-wrap {
        margin-left: 32px;
        padding-top: 40px;
        width: 354px;
        height: 414px;
        background: #ffffff;
        border: 1px solid #ececec;
        border-radius: 8px;
        overflow: hidden;
        // min-width: 375px;
        // margin-left: 49px;

        .info-item {
            padding: 0 32px;
            display: flex;
            margin-bottom: 42px;

            &:last-child {
                margin-bottom: 0;
                height: 100px;
                background: #f7f7f7;
                border-radius: 0 0 8px 8px;

                .money-info {
                    .desc {
                        display: none;
                    }
                }
            }

            .info,
            .money-info {
                display: flex;
                align-items: center;
            }

            .info {
                flex: 1;

                color: #1f1f1f;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 20px;

                .icon {
                    margin-right: 10px;
                }
            }

            .money-info {
                .desc {
                    transform: translateY(1px);
                    margin-right: 5px;
                    color: #1f1f1f;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 500;
                    line-height: 20px;
                    text-align: center;
                }
            }
        }
    }
}

.money {
    position: relative;
    z-index: 2;
    color: #1f1f1f;
    font-family: 'Kanzhun';
    font-size: 24px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    line-height: 24px;

    span {
        font-size: 18px;
    }
}
</style>
