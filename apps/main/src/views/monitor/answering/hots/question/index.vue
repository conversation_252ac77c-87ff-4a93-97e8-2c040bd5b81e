<template>
    <div v-if="parentData" class="question-content-wrap" :style="{ backgroundImage: `url(${parentData.data.backgroundImage})` }">
        <p class="question-title">{{ parentData.index }}、{{ parentData.data.questionTitle }}</p>
        <p class="question-desc">
            {{ parentData.data.backgroundInfo }}
        </p>
        <div class="question-option-wrap">
            <div class="question-option-content">
                <component
                    :is="config.templateName"
                    :isPreview="isPreview"
                    :data="parentData.data.product10QuestionParamVO"
                    :resultList="resultList"
                    :lastInput="lastInput"
                    @onMockCommit="onMockCommit"
                />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { QUESTION_TYPE, previewData } from '../constant';
import { useHotsStore } from '../store';

const props = defineProps({
    isPreview: {
        type: Boolean,
        default: false,
    },
    config: {
        type: Object,
        default: () => ({}),
    },
    encExamId: {
        type: String,
        default: '',
    },
    encSeqTmpId: {
        type: String,
        default: '',
    },
});

const hotsStore = useHotsStore();
const { setCurrentHistoryScheme } = hotsStore;

const parentData = computed(() => {
    const { currentQuestion, sceneQuestionData } = hotsStore;
    const { parentEncryptId } = currentQuestion;
    return sceneQuestionData[parentEncryptId];
});

const resultList = ref<any[]>([]);
const lastInput = ref();
// 模拟训练
async function onMockCommit(query: any) {
    const { inputParamList } = parentData.value.data.product10QuestionParamVO;

    if (props.isPreview) {
        // previewData();
        const { history, result } = previewData({
            history: hotsStore.currentHistoryScheme,
            optinon: inputParamList,
            result: [...resultList.value],
            childQuestionId: hotsStore.currentQuestion.id,
        });
        setPlanHistory(history);
        resultList.value = result;
        return;
    }

    const { code, data, message } = await Invoke.hots.postHotsMockCommit({
        encryptExamId: props.encSeqTmpId,
        ...query,
    });

    if (code !== 0) {
        Toast.danger(message);
        return;
    }

    setPlanHistory(data.history);
    formatResultList(data.outputAgg);
}

// 设置方案题
function setPlanHistory(list: any) {
    const { questionType } = hotsStore.currentQuestion;

    // 设置最后一次入参
    if (list.length && list[0]) {
        lastInput.value = list[0]?.input || [];
    }

    if (questionType === QUESTION_TYPE.HOTS_SCHEME) {
        setCurrentHistoryScheme(list);
    } else {
        setCurrentHistoryScheme([]);
    }
}

// 获取历史 模拟训练
async function getHotsMockCommitLog() {
    const { code, data } = await Invoke.hots.getHotsMockCommitLog({
        encryptExamId: props.encSeqTmpId,
        questionId: parentData.value.data.id,
    });

    if (code !== 0) {
        return;
    }

    setPlanHistory(data.history);
    formatResultList(data.outputAgg);
}

// 格式化结果数据
function formatResultList(list: any[] = []) {
    const newList = parentData.value.data.product10QuestionParamVO.outputParamList.map((item: any) => {
        let value = 0;
        let historyList: string | any[] = [];

        const currentData = list.find((element: any) => {
            return element.paramName === item.paramName;
        });

        if (currentData && currentData.paramValues.length) {
            historyList = [...currentData.paramValues];
            value = historyList[historyList.length - 1];
        }

        return {
            ...item,
            value,
            historyList,
        };
    });

    resultList.value = newList;
}

watch(
    () => hotsStore.currentQuestion,
    (_val) => {
        if (!props.isPreview) {
            getHotsMockCommitLog();
        }
    },
    {
        deep: true,
        immediate: true,
    },
);

watch(
    () => parentData.value,
    (_val) => {
        if (props.isPreview) {
            formatResultList([]);
            setPlanHistory([]);
        }
    },
    {
        deep: true,
        immediate: true,
    },
);
</script>

<style lang="less" scoped>
.question-content-wrap {
    padding: 20px;
    color: #1f1f1f;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    background-size: 100% auto;
    background-position: center bottom;
    background-repeat: no-repeat;
    height: 100%;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;

    .question-title {
        font-size: 20px;
        font-weight: 600;
    }

    .question-desc {
        margin: 12px 0 12px;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        white-space: pre-wrap;
    }

    .question-option-wrap {
        padding: 20px 0;

        .question-option-content {
            margin: 0 auto;
            width: 748px;
        }
    }
}
</style>

<style lang="less">
.b-tooltip {
    transform: translateY(1px);

    &.b-trigger-popup {
        // background-color: red !important;

        .b-trigger-popup-wrapper {
            // background-color: red !important;

            .b-trigger-content {
                background-color: #caebeb !important;
                padding: 0px 10px;
                min-width: 45px;
                color: #1f1f1f;
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                border: 1px solid #00a6a7;
                border-radius: 22px;
                text-align: center;
            }

            .b-tooltip-dark-popup-arrow {
                z-index: 2;
                background-color: red !important;
                background-color: #caebeb !important;
                // box-shadow: 0 1px 0 #00a6a7;
                border-bottom: 1px solid #00a6a7;
                border-right: 1px solid #00a6a7;
            }
        }
    }
}
</style>
