import { useDebuggingStore } from '../../store';

export default function useMonitor() {
    const debuggingStore = useDebuggingStore();
    const { hasComputer, hasPhone, hasScreen, STATUS } = debuggingStore;
    if (STATUS.camera.status !== 2 && hasComputer) {
        debuggingStore.openCameraMonitor();
    }
    if (STATUS.phone.status !== 2 && hasPhone) {
        debuggingStore.openPhoneMonitor();
    }
    if (STATUS.screen.status !== 2 && hasScreen) {
        debuggingStore.openScreenMonitor();
    }
}
