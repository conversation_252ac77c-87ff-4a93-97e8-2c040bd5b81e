import type { DialogReturn } from '@boss/design';
import { onUnmounted } from 'vue';
import { DEPLOY_ENV } from '@/shared';
import { logger } from '@/utils/logger';

let isLeaveScreen = false;
let dialogVisible = false;
let shiftDialogRef: DialogReturn;
let switchScreenDialogRef: DialogReturn;

let encExamId = '';
let forceSubmitExam = () => {};

// 添加防抖相关变量
let blurDebounceTimer: number | null = null;
let focusDebounceTimer: number | null = null;
const DEBOUNCE_DELAY = 300; // 防抖延迟时间（毫秒）

// 切屏提示弹窗
function shiftDialog() {
    if (dialogVisible) {
        return;
    }
    dialogVisible = true;
    shiftDialogRef = userCutScreenDialog({
        title: '切换屏幕',
        content: '检测到您刚刚有切换屏幕操作，切换超过一定次数会自动交卷，作答期间请勿离开答题页面！',
        type: 'warning',
        showCancel: false,
        showClose: false,
        layerClosable: false,
        enableEscClose: false,
        confirmText: '知道了',
        close() {
            dialogVisible = false;
        },
    });

    // 埋点开始
    BossAnalyticsTrack('zhice-pc-exam-switch-screen-warn', {
        pData: {
            type: TrackTypeEnum.失败,
            nameZh: '弹出切屏警告弹窗',
        },
    });
    // 埋点结束
}

function closeDialogs() {
    shiftDialogRef?.close();
    switchScreenDialogRef?.close();
}

// 清理防抖定时器
function clearDebounceTimers() {
    if (blurDebounceTimer) {
        window.clearTimeout(blurDebounceTimer);
        blurDebounceTimer = null;
    }
    if (focusDebounceTimer) {
        window.clearTimeout(focusDebounceTimer);
        focusDebounceTimer = null;
    }
}

async function executeFlow(currentSwitchType: number) {
    // 首次进入获取焦点 不进行上报
    if (!isLeaveScreen && currentSwitchType === 1) {
        return false; // 与原逻辑一致，不执行上报则返回 false
    }

    try {
        const paramsQuery = {
            encryptExamId: encExamId,
            switchType: currentSwitchType, // 1:返回屏幕  0:离开屏幕
        };

        const res = await Invoke.exam.postSwitchScreenIncrease(paramsQuery, { noErrorToast: true });

        if (res.code === 101) {
            switchScreenDialogRef = Dialog.open({
                title: '强制交卷',
                content: res.message,
                type: 'warning',
                showCancel: false,
                showClose: false,
                confirmText: '知道了',
            });

            forceSubmitExam(); // 假设 forceSubmitExam 也是异步的或处理后续逻辑

            return true;
        } else if (res.code === 104) {
            shiftDialog();
            return false;
        } else if (res.code === 0) {
            return false; // API 调用成功但无特殊处理，返回 false
        } else {
            // 其他非预期 code
            return false;
        }
    } catch (error: any) {
        logger.error('[switchScreenIncrease Flow Error]', error);
        return false;
    }
}

export async function switchScreenIncrease(switchType: number = 1) {
    // 首次进入获取焦点 不进行上报
    if (!isLeaveScreen && switchType === 1) {
        return;
    }

    try {
        // 执行流程
        return executeFlow(switchType);
    } catch (error: any) {
        // 如果是重复执行错误，记录日志但不抛出异常
        if (error.message && error.message.includes('已经在运行中')) {
            logger.warn('[switchScreenIncrease] 切屏事件上报流程正在执行中，跳过重复请求', { switchType });
            return false;
        }
        // 其他错误继续抛出
        throw error;
    }
}

// 防抖版本的窗口失焦处理函数
function debouncedWindowBlurHandler() {
    // 清除之前的定时器
    clearDebounceTimers();
    blurDebounceTimer = window.setTimeout(() => {
        isLeaveScreen = true;
        switchScreenIncrease(0);

        BossAnalyticsTrack('zhice-pc-exam-paper-blur', {
            pData: {
                type: TrackTypeEnum.失败,
                nameZh: '窗口失去焦点',
            },
        });

        blurDebounceTimer = null;
    }, DEBOUNCE_DELAY);
}

// 防抖版本的窗口获焦处理函数
function debouncedWindowFocusHandler() {
    // 清除之前的定时器
    clearDebounceTimers();

    focusDebounceTimer = window.setTimeout(() => {
        switchScreenIncrease(1);

        BossAnalyticsTrack('zhice-pc-exam-paper-focus', {
            pData: {
                type: TrackTypeEnum.失败,
                nameZh: '窗口获取焦点',
            },
        });

        focusDebounceTimer = null;
    }, DEBOUNCE_DELAY);
}

// 窗口失焦事件 判断当前考生是否切屏
function bindWindowEvents() {
    // 开发环境 不进行切屏监控
    if (DEPLOY_ENV === 'dev') {
        return;
    }

    window.addEventListener('blur', debouncedWindowBlurHandler);
    window.addEventListener('focus', debouncedWindowFocusHandler);
}

// 移除窗口失焦事件
function removeWindowEvents() {
    window.removeEventListener('blur', debouncedWindowBlurHandler);
    window.removeEventListener('focus', debouncedWindowFocusHandler);
    // 清理防抖定时器
    clearDebounceTimers();
}

export default function ({ examId, forceSubmitExamFn }: { examId: string; forceSubmitExamFn: () => void }) {
    encExamId = examId;
    forceSubmitExam = forceSubmitExamFn;

    bindWindowEvents();

    onUnmounted(() => {
        removeWindowEvents();
        closeDialogs();
    });
}
