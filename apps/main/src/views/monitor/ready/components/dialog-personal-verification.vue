<template>
    <b-dialog
        v-model="visible"
        confirmText="同意授权并继续"
        :layerClosable="false"
        :width="800"
        :confirmButtonProps="{ disabled: !checked }"
        @confirm="onConfirm"
        @cancel="onClose"
    >
        <template #title> 人脸验证服务确认 </template>
        <div>
            <div style="margin-bottom: 24px">为了确保参考考生系您本人，需要对您开展实人认证以核验您的身份。如您同意，则点击“同意授权并继续”；如您不同意则点击“取消”。</div>
            <b-checkbox v-model="checked">
                <span>
                    我已阅读并同意
                    <a :href="FACE_VERIFY_SERVICE_AGREEMENT_URL" target="blank" @click="onJump">《人脸验证服务协议》</a>
                </span>
            </b-checkbox>
        </div>
    </b-dialog>
</template>

<script lang="ts" setup>
import { FACE_VERIFY_SERVICE_AGREEMENT_URL } from '@crm/exam-constants';
import { ref, watch } from 'vue';

defineOptions({
    name: 'DialogPersonalVerification',
});

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false,
    },
});
const emit = defineEmits(['confirm', 'update:modelValue']);
const visible = ref(true);
const checked = ref(false);

watch(
    () => props.modelValue,
    () => {
        visible.value = props.modelValue;
    },
    { immediate: true },
);

function onJump() {
    BossAnalyticsTrack('zhice-pc-exam-face-verify');
}

function onReset() {
    visible.value = false;
    checked.value = false;
    emit('update:modelValue', false);
}
function onConfirm() {
    if (!checked.value) {
        return;
    }
    BossAnalyticsTrack('zhice-pc-exam-face-verify-next', {
        pData: { nameZh: '人脸验证服务确认-协议确认' },
    });
    emit('confirm');
    onReset();
}
function onClose() {
    BossAnalyticsTrack('zhice-pc-exam-face-verify-cancel', {
        pData: { nameZh: '人脸验证服务确认-协议关闭' },
    });
    onReset();
}
</script>
