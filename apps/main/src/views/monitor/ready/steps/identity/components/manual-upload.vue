<template>
    <b-upload ref="uiUploadRef" class="manual-upload" drag :limit="1" :autoUpload="false" :defaultFileList="defaultFileList" listType="picture-card" :onChange="onChange">
        <template #default>
            <div class="trigger-box" @click="onPick">
                <SvgIcon name="svg-full-add" width="22" height="22" />
                <div class="ui-upload-text">
                    <div class="hint-text-wrap-main"><span class="upload-main-text">点击上传</span><span class="sep">/</span><span>拖拽到此处</span></div>
                    <div class="hint-text-wrap-sub">支持格式：jpg、jpeg、png，图片最大支持4M</div>
                </div>
            </div>
        </template>
        <template #file="{ file }">
            <img class="item-thumbnail" :src="getDisplayUrl(file.url as string)" alt="" />
            <div v-if="status === 'HAS_TOKESHOT' || status === 'MANUAL_FAIL'" class="item-actions">
                <IconDelete @click="onDelete" />
            </div>
        </template>
    </b-upload>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import { IconDelete } from '@boss/design/es/icon';
import { inject, onBeforeMount, ref } from 'vue';
import { useFaceRecognizeSubmit } from '../hooks/useFaceRecognizeSubmit';
import { useStatus } from '../hooks/useStatus';

// 人身核验的前提是需要姓名&身份证信息完整

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false,
    },
    manualAuditStatus: {
        type: Number as PropType<number | null>,
        default: null,
    },
    manualAuditResult: {
        type: Number as PropType<number | null>,
        default: null,
    },
    manualApplyAttachmentUrl: {
        type: String,
        default: '',
    },
});
const emit = defineEmits(['update:modelValue', 'close']);
const validateNameAndIdentity = inject<() => Promise<boolean>>('validateNameAndIdentity', () => Promise.resolve(false));
const selectedFile = ref<any>(null); // 存放上传文件
const defaultFileList = ref<Array<any>>([]); // 上传文件列表
const uiUploadRef = ref();

function getDisplayUrl(url: string) {
    const regex = /^https?:\/\/[^/]+/;
    // const isDev = import.meta.env?.MODE === 'development'
    const isDev = true;
    const result = isDev ? url.replace(regex, '') : url;
    return result;
}

function getValidateFileFlag(file: File) {
    const isValidFileType = ['image/jpeg', 'image/jpg', 'image/png'].includes(file.type);
    const isLt4M = file.size / 1024 / 1024 <= 4;

    const checkList = [
        {
            name: '文件格式',
            isValid: isValidFileType,
            message: '上传文件只能是jpg、jpeg、png格式!',
        },
        {
            name: '文件大小',
            isValid: isLt4M,
            message: '上传图片大小不能超过 4MB!',
        },
    ];

    for (let i = 0; i < checkList.length; i++) {
        const item = checkList[i];
        if (!item?.isValid) {
            Toast.danger({ content: item?.message || '' });
            return false;
        }
    }

    return true;
}

function onChange(file: any) {
    const raw = file?.raw; // File
    const isValid = getValidateFileFlag(raw);
    status.value = isValid ? 'HAS_TOKESHOT' : 'INIT';

    if (!isValid) {
        // 清空
        uiUploadRef.value.handleRemove(file);
        uiUploadRef.value.clearFiles();
    } else {
        selectedFile.value = file;
        emit('update:modelValue', true); // 确定上传按钮 恢复点击
    }
}

// 点击上传的埋点
function onPick() {
    BossAnalyticsTrack('zhice-pc-exam-identity-help-picture', {
        pData: {
            nameZh: '人工审核上传图片',
        },
    });
}
function onDelete() {
    uiUploadRef.value.handleRemove(selectedFile.value);
    uiUploadRef.value.clearFiles();
    status.value = 'INIT';
    emit('update:modelValue', false);
}

const status = ref('INIT');
function changeStatus() {
    // 首次进人工
    if (!props.manualAuditStatus) {
        status.value = 'INIT';
    } else if (props.manualAuditStatus === 1) {
        status.value = 'MANUAL_VERIFYING';
    } else if (props.manualAuditStatus === 2) {
        if (props.manualAuditResult === 1) {
            status.value = 'MANUAL_SUCCESS';
        } else {
            status.value = 'MANUAL_FAIL';
        }
    }
    if (props.manualApplyAttachmentUrl) {
        defaultFileList.value = [{ url: props.manualApplyAttachmentUrl }];
    }
}

const { updateOriginStatus } = useStatus(changeStatus);

const isLoading = ref(false);
const { doSubmit } = useFaceRecognizeSubmit({ picSourceType: 2 });
async function submit() {
    if (isLoading.value || !(await validateNameAndIdentity())) {
        return;
    }
    // 业务埋点
    // BossAnalyticsTrack('zhice-pc-exam-identity-help-submit', {
    //     pData: {
    //         nameZh: '有效点击发起图片上传人工审核',
    //     },
    // })
    // 校验是否有有效文件
    const file = selectedFile.value!.raw;
    if (!file) {
        Toast.warning({ content: '请先上传图片' });
        return;
    }
    // 标记提交中
    isLoading.value = true;
    // 发起审核
    doSubmit({ file, biz: 'USER_EXAM_FACE_AUDIT' })
        .then(() => {
            emit('update:modelValue', false);
            emit('close');
            updateOriginStatus();
        })
        .catch(() => {
            Toast.danger({ content: '提交人工审核失败' });
        })
        .finally(() => {
            isLoading.value = false;
        });
}

onBeforeMount(() => {
    changeStatus();
});

defineExpose({ submit });
</script>

<style lang="less">
.manual-upload {
    position: relative;
    background: #ecf9f9;
    border-radius: 4px;
    border: 1px dashed #dfdfdf;
    overflow: hidden;
    &:hover {
        border-color: var(--primary-color);
    }
    .b-upload-list {
        margin: 0;
    }
    .b-upload-dragger {
        position: absolute;
        left: 0;
        top: -1px;
        height: 318px;
        display: flex;
        justify-content: center;
        align-items: center;
        border: none;

        .ui-upload-text {
            margin-top: 16px;
            .hint-text-wrap-main {
                font-size: 14px;
                line-height: 20px;
                .upload-main-text {
                    font-weight: 600;
                    color: var(--primary-color);
                }
                .sep {
                    margin: 0 8px;
                }
            }
            .hint-text-wrap-sub {
                font-size: 12px;
                color: #808080;
                line-height: 20px;
                margin-top: 4px;
            }
        }
    }
    .b-upload-list-item {
        position: absolute;
        z-index: 1;
        left: 0;
        top: -1px;
        width: 100%;
        height: 318px;
        margin: 0;
        border: none;
        border-radius: 0;
        background: #fff;
        img {
            position: absolute;
            width: auto;
            height: auto;
            max-width: 100%;
            max-height: 100%;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
        }
        &:hover .item-actions {
            opacity: 1;
        }
        .item-actions {
            display: flex;
            justify-content: center;
            align-items: center;
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            cursor: default;
            text-align: center;
            color: #fff;
            opacity: 0;
            font-size: 20px;
            background-color: rgba(0, 0, 0, 0.5);
            transition: opacity 0.3s;
            svg {
                cursor: pointer;
            }
        }
    }
}
</style>
