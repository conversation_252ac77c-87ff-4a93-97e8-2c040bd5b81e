import { emitter } from '@crm/exam-utils';
import { inject, onBeforeUnmount } from 'vue';

export function useStatus(onStatusChange: () => void) {
    // 注入状态变更方法
    const updateOriginStatus = inject('updateOriginStatus', () => {});

    // 监听状态变更事件
    emitter.on('onVerifyStatusChange', onStatusChange);
    onBeforeUnmount(() => {
        emitter.off('onVerifyStatusChange', onStatusChange);
    });

    return {
        updateOriginStatus,
    };
}
