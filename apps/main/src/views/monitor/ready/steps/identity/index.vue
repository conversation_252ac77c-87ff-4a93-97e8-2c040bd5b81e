<template>
    <div class="setting-inner">
        <div class="setting-inner-title">基本信息</div>
        <div v-if="examConfig.personalVerification" class="setting-inner-desc">系统通过人脸识别技术，结合考试全程监拍，验证考生身份，确保考试的公平、公正</div>
        <div class="setting-inner-content">
            <!-- 个人信息 -->
            <PersonalInfo ref="personInfoRef" class="setting-inner-section" />
            <!-- 人脸认证 -->
            <FaceVerification v-if="examConfig.personalVerification" class="setting-inner-section" @updateClickable="onUpdateClickable" :examId="examId" :seqId="seqId" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { nextTick, onBeforeMount, provide, ref } from 'vue';
import FaceVerification from './face-verification.vue';
import PersonalInfo from './personal-info.vue';
import { useDebuggingStore } from '../../../store';

defineProps({
    stepStatus: {
        type: String,
        default: 'wait',
    },
    examId: {
        type: String,
        default: '',
    },
    seqId: {
        type: String,
        default: '',
    },
});
const emit = defineEmits(['changeStepClickable', 'update:stepStatus']);
const debuggingStore = useDebuggingStore();
const { examConfig } = debuggingStore;

// 步骤触达埋点
BossAnalyticsTrack('zhice-pc-exam-enter-face-validation', {
    pData: { nameZh: '进入人脸认证' },
});

// 人脸验证所需要的协同验证字段name、identity
const personInfoRef = ref();
async function validateField(field: string): Promise<boolean> {
    await nextTick();

    // 添加安全检查，确保组件和formRef都已经准备好
    if (!personInfoRef.value || !personInfoRef.value.formRef) {
        return false;
    }

    const formRef = personInfoRef.value.formRef;
    const res = await formRef.validateField(field);
    if (res) {
        Toast.warning({ content: res[field].message });
        return false;
    }
    return true;
}

async function validateNameAndIdentity(): Promise<boolean> {
    const fields = ['name', 'identity'];

    // 使用 map 收集所有的异步操作
    const validationPromises = fields.map(async (field) => {
        return validateField(field);
    });

    // 等待所有异步操作完成
    const results = await Promise.all(validationPromises);

    // 检查是否所有字段都通过了验证
    return results.every((isValid) => isValid);
}

async function getNextCheckResult(cb: any) {
    // 添加安全检查，确保组件和formRef都已经准备好
    if (!personInfoRef.value || !personInfoRef.value.formRef) {
        emit('update:stepStatus', 'danger');
        cb('个人信息组件未准备好，请稍后重试');
        return false;
    }

    const formRef = personInfoRef.value.formRef;
    const res = await formRef?.validate?.();
    if (!res) {
        emit('update:stepStatus', 'finish');
    } else {
        emit('update:stepStatus', 'danger');
        cb(`请填写完成考生信息`);
    }
    return !res;
}

onBeforeMount(() => {
    // 当前考试没有配置人脸认证
    if (!examConfig.personalVerification) {
        emit('changeStepClickable', true);
    }
});

function onUpdateClickable(hasDone: boolean) {
    emit('changeStepClickable', hasDone);
}

// 依赖注入
provide('validateNameAndIdentity', validateNameAndIdentity);

defineExpose({ getNextCheckResult });
</script>

<style lang="less" scoped>
.setting-inner-section {
    flex-grow: 1;
    flex-shrink: 0;
    padding: 11px;
    border-radius: 8px;
    border: 1px solid #f0f1f2;
    width: 428px;
    min-height: 365px;
    height: fit-content;
    & + .setting-inner-section {
        margin-left: 16px;
    }
}
</style>
