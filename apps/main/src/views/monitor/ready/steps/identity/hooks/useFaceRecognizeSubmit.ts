import { _fileUpload } from '@/services/apis/exam';

import { getFormData } from '@crm/exam-utils';
import { useDebuggingStore } from '../../../../store';
import { useMonitorStore } from '@/store/use-monitor-store';
interface ISubmitParams {
    file: any;
    biz: string;
}

interface IParams {
    picSourceType: 1 | 2; // 图片来源：1-摄像头；2-电脑文件
}

export function useFaceRecognizeSubmit(baseParams: IParams) {
    const $monitorStore = useMonitorStore();
    const debuggingStore = useDebuggingStore();

    const doSubmit = async (params: ISubmitParams) => {
        try {
            const fd = getFormData(params);
            const res = await _fileUpload(fd);
            if (res.code !== 0 || !res.data?.encryptId) {
                throw new Error('File upload failed or missing encryptId');
            }
            const postParams = {
                name: debuggingStore.baseInfoFormData.name, // 考生姓名
                identityCard: debuggingStore.baseInfoFormData.identity, // 考生身份证号
                encryptFileId: res.data.encryptId, // 人工审核 附属文件 加密id
                picSourceType: baseParams.picSourceType, // 文件来源：1-摄像头；2-电脑文件
                encExamId: $monitorStore.examBaseInfo.examInfo?.examId, // 加密小考试id
            };
            // 发起审核
            const finallyRes = await Invoke.exam.postSubmitFaceRecognizeApply(postParams);
            if (finallyRes.code !== 0) {
                throw new Error('Face recognition submission failed');
            }
            return finallyRes.data;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    return {
        doSubmit,
    };
}
