<template>
    <div class="dynamic-personal-info-panel">
        <div class="block-title">个人信息</div>
        <b-form ref="formRef" :model="formData" layout="vertical" @submit.prevent>
            <b-form-item
                v-for="item in examineeInfoList"
                :key="item.encryptId"
                :field="item.code"
                :label="item.name"
                :required="item.required"
                validateTrigger="change"
                :rules="getFormItemRules(item) as any"
            >
                <template v-if="item.type === FieldType.radio || item.type === FieldType.checkbox">
                    <b-select
                        v-model="formData[item.code]"
                        placeholder="请选择"
                        :disabled="disabledFiledList.includes(item.code)"
                        :multiple="item.type === FieldType.checkbox"
                        @change="onChangeSelect(item)"
                    >
                        <b-option v-for="(option, index) in item.optionList" :key="index" :value="option">
                            {{ option }}
                        </b-option>
                    </b-select>
                </template>
                <template v-else-if="item.type === FieldType.text">
                    <b-input
                        v-model="formData[item.code]"
                        placeholder="请输入"
                        :disabled="disabledFiledList.includes(item.code)"
                        :maxLength="item.maxLength"
                        @change="onChangeInput(item)"
                    />
                </template>
            </b-form-item>
        </b-form>
    </div>
</template>

<script lang="ts" setup>
import { debounce, isEmpty } from 'lodash-es';
import { reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useDebuggingStore } from '../../../store';

enum FieldType {
    text = 1,
    radio = 2,
    checkbox = 3,
}
interface InfoField {
    type: 1 | 2 | 3; // 1文本 2单选 3多选
    encryptId: string; // 字段加密id
    name: string; // 字段名称
    code: string; // 字段code
    optionList?: Array<string>; // 选项列表
    required: boolean; // 是否必填
    fillValue: string; // 填写值
    disabled?: boolean; // 是否禁止修改
    maxLength?: number; // 最大长度
}

const $route = useRoute();
const { query, params } = $route;
const encryptExamId = params.examId || query.examId;
const debuggingStore = useDebuggingStore();
const { examConfig } = debuggingStore;
const { examineeInfoList }: { examineeInfoList: Array<InfoField> } = examConfig as any;

// 基于原始数据 生成formData数据
const formData = reactive<Record<string, any>>({});
const disabledFiledList = ref<Array<string>>([]); // 禁止考生修改的字段列表

examineeInfoList.forEach((item) => {
    const { type, fillValue, code, required } = item as InfoField;
    const trueValue = type === FieldType.checkbox ? (fillValue ? fillValue.split('、') : []) : fillValue;
    const canModifyField = examConfig.examineeInfoCanModify || (!examConfig.examineeInfoCanModify && required && isEmpty(trueValue));
    // 为空值字段提供默认值，特别是必填字段
    formData[code] = trueValue || (required ? '' : trueValue);
    if (!canModifyField) {
        disabledFiledList.value.push(code);
    }
});

// 存储全局数据
function storeGlobalData() {
    // 添加调试代码
    debuggingStore.setBaseInfoFormData(formData);
}
storeGlobalData();

// 获取表单item rule
function getFormItemRules(item: InfoField) {
    const { required, code } = item;
    const rules = [];

    if (required) {
        rules.push({
            required: true,
            message: `请${item.type === FieldType.text ? '输入' : '选择'}${item.name}`,
            type: item.type === FieldType.checkbox ? 'array' : 'string',
        });
    }
    if (code === 'email') {
        rules.push({
            match: /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/,
            message: '请输入正确的邮箱格式',
            type: 'string',
        });
    }
    if (code === 'identity') {
        rules.push({
            validator: (value: any, callback: (error?: string) => void) => {
                if (value && !/(^\d{18}$)|(^\d{17}([\dX])$)/i.test(value)) {
                    callback('请输入正确的身份证号');
                } else {
                    callback();
                }
            },
        });
    }

    return rules;
}

// 数据存储
function onChangeInput(item: InfoField) {
    const { code } = item;
    postData();
    // 埋点相关
    if (code === 'name') {
        BossAnalyticsTrack('zhice-pc-exam-identity-name');
    } else if (code === 'identity') {
        BossAnalyticsTrack('zhice-pc-exam-identity-ID');
    }
}
function onChangeSelect(item: InfoField) {
    postData();
}

const postData = debounce(async () => {
    storeGlobalData();
    const params = {
        encryptExamId,
        idValueList: examineeInfoList.map((item: InfoField) => {
            return {
                encryptId: item.encryptId,
                name: item.name,
                required: item.required,
                value: item.type === FieldType.checkbox ? formData[item.code]?.join?.('、') : formData[item.code],
            };
        }),
    };
    try {
        const { code, message } = await Invoke.exam.postFillSelfInfo(params);
        if (code !== 0) {
            Toast.danger({
                content: message,
            });
        }
    } catch (error: any) {
        Toast.danger({
            content: error,
        });
    }
}, 300);

const formRef = ref();
defineExpose({
    formData,
    formRef,
});
</script>

<style lang="less" scoped>
.dynamic-personal-info-panel {
    .block-title {
        position: relative;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 24px;
        &::before {
            content: '';
            position: absolute;
            top: 50%;
            margin-top: -5px;
            left: -12px;
            width: 2px;
            height: 10px;
            background: #12ada9;
            border-radius: 0px 2px 2px 0px;
        }
    }
}
</style>
