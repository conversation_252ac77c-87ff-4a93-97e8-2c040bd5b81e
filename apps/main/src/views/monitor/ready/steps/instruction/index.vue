<template>
    <div class="setting-inner">
        <div class="setting-inner-title">答题前请仔细阅读以下说明</div>
        <div class="setting-inner-mini">
            <div class="instruction-panel">
                <div class="remarks">
                    {{ mode === 'PREPARE_MODE' ? examBaseInfo.seqInfo?.seqRemarks : examBaseInfo.examInfo?.examRemarks }}
                </div>
                <div class="exam-list" v-if="mode !== 'PREPARE_MODE'">
                    <ul>
                        <li class="exam-item">
                            <div class="title">
                                <h3>{{ examBaseInfo.examInfo?.examName }}</h3>
                                <span class="status">{{ examBaseInfo.examInfo?.statusDesc }}</span>
                            </div>
                            <div class="content">
                                <div class="question">
                                    <span class="label">题目数：</span>
                                    <span class="value">{{ examBaseInfo.examInfo?.questionCount }}道</span>
                                </div>
                                <div class="duration">
                                    <span class="label">建议时长：</span>
                                    <span class="value">{{ examBaseInfo.examInfo?.adviseDurationStr }}</span>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useMonitorStore } from '@/store/use-monitor-store';
import { computed, onMounted, watch } from 'vue';

defineProps({
    stepStatus: {
        type: String,
        default: 'wait',
    },
});
const emit = defineEmits(['changeStepClickable', 'update:stepStatus']);

const monitorStore = useMonitorStore();
const { examBaseInfo } = monitorStore;
const mode = computed(() => monitorStore.ExamMode); // PREPARE_MODE : EXAM_MODE

// 监听考试状态变化，这里使用store中的examStatus计算属性
watch(
    () => monitorStore.examStatus,
    (newStatus) => {
        // 更新步骤可点击状态
        emit('changeStepClickable', mode.value === 'PREPARE_MODE' || (mode.value === 'EXAM_MODE' && newStatus === 2));
    },
    { immediate: true },
);

// 修改step ✓
emit('update:stepStatus', 'finish');

onMounted(async () => {
    // 调用calculateExamStatus更新状态
    monitorStore.calculateExamStatus();
    // 埋点
    BossAnalyticsTrack('zhice-pc-exam-enter-instruction', {
        pData: {
            nameZh: '进入作答说明',
        },
    });
});

function getNextCheckResult(cb: (str?: string) => void) {
    return true;
}

defineExpose({ getNextCheckResult });
</script>

<style lang="less" scoped>
@import './index.less';
</style>
