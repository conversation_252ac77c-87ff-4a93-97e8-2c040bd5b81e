<template>
    <div class="detection-item">
        <TestStatus :isTesting="isTesting" :isSuccess="isSuccess" />
        <div class="detection-content">
            <div class="type">
                {{ CHECK_TYPE_NAME }}
            </div>
            <div class="result" :class="{ error: !isSuccess && !isTesting }">
                {{ testResult }}
            </div>
        </div>
        <div v-if="!isSuccess && !isTesting" class="detection-suggestion">请检查您的网络</div>
        <div v-if="!isSuccess && !isTesting" class="detection-action">
            <button type="button" plain @click="test">重新检测</button>
        </div>
    </div>
</template>

<script setup lang="ts">
import netSpeedTestImg from '@/assets/images/monitor/net-speed-test.jpg';
import { onMounted, ref } from 'vue';
import { useTestResult } from '../useTestResult';
import TestStatus from './test-status.vue';

const emits = defineEmits(['update:is-success', 'update:is-testing', 'checkSave']);
const CHECK_TYPE_NAME = '网速';
const NETWORK_BASE_SPEED = 0.3;

const isTesting = ref(true);
const isSuccess = ref(false);
const testResult = ref('');
function updateIsTesting(value: boolean) {
    emits('update:is-testing', value);
}
function updateIsSuccess(value: boolean) {
    emits('update:is-success', value);
}
useTestResult(isTesting, isSuccess, updateIsTesting, updateIsSuccess);

function reset() {
    isTesting.value = true;
    isSuccess.value = false;
    testResult.value = '正在检测中…';
    // testSuggestions.value = '';
}

async function test() {
    reset();

    let speed: any = 0;
    speed = await getSpeedWithImg(`${netSpeedTestImg}?t=${Date.now()}`, 941);
    speed = (speed / 1024).toFixed(2);

    isSuccess.value = speed >= NETWORK_BASE_SPEED;
    testResult.value = `${speed}M/s`;
    isTesting.value = false;

    BossAnalyticsTrack('zhice-pc-exam-network-speed', {
        pData: {
            type: TrackTypeEnum.成功,
            message: testResult.value,
            nameZh: '设备检测-网速',
        },
    });

    // 拓展埋点参数
    BossAnalyticsExtendTrackProps({
        Internetspeed: testResult.value,
    });

    emits('checkSave', {
        checkItem: 2, // 1 浏览器 2 网速 3麦克风 4 电脑主摄像头 5 电脑屏幕 6手机第二视角
        checkStatus: isSuccess.value ? 1 : 2, // //状态 1成功 2失败
        checkRemark: isSuccess.value ? '' : testResult.value,
    });
}

/**
 *
 * @param imgUrl 图片路径
 * @param fileSize 文件大小kb
 * @return 网速数KB/S
 */
function getSpeedWithImg(imgUrl: any, fileSize: any) {
    return new Promise((resolve) => {
        let start: any = null;
        let end = null;
        const img = document.createElement('img');
        start = new Date().getTime();
        img.onload = function () {
            end = new Date().getTime();
            const speed = (fileSize * 1000) / (end - start);
            resolve(speed);
        };
        img.src = imgUrl;
    });
}

onMounted(() => {
    test();
});

defineExpose({ test });
</script>
