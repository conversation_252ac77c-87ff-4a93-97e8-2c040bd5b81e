<template>
    <div class="detection-item">
        <TestStatus :isTesting="isTesting" :isSuccess="isSuccess" errorLevel="warning" />
        <div class="detection-content">
            <div class="type">
                {{ CHECK_TYPE_NAME }}
            </div>
            <div class="result" :class="{ error: !isSuccess && !isTesting }">
                {{ testResult }}
            </div>
        </div>
        <div v-if="!isSuccess" class="detection-suggestion">
            请使用最新版本的 Chrome 浏览器<a href="https://www.google.cn/chrome/index.html" class="download" target="_blank" @click="onDownload">下载</a>
        </div>
    </div>
</template>

<script setup lang="ts">
import Bowser from 'bowser';
import { onMounted, ref } from 'vue';
import { useTestResult } from '../useTestResult';
import TestStatus from './test-status.vue';

const emits = defineEmits(['update:is-success', 'update:is-testing', 'checkSave']);
const CHECK_TYPE_NAME = '浏览器';
const CHROME_BASE_VERSION = '>120';

const isTesting = ref(true); // 是否正在检测
const isSuccess = ref(false); // 是否检测成功
const testResult = ref('');
function updateIsTesting(value: boolean) {
    emits('update:is-testing', value);
}
function updateIsSuccess(value: boolean) {
    emits('update:is-success', value);
}
useTestResult(isTesting, isSuccess, updateIsTesting, updateIsSuccess);

function reset() {
    isTesting.value = true;
    isSuccess.value = false;
    testResult.value = '正在检测中…';
}

async function test() {
    reset();

    const browser = Bowser.getParser(window.navigator.userAgent);
    const { name, version } = browser.getBrowser();

    BossAnalyticsTrack('zhice-pc-exam-browser-info', {
        pData: {
            type: TrackTypeEnum.成功,
            message: `${name}-${version}`,
            nameZh: '设备检测-浏览器',
        },
    });

    testResult.value = `${name} ${version}`;
    isSuccess.value = !!(name?.toLowerCase() === 'chrome' && browser.satisfies({ chrome: CHROME_BASE_VERSION }));
    isTesting.value = false;

    emits('checkSave', {
        checkItem: 1, // 1 浏览器 2 网速 3麦克风 4 电脑主摄像头 5 电脑屏幕 6手机第二视角 写死参数不可修改
        checkStatus: isSuccess.value ? 1 : 2, // //状态 1成功 2失败
        checkRemark: isSuccess.value ? '' : '不满足Chrome>120',
    });
}

function onDownload() {
    BossAnalyticsTrack('zhice-pc-DeviceTesting-downloadBrower', {
        pData: {
            nameZh: '设备检测-点击下载浏览器',
        },
    });
}

onMounted(() => {
    test();
});

defineExpose({ test });
</script>
