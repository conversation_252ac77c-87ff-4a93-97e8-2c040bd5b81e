<template>
    <router-view v-if="showRouterView" />
    <div v-else class="monitor-inner">
        <ExamInfo :hasHelp="examConfig.personalVerification && ['identity'].includes(currentStepItem?.type || '')" />
        <div class="exam-setting">
            <div class="setting-area">
                <b-step :current="step + 1" labelPlacement="vertical" small :changeable="false">
                    <b-step-item v-for="(item, index) of stepList" :key="index" :status="step === index ? 'process' : item.status">
                        {{ item.label }}
                    </b-step-item>
                </b-step>
                <Component
                    :is="currentStepItem.component"
                    ref="currentComponentRef"
                    v-model:stepStatus="currentStepItem.status"
                    class="step-content"
                    v-on="{ changeStepClickable }"
                    :examId="encryptExamId"
                    :seqId="seqId"
                />
            </div>
            <div class="setting-footer">
                <b-space direction="horizontal">
                    <span v-if="['screen', 'phone'].includes(currentStepItem?.type)" class="op-help" @click="getHelp"
                        ><SvgIcon name="question" width="16" height="16" />更多帮助</span
                    >
                    <b-button v-if="step > 0" status="primary" type="outline" shape="round" size="large" @click="onClickPrev"> 上一步 </b-button>
                    <b-button v-if="step < stepList.length" type="primary" shape="round" size="large" :disabled="!currentStepItem?.nextClickable" @click="onClickNext">
                        {{ nextButtonText }}
                    </b-button>
                </b-space>
            </div>
        </div>
        <!-- 人脸验证服务确认弹窗 -->
        <DialogPersonalVerification v-model="isShowPersonalVerificationDialog" unmountOnClose @confirm="onConfirmPersonalVerification" />
    </div>
</template>

<script lang="ts" setup>
import type { IStepItem } from '@crm/exam-types';
import { useDebuggingStore } from '../store';
import DialogPersonalVerification from './components/dialog-personal-verification.vue';
import ExamInfo from './components/exam-info.vue';
import { HELP_PHONE } from '@crm/exam-constants';
import { createVNode, defineAsyncComponent, markRaw } from 'vue';
import { useMonitorStore } from '@/store/use-monitor-store';
import { useRouteParamId } from '../hooks/useRouteParamId';
const $route = useRoute();

const { seqId, encryptExamId } = useRouteParamId();

const debuggingStore = useDebuggingStore();
const { examConfig } = debuggingStore;
const monitorStore = useMonitorStore();

const mode = computed(() => monitorStore.ExamMode); // PREPARE_MODE : EXAM_MODE
const step = computed(() => debuggingStore.step); // 保存步骤接口，目前仅会 保存终态（准备：-4， 正式：5），中间阶段步骤不会记忆
const showRouterView = computed(() => {
    return $route.path === '/monitor/ready/done';
});

// 考前准备阶段相关逻辑
function showDefindDialog(str: string) {
    Dialog.open({
        title: '检测不通过',
        type: 'warning',
        content: str,
        width: 400,
        layerClosable: false,
        confirm: () => {
            goNext();
        },
    });
}
function showDefindToast(str: string) {
    Toast.warning(str);
}
const originStepList: IStepItem[] = reactive([
    {
        label: '设备检测',
        type: 'device',
        cb: showDefindDialog,
        component: markRaw(defineAsyncComponent(() => import('./steps/device/index.vue'))),
        nextClickable: false,
        status: 'wait',
    },
    {
        label: '考生身份核验',
        type: 'identity',
        cb: showDefindToast, // 考前准备阶段，核验检测不通过的提示
        component: markRaw(defineAsyncComponent(() => import('./steps/identity/index.vue'))), // 组件
        nextClickable: false, // 是否允许下一步
        check: () => mode.value === 'EXAM_MODE', // 是否有当前步骤的判断条件
        status: 'wait', // 当前步骤的状态
    },
    {
        label: '屏幕共享设置',
        type: 'screen',
        cb: showDefindDialog,
        component: markRaw(defineAsyncComponent(() => import('./steps/screen/index.vue'))),
        nextClickable: false,
        check: () => examConfig.computerScreenMonitor,
        status: 'wait',
    },
    {
        label: '第二视角设置',
        type: 'phone',
        cb: showDefindDialog,
        component: markRaw(defineAsyncComponent(() => import('./steps/phone/index.vue'))),
        nextClickable: true,
        check: () => examConfig.mobilePerspectiveMonitor,
        status: 'wait',
    },
    {
        label: '作答说明',
        type: 'instruction',
        component: markRaw(defineAsyncComponent(() => import('./steps/instruction/index.vue'))),
        nextClickable: false,
        status: 'wait',
    },
]);
const stepList = computed(() => {
    return originStepList.filter((item) => !item.check || item.check());
});
const currentStepItem = computed((): IStepItem => {
    return stepList.value[step.value] || ({} as IStepItem);
});
const currentComponentRef = ref();
function changeStepClickable(flag: boolean) {
    if (currentStepItem.value) {
        currentStepItem.value.nextClickable = flag;
    }
}

// 下一步按钮文案
const nextButtonText = computed(() => {
    if (step.value === stepList.value.length - 1) {
        return mode.value === 'EXAM_MODE' ? '开始作答' : '准备好了';
    }
    return '下一步';
});
enum STAGE_CODE_ENUM {
    device = 0,
    identity = 1,
    screen = 2,
    phone = 3,
    instruction = 4,
}
async function goNext() {
    let nextStep = step.value + 1;

    if (nextStep === stepList.value.length) {
        if (mode.value === 'PREPARE_MODE') {
            nextStep = -4; // 自己定的一个值 （正式考试阶段要变成5）
            await saveFinalStep(nextStep);
        } else {
            nextStep = 5;
            await Promise.all([saveFinalStep(nextStep), reportEveryStep(nextStep)]);
            // 开始作答
            BossAnalyticsTrack('zhice-pc-exam-begin', {
                pData: '开始作答',
            });
        }
    } else if (mode.value === 'EXAM_MODE') {
        await reportEveryStep(STAGE_CODE_ENUM[stepList.value[nextStep]?.type as keyof typeof STAGE_CODE_ENUM]);
    }

    debuggingStore.step = nextStep;
}

function onClickPrev() {
    debuggingStore.step--;
    BossAnalyticsTrack('zhice-pc-DeviceTesting-back', {
        p3: currentStepItem.value?.type,
        nameZh: '点击上一步',
    });
}

async function onClickNext() {
    // 记录点击下一步事件埋点
    BossAnalyticsTrack('zhice-pc-DeviceTesting-next', {
        p3: currentStepItem.value?.type,
        pData: { nameZh: '点击下一步' },
    });

    // 防止重复或不可点击
    if (isSavingFinalStep || isReportingEveryStep || !currentStepItem.value?.nextClickable) {
        return;
    }

    // 执行当前组件校验
    const cb = currentStepItem.value?.cb || (() => {});
    const flag = await currentComponentRef.value.getNextCheckResult(cb);
    if (!flag) return;

    // 人脸验证弹窗逻辑
    if (
        stepList.value[step.value + 1] &&
        stepList.value[step.value + 1]?.type === 'identity' &&
        mode.value === 'EXAM_MODE' &&
        examConfig.personalVerification &&
        !examConfig.recognizeFaceConfirm
    ) {
        isShowPersonalVerificationDialog.value = true;
        return;
    }

    // 计算下一步
    await goNext();
}

let isSavingFinalStep = false;
async function saveFinalStep(stage: number) {
    try {
        const params = { encryptExamId: encryptExamId || seqId, stage };
        isSavingFinalStep = true;
        await Invoke.exam.postSaveStage(params);
        isSavingFinalStep = false;
    } catch (error) {
        logger.error('saveFinalStep', error);
    }
}
let isReportingEveryStep = false;
// 上报考试动作（当前考试已触达第几步）
async function reportEveryStep(stage: number) {
    try {
        const params = { encryptExamId, actionType: stage };
        isReportingEveryStep = true;
        await Invoke.exam.postStepReport(params);
        isReportingEveryStep = false;
    } catch (error) {
        logger.error('reportEveryStep', error);
    }
}

function getHelp() {
    const helpText = `请根据页面提示先进行调试或授权，确保使用Chrome浏览器120以上版本并保持网络畅通，您也可以尝试重启电脑解决问题。如需更多帮助请联系${HELP_PHONE}`;
    Dialog.open({
        title: '更多帮助',
        type: 'info',
        width: 460,
        footer: false,
        content: () => createVNode('div', { style: { marginBottom: '30px' } }, helpText),
    });
    // 埋点
    BossAnalyticsTrack('zhice-pc-DeviceTesting-help', {
        p3: currentStepItem.value?.type,
    });
}

// 人脸验证服务确认 弹窗
const isShowPersonalVerificationDialog = ref(false);
function onConfirmPersonalVerification() {
    // "人脸验证服务确认"完成: 13
    Invoke.exam.postExamActionSave({
        encryptExamId,
        actionType: 13,
    });
    debuggingStore.recognizeFaceConfirm();
    goNext();
}

// 监控状态实时变化，同步step status
enum MONITOR_STATUS_MAP {
    未开启 = 0,
    失败 = 1,
    成功 = 2,
}

function syncStepStaus(type: 'screen' | 'phone', status: 0 | 1 | 2): void {
    enum statusStr {
        finish = 2,
        danger = 1,
        wait = 0,
    }
    stepList.value.forEach((item) => {
        if (item.type === type) {
            item.status = statusStr[status] as 'danger' | 'wait' | 'process' | 'finish';
        }
    });
}

watch(
    () => debuggingStore.STATUS.camera.status,
    (newVal: MONITOR_STATUS_MAP, oldVal: MONITOR_STATUS_MAP) => {
        // 摄像头监控状态变化埋点
        BossAnalyticsTrack('zhice-pc-exam-camera-status-change', {
            pData: {
                message: `${MONITOR_STATUS_MAP[oldVal]} -> ${MONITOR_STATUS_MAP[newVal]}`,
                type: newVal === MONITOR_STATUS_MAP.失败 ? TrackTypeEnum.失败 : TrackTypeEnum.成功,
                reportType: ReportTypeEnum.摄像头,
                nameZh: '摄像头监控状态变化',
            },
        });
        // 埋点结束
    },
);

watch(
    () => debuggingStore.STATUS.screen.status,
    (newVal: MONITOR_STATUS_MAP, oldVal: MONITOR_STATUS_MAP) => {
        // 进度条状态与真实播放状态同步wait finish danger
        syncStepStaus('screen', newVal);
        // 屏幕共享监控状态变化埋点
        BossAnalyticsTrack('zhice-pc-exam-screen-status-change', {
            pData: {
                message: `${MONITOR_STATUS_MAP[oldVal]} -> ${MONITOR_STATUS_MAP[newVal]}`,
                type: newVal === MONITOR_STATUS_MAP.失败 ? TrackTypeEnum.失败 : TrackTypeEnum.成功,
                reportType: ReportTypeEnum.屏幕共享,
                nameZh: '屏幕共享监控状态变化',
            },
        });
        // 埋点结束
    },
);

watch(
    () => debuggingStore.STATUS.phone.status,
    (newVal: MONITOR_STATUS_MAP, oldVal: MONITOR_STATUS_MAP) => {
        // 进度条状态与真实播放状态同步wait finish danger
        syncStepStaus('phone', newVal);
        // 手机监控状态变化埋点
        BossAnalyticsTrack('zhice-pc-exam-phone-status-change', {
            pData: {
                message: `${MONITOR_STATUS_MAP[oldVal]} -> ${MONITOR_STATUS_MAP[newVal]}`,
                type: newVal === MONITOR_STATUS_MAP.失败 ? TrackTypeEnum.失败 : TrackTypeEnum.成功,
                reportType: ReportTypeEnum.手机,
                nameZh: '手机监控状态变化',
            },
        });
        // 埋点结束
    },
);
</script>
