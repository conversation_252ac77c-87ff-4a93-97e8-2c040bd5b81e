import type { IDeployEnv } from '@/types/shims-vue.d';
export declare const APP_NAME = "\u77B0\u8350\u804C\u6D4B\u7BA1\u7406\u540E\u53F0";
export declare const IUC_TOKEN = "kanjian_exam";
export declare const DEPLOY_ENV: IDeployEnv;
export declare const B_SITES: {
    rd: string;
    qa: string;
    pre: string;
    prod: string;
};
export declare const STORAGE_KEY_KANJIAN_EXAM_SEQ_ID = "kanjian_exam_seq_id";
//# sourceMappingURL=index.d.ts.map