import { DEPLOY_ENV } from '.';

const urls = {
    rtcUrl: '',
    wsUrl: '',
};

switch (DEPLOY_ENV) {
    case 'pre':
        urls.rtcUrl = 'https://pre-nebula.zhipin.com';
        urls.wsUrl = 'wss://pre-kaoshi.zhipin.com/kanjian/ws';
        break;
    case 'prod':
        urls.rtcUrl = 'https://nebula.zhipin.com';
        urls.wsUrl = 'wss://kaoshi.zhipin.com/kanjian/ws';
        break;
    default:
        urls.rtcUrl = 'https://qa-nebula.weizhipin.com';
        urls.wsUrl = 'wss://kaoshi-qa.weizhipin.com/kanjian/ws';
        break;
}

export { urls };
