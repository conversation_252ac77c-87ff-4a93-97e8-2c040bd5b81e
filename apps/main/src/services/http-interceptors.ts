import type { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { DEPLOY_ENV } from '@/shared';
import { jumpLogin } from '@/utils/jump-url';
import { generateBossTraceID } from '@boss/utils';
import { userKickLoginOutDialog, userForceRefresh } from '@/utils/system-notification-dialog';
import axios from 'axios';
import { apmSendAxiosError, clearPMcustomUid } from '@/utils/apm';

const instance = axios.create({
    timeout: 400000, // 请求超时时间
    withCredentials: true, // 跨域
    params: {
        _: Date.now(),
    },
});

instance.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
        config.headers['X-Requested-With'] = 'XMLHttpRequest'; // 请求头来源标识
        config.headers.traceId = generateBossTraceID();
        return config;
    },
    (error: AxiosError) => {
        return Promise.reject(error);
    },
);

const loginFailureCodes = [7]; // 7: 登录方式发生变更！/ 当前登录状态已失效

instance.interceptors.response.use(
    (response: AxiosResponse) => {
        const { status, data, config } = response;
        const errorMsg = data.message || '请求失败';

        if (status !== 200) {
            if (DEPLOY_ENV !== 'prod') {
                Toast.danger(errorMsg);
            }
            return Promise.reject(response);
        }

        // 如果接口过滤非登录判断直接返回接口请求
        if (config.headers.noNeedLogin === true) {
            return data;
        }

        // 用户被踢出
        if (data && data.code === 99) {
            userKickLoginOutDialog();
            return data;
        }

        // 强制用户刷新
        if (data && data.code === 31) {
            userForceRefresh();
            return data;
        }

        // 登录失效相关
        if (data && loginFailureCodes.includes(data.code)) {
            // 清空APM标记
            clearPMcustomUid();
            jumpLogin();
        }

        // 异常处理；历史noErrorToast逻辑暂时无需处理
        if (data.code !== 0) {
            // 异常处理；与服务端约定，code为1、2、17、19时，进行错误监控，剩余情况服务端监控
            if ([1, 2, 17, 19].includes(data.code)) {
                apmSendAxiosError(
                    {
                        url: config.url,
                        param: JSON.stringify(config.data) || JSON.stringify(config.params),
                        config,
                        response,
                        traceId: config.headers.traceId,
                    },
                    'code-error',
                );
            }

            if (DEPLOY_ENV !== 'prod') {
                Toast.danger(errorMsg);
            }
        }

        return data;
    },
    (error: AxiosError) => {
        const { config } = error;
        // 啄木鸟上报(排除掉取消请求)
        if (!axios.isCancel(error) && config) {
            apmSendAxiosError(
                {
                    url: config!.url,
                    param: JSON.stringify(config.data) || JSON.stringify(config.params),
                    config,
                    response: error,
                    traceId: config.headers.traceId,
                },
                'code-error',
            );
        }
        return Promise.reject(error);
    },
);

export default instance;
