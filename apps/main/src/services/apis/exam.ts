import type { IFileItem } from '@bzl/zhice-exam-question';
import { get, post } from '../http';

/**
 * https://api.weizhipin.com/project/2353/interface/api/513916
 * 考试答题页-上传文件
 */
export function _fileUploadForExam(params: any, fileItem: IFileItem) {
    return post('/wapi/web/file/upload', params, {
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        onUploadProgress: (progressEvent: ProgressEvent) => {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            fileItem.progress = percentCompleted;
        },
    });
}

/**
 * https://api.weizhipin.com/project/2353/interface/api/513916
 * 考试-下载文件
 */
export function _fileDownload(params: any) {
    return get(
        `/wapi/web/file/download/${params.fileId}/${params.fileName}`,
        {},
        {
            responseType: 'blob',
        },
    );
}

/**
 * https://api.weizhipin.com/project/2353/interface/api/513916
 * 考试-上传文件
 */
export function _fileUpload(params: any) {
    return post('/wapi/web/file/upload', params, {
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
    });
}

/**
 * https://api.weizhipin.com/project/2353/interface/api/513922
 * 人脸识别
 */
export function _recognizeFace(params: any) {
    return post('/wapi/web/exam/face/recognize.json', params, {
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
    });
}

/**
 * https://api.weizhipin.com/project/2353/interface/api/513922
 * 人脸识别
 */
export const _cameraIncrease = (params: any) => {
    return post('/wapi/web/exam/cameraMonitoring/increase.json', params, {
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
    });
};

/**
 * https://api.weizhipin.com/project/2353/interface/api/561922
 * 考生判断是否替考
 */
export const _substituteCheck = (params: any) => {
    return post('/wapi/web/exam/cameraMonitoring/substituteCheck.json', params, {
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
    });
};

/**
 * 考生监控截图上传
 */
export const _addUserPhoto = (params: any) => {
    return post('/wapi/web/exam/examinee/addUserPhoto.json', params, {
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
    });
};

/**
 * 考生异常行为记录
 */
export const _postErrAction = (params: any) => {
    return post('/wapi/web/exam/examinee/errAction.json', params, {
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
    });
};
