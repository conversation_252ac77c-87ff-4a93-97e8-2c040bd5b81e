const API = {
    login: {
        postLoginCaptchaSms: '/wapi/web/login/captcha/sms',
        postLogin: '/wapi/web/login',
        postLogout: '/wapi/web/logout',
        postLoginCaptchaGeetest: '/wapi/web/login/captcha/geetest',
        getLoginValidExamId: '/wapi/web/login/valid/examId',
    },
    common: {
        getInfo: '/mapi/wap/common/info.json', // APM 用户标识获取接口
        getBaseInfo: '/wapi/web/exam/getMonoBase.json', // 获取大场次或小考试或小测评的基础信息
        getConfig: '/wapi/web/exam/getMonoConfig.json', // 获取大场次或小考试调试设备配置
        postFormLog: '/mapi/wap/common/log/activeLog.json', // 埋点接口（复用H5）
        postEvaluationPaperCommit: '/wapi/web/exam/evaluation/paper/commit.json', // 测评交卷接口
        postFormAddAnswer: '/mapi/wap/evaluation/paper/addAnswer.json', // 试题提交
        postHeartBeat: '/wapi/web/exam/common/heartbeat/report', // 心跳上报
    },
    examList: {
        // 试卷列表页
        getExamList: '/wapi/web/exam/list',
    },
    gba: {
        gbaAnswerList: '/mapi/wap/examinee/paper/gba/answerList.json', // GBA产品-答题列表
        postGbaAddAnswer: '/mapi/wap/evaluation/paper/gba/addAnswer.json', // GBA产品-提交回合答案
        postGbaGameCommit: '/mapi/wap/evaluation/paper/gba/commit/game.json', // GBA产品-提交大题
    },
    hots: {
        getAnswerList: '/mapi/wap/examinee/paper/answerList.json', // HOTS产品-答题列表
        getHotsMockCommitLog: '/mapi/wap/hots/mock/action/history', // hots产品执行模拟历史记录
        postHotsMockCommit: '/mapi/wap/hots/mock/action/commit', // hots产品执行模拟
        postFormAddTmpAnswer: '/mapi/wap/evaluation/paper/addTmpAnswer.json', // 试题暂存
    },
    exam: {
        getH5Code: '/wapi/web/exam/getH5Code', // 第二视角/人脸验证二维码code
        getSystemRecognizeResult: '/wapi/web/exam/getSystemRecognizeResult', // 人脸识别结果
        getFaceRecognizeAuditInfo: '/wapi/web/exam/getFaceRecognizeAuditInfo.json', //  查询人脸识别审核信息
        postSaveStage: '/wapi/web/exam/enter/stage/save.json', // 进入考试完成阶段保存
        getStageQuery: '/wapi/web/exam/enter/stage/query.json', // 进入考试完成阶段查询
        postSwitchScreenIncrease: '/wapi/web/exam/switchScreen/increase.json', // 增加考生考试切换屏幕次数
        postPhoneCameraUpdate: '/wapi/web/exam/prepare/phoneCamera/update', // 考前准备第二视角状态更新
        postExamActionSave: '/wapi/web/exam/action/save', // 考生动作上报
        postStepReport: '/wapi/web/exam/action/v2/save', // 当前步骤上报
        postPrepareCheckSave: '/wapi/web/exam/prepare/check/save', // 考前准备情况上报
        getExamQuestionList: '/wapi/web/exam/question/list.json', // 获取考试题目列表
        postAnswerSave: '/wapi/web/exam/answer/save.json', // 保存考试答案
        postCommitPaper: '/wapi/web/exam/commitPaper.json', // 提交试卷
        postFillSelfInfo: '/wapi/web/exam/fillSelfInfo.json', // 考生填写个人信息
        postWaningConfirmReLogin: '/wapi/web/exam/reLogin/waningConfirm.json', // 考生考试重复登录提醒确认
        getDialogHistoryList: '/wapi/web/exam/message/dialog/list', // 获取消息框历史消息
        postSubmitFaceRecognizeApply: '/wapi/web/exam/submitFaceRecognizeApply.json', // 提交人脸识别审核申请
        postFormCameraIncrease: '/wapi/web/exam/cameraMonitoring/increase.json', // 增加考生考试主摄像头监控
        postFormSubstituteCheck: '/wapi/web/exam/cameraMonitoring/substituteCheck.json', // 考生判断是否替考
        getEvaluationInfo: '/mapi/wap/examinee/paper/common/answerList.json', // 量表试题列表（与H5通用）
        postFormAddUserPhoto: '/wapi/web/exam/examinee/addUserPhoto.json', // 考生监控截图上传
        postProgramExecute: '/wapi/web/exam/judge.json', // 代码题运行
        postProgramExecuteStatus: '/wapi/web/exam/judge/status.json', // 代码题运行状态
        postProgramSetLanguage: '/wapi/web/exam/judge/question/set/language', // 代码题语言选择设置
        getProgramGetLanguage: '/wapi/web/exam/judge/question/get/language', // 代码题语言获取
        postFaceRecognizeExceptionSave: '/wapi/web/exam/faceRecognize/exception/save', // 人脸识别异常上报
    },
    feedback: {
        postExamNoticeCallback: '/wapi/web/exam/notice/callback',
    },
    dev: {
        // 调试接口 --- 上线调用无效
    },
    preview: {
        // 试卷预览
        paperBase: '/mapi/wap/evaluation/paper/sequence/template/preview.json', // 获取场次信息
        getAnswerList: '/mapi/wap/evaluation/paper/common/preview/answerList.json', // 试题列表
        paperGbaPreview: '/mapi/wap/evaluation/paper/gba/preview/answerList.json', // GBA 预览接口
        paperHotsPreview: '/mapi/wap/evaluation/paper/v2/preview.json', // HOTS 预览接口
    },
};

export default {
    ...API,
};
