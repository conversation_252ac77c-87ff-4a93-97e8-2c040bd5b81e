{"globals": {"BossAnalyticsExtendTrackProps": "readonly", "BossAnalyticsTrack": "readonly", "LogLevel": "readonly", "Logger": "readonly", "ReportTypeEnum": "readonly", "SessionStorage": "readonly", "Storage": "readonly", "TrackTypeEnum": "readonly", "clearContextConfig": true, "imgPreload": "readonly", "interceptConsole": "readonly", "jumpH5Exam": "readonly", "jumpLogin": "readonly", "logger": "readonly", "loggerConfig": true, "numberToChinese": "readonly", "openTopNotification": "readonly", "parseURL": "readonly", "postLog": "readonly", "restoreConsole": "readonly", "setContextConfig": true, "setGlobalConfig": true, "setGlobalLevel": true, "setGlobalShowLevel": true, "setGlobalShowTimestamp": true, "userCutScreenDialog": "readonly", "userForceRefresh": "readonly", "userKickLoginOutDialog": "readonly", "Dialog": "readonly", "Toast": "readonly", "BossAnalyticsInit": "readonly", "BossAnalyticsLogin": "readonly", "sendErrorAction": "readonly", "sendInitializeErrorAction": "readonly", "sendVirtualErrorAction": "readonly", "LogRecord": "readonly", "LoggerConfig": "readonly", "Middleware": "readonly", "_postErrAction": "readonly", "apmSendAxiosError": "readonly", "apmSendCustom": "readonly", "apmSendRouteErr": "readonly", "clearPMcustomUid": "readonly", "getPMcustomUid": "readonly", "preloadRoutes": "readonly", "setAPMcustomUid": "readonly", "originalConsole": "readonly", "generateExamUrl": "readonly", "getH5ExamMonitor": "readonly", "getH5FaceCheckUrl": "readonly", "Component": "readonly", "ComponentPublicInstance": "readonly", "ComputedRef": "readonly", "CustomToast": "readonly", "DirectiveBinding": "readonly", "EffectScope": "readonly", "ExtractDefaultPropTypes": "readonly", "ExtractPropTypes": "readonly", "ExtractPublicPropTypes": "readonly", "InjectionKey": "readonly", "MaybeRef": "readonly", "MaybeRefOrGetter": "readonly", "PropType": "readonly", "QuestionType": "readonly", "Ref": "readonly", "RequestStatus": "readonly", "Slot": "readonly", "Slots": "readonly", "VNode": "readonly", "WritableComputedRef": "readonly", "acceptHMRUpdate": "readonly", "closeDialogs": "readonly", "computed": "readonly", "createApp": "readonly", "createPinia": "readonly", "customRef": "readonly", "defineAsyncComponent": "readonly", "defineComponent": "readonly", "defineStore": "readonly", "effectScope": "readonly", "getActivePinia": "readonly", "getCurrentInstance": "readonly", "getCurrentScope": "readonly", "globalEventCallbacks": "readonly", "h": "readonly", "inject": "readonly", "isProxy": "readonly", "isReactive": "readonly", "isReadonly": "readonly", "isRef": "readonly", "mapActions": "readonly", "mapGetters": "readonly", "mapState": "readonly", "mapStores": "readonly", "mapWritableState": "readonly", "markRaw": "readonly", "nextTick": "readonly", "onActivated": "readonly", "onBeforeMount": "readonly", "onBeforeRouteLeave": "readonly", "onBeforeRouteUpdate": "readonly", "onBeforeUnmount": "readonly", "onBeforeUpdate": "readonly", "onDeactivated": "readonly", "onErrorCaptured": "readonly", "onMounted": "readonly", "onRenderTracked": "readonly", "onRenderTriggered": "readonly", "onScopeDispose": "readonly", "onServerPrefetch": "readonly", "onUnmounted": "readonly", "onUpdated": "readonly", "onWatcherCleanup": "readonly", "provide": "readonly", "reactive": "readonly", "readonly": "readonly", "ref": "readonly", "resolveComponent": "readonly", "setActivePinia": "readonly", "setMapStoreSuffix": "readonly", "shallowReactive": "readonly", "shallowReadonly": "readonly", "shallowRef": "readonly", "storeToRefs": "readonly", "toRaw": "readonly", "toRef": "readonly", "toRefs": "readonly", "toValue": "readonly", "triggerRef": "readonly", "unref": "readonly", "useAnswerDialogController": "readonly", "useAnswerRetry": "readonly", "useAntiCheat": "readonly", "useAttrs": "readonly", "useCommitPaper": "readonly", "useCssModule": "readonly", "useCssVars": "readonly", "useDialogHint": "readonly", "useEventListenersStore": "readonly", "useEventSubscription": "readonly", "useFullScreen": "readonly", "useGlobalInit": "readonly", "useId": "readonly", "useLink": "readonly", "useModel": "readonly", "useMonitor": "readonly", "useOpenDialogCoolDown": "readonly", "usePrepareCheckSave": "readonly", "useReLogin": "readonly", "useRoute": "readonly", "useRouter": "readonly", "useSlots": "readonly", "useSwitchScreen": "readonly", "useTemplateRef": "readonly", "useToast": "readonly", "watch": "readonly", "watchEffect": "readonly", "watchPostEffect": "readonly", "watchSyncEffect": "readonly", "TrackerEventData": "readonly", "switchScreenIncrease": "readonly", "detectScreenShareType": "readonly", "getFullscreenElement": "readonly", "isFullscreenSupported": "readonly", "requestFullscreenWithScreenShare": "readonly", "safeExitFullscreen": "readonly"}}