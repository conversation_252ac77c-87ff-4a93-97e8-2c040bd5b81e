{"extends": "../../tsconfig.base.json", "compilerOptions": {"composite": true, "baseUrl": "./", "types": ["@boss/apm-sdk-types"], "paths": {"@/*": ["src/*"]}, "outDir": "dist", "tsBuildInfoFile": ".tsbuildinfo"}, "include": ["auto-imports.d.ts", "components.d.ts", "vite.config.ts", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"], "exclude": ["node_modules", "dist", "public", "**/*.tsbuildinfo"]}