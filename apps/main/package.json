{"name": "@crm/exam-main", "version": "0.0.0", "private": true, "type": "module", "scripts": {"build:pre": "vite build --mode pre", "build:prod": "vite build --mode prod", "build:qa": "vite build --mode qa", "build:rd": "vite build --mode rd", "build:webfont": "boss-cli build:icons -b -d src/assets/svg --name bossicon  --namespace b-icon ", "dev": "vite --force --mode dev", "dev:rd": "vite --force --mode rd", "dev:pre": "vite --force --mode pre", "lint": "eslint src --no-fix --quiet --ext .ts,.tsx,.vue,.js,.jsx", "lint:fix": "eslint src --fix --ext .ts,.tsx,.vue,.js,.jsx", "preview": "vite preview", "style-lint": "stylelint src/**/*.{css,sass,scss,less,vue}", "style-lint:fix": "stylelint src/**/*.{css,sass,scss,less,vue} --fix", "style-lint:fixless": "stylelint \"src/**/*.(less|css)\" --fix"}, "dependencies": {"@crm/vueuse-pro": "2.1.0", "gsap": "3.12.7"}}