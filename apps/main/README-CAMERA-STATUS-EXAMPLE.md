# 摄像头状态细化管理示例

这个示例展示了如何在UI组件中使用细化的摄像头状态管理，解决麦克风问题导致摄像头整体异常的问题。

## 新的状态结构

```typescript
// 现在摄像头状态包含了细化的音视频状态
interface CameraStatus {
  status: number;           // 整体状态: 0未开启, 1失败, 2成功
  videoStatus: number;      // 视频状态: 0未开启, 1失败, 2成功
  audioStatus: number;      // 音频状态: 0未开启, 1失败, 2成功
  errorText: string;        // 组合的错误信息
  videoErrorText: string;   // 视频错误信息
  audioErrorText: string;   // 音频错误信息
  // ... 其他字段
}
```

## Vue组件示例

```vue
<template>
  <div class="camera-monitor-status">
    <!-- 整体状态指示器 -->
    <div class="status-indicator" :class="getStatusClass(cameraStatus.overall)">
      <span class="status-text">{{ getStatusText(cameraStatus.overall) }}</span>
    </div>

    <!-- 详细的音视频状态 -->
    <div class="detailed-status">
      <!-- 视频状态 -->
      <div class="video-status" :class="getStatusClass(cameraStatus.video)">
        <icon-camera :class="{ disabled: cameraStatus.video === 1 }" />
        <span>摄像头</span>
        <span v-if="cameraStatus.videoError" class="error-text">
          {{ cameraStatus.videoError }}
        </span>
      </div>

      <!-- 音频状态 -->
      <div class="audio-status" :class="getStatusClass(cameraStatus.audio)">
        <icon-microphone :class="{ disabled: cameraStatus.audio === 1 }" />
        <span>麦克风</span>
        <span v-if="cameraStatus.audioError" class="error-text">
          {{ cameraStatus.audioError }}
        </span>
      </div>
    </div>

    <!-- 根据不同情况显示不同的提示 -->
    <div class="status-tips">
      <div v-if="cameraStatus.isFullyWorking" class="tip success">
        摄像头和麦克风工作正常
      </div>
      <div v-else-if="cameraStatus.isOnlyAudioIssue" class="tip warning">
        摄像头正常，麦克风异常 - 可以继续考试，但无法录制音频
      </div>
      <div v-else-if="cameraStatus.isOnlyVideoIssue" class="tip error">
        麦克风正常，但摄像头异常 - 请检查摄像头权限
      </div>
      <div v-else-if="cameraStatus.overall === 1" class="tip error">
        摄像头和麦克风都有问题 - 请检查设备权限
      </div>
    </div>
  </div>
</template>

<script setup>
import { useMonitorStore } from '@/store/use-monitor-store';

const monitorStore = useMonitorStore();
const { cameraStatus } = storeToRefs(monitorStore);

const getStatusClass = (status) => {
  switch (status) {
    case 0: return 'status-idle';
    case 1: return 'status-error';
    case 2: return 'status-success';
    default: return 'status-unknown';
  }
};

const getStatusText = (status) => {
  switch (status) {
    case 0: return '未开启';
    case 1: return '异常';
    case 2: return '正常';
    default: return '未知';
  }
};
</script>

<style scoped>
.camera-monitor-status {
  padding: 16px;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
}

.status-indicator {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.status-idle { color: #666; }
.status-error { color: #f56565; }
.status-success { color: #48bb78; }

.detailed-status {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.video-status, .audio-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.disabled {
  opacity: 0.5;
  filter: grayscale(1);
}

.error-text {
  font-size: 12px;
  color: #f56565;
}

.status-tips {
  font-size: 14px;
}

.tip {
  padding: 8px 12px;
  border-radius: 4px;
  margin-top: 8px;
}

.tip.success {
  background-color: #f0fff4;
  color: #38a169;
  border: 1px solid #9ae6b4;
}

.tip.warning {
  background-color: #fffaf0;
  color: #dd6b20;
  border: 1px solid #fbd38d;
}

.tip.error {
  background-color: #fff5f5;
  color: #e53e3e;
  border: 1px solid #feb2b2;
}
</style>
```

## 状态逻辑说明

### 状态计算规则

1. **整体状态计算**：
   - 视频正常(2) → 整体正常(2)，即使音频异常
   - 视频异常(1) → 整体异常(1)，无论音频状态
   - 都未开启(0) → 整体未开启(0)

2. **错误处理**：
   - 根据 `streamType` 分别处理音视频错误
   - 麦克风错误不会影响摄像头的视频功能
   - 摄像头错误会影响整体功能

3. **UI表现**：
   - 显示具体的音视频设备状态
   - 根据不同组合情况给出相应提示
   - 麦克风禁用时显示禁用图标，但不影响整体考试

## 兼容性

这个改动是向后兼容的：
- 原有的 `status` 字段仍然可用
- 新增的细化状态为可选功能
- 不使用细化状态的组件不受影响
