flowchart TD
    %% ---------- 全局开始 ----------
    Start([开始：用户访问 /monitor/:seqId/:examId])
    Start --> A1[monitor.vue setup]
    A1 --> A2[实例化 useMonitorStore]
    A1 --> A3[调用 useGlobalInit Hook]

    subgraph 全局初始化
        A3 --> B1[getBaseInfo & getConfig API 请求]
        B1 --> B2[更新 store.examBaseInfo & store.examConfig]
        B2 --> B3{是否已交卷或时间结束?}
        B3 -- "是" --> B4[路由替换 -> /exam-list/:seqId]
        B3 -- "否" --> B5[examInfoRequestStatus = SUCCESS]
    end

    A1 --> A4[调用 initCurrentStep -> getStageQuery API]
    A4 --> A5[更新 store.step & store.hasInitStep]
    A1 --> A6[调用 initMqtt?]
    A6 -- "满足条件" --> C1[chatService.connect]
    A6 -- "否则" --> C2[跳过 MQTT]
    A1 --> A7[调用 initBodyDetect?]
    A7 -- "需要" --> D1[NebulaMatrix.init]
    D1 --> D2[NebulaMatrix.onRuntimeInitialized -> store.nebulaLoaded=true]
    A7 -- "不需要" --> D3[跳过活体 SDK]

    %% ---------- 时间与心跳 ----------
    subgraph 心跳与时间
        TS1[useTimeStore 初始化 timeCenter]
        TS1 --> TS2[每 15s 调用 postHeartBeat API]
        TS2 --> TS3[更新 lastServerTime/lastLocalTime 或估算时间]
        TS1 --> TS4[每 ~33ms 更新 currentTime]
        TS4 --> U1[驱动相关 computed: examStatus, examEndCountdown…]
    end

    %% ---------- 渲染不同阶段 ----------
    A5 --> R0{当前阶段 step?}
    R0 -- "设备调试" --> DET[设备调试阶段\n]
    R0 -- "准备完成" --> PREP[考前准备完成\n]
    R0 -- "正式答题" --> REP[正式答题阶段\n]

    %% ---------- 设备调试阶段 ----------
    subgraph 设备调试阶段
        DET --> S1[生成 stepList]
        S1 --> S2[渲染 b-step 与 当前步骤组件]

        %% 子步骤示例：摄像头检测
        S2 --> Step_Device[DeviceCheck 组件]
        Step_Device --> SD1[调用 monitorStore.openCameraMonitor]

        subgraph 打开摄像头监控
            SD1 --> M1[Media.getClient]
            M1 --> M2[_getSign API 获取鉴权]
            M2 --> M3[NEBULARTC.createClient]
            M3 --> M4[client.join]
            M4 --> M5[client.createStream]
            M5 --> M6[stream.initialize -> publish]
            M6 --> M7[监听流/连接事件]
            M7 --> M8[更新 store.STATUS.camera]
        end

        Step_Device --> SD2[emit 更新步骤状态/下一步可用]

        %% 用户点击“下一步”
        S2 -- "点击下一步" --> NEXT{下一步可用?}
        NEXT -- "否" --> WARN[无效]
        NEXT -- "是" --> STEP_CHECK[调用 getNextCheckResult]

        STEP_CHECK -- "校验失败" --> CB[cb 显示提示]
        STEP_CHECK -- "校验成功" --> FACE_CONFIRM{需人脸确认?}
        FACE_CONFIRM -- "是" --> PC1[弹确认Dialog]
        PC1 --> PC2[用户 Confirm -> API -> 更新 store]
        PC2 --> GO1[goNext]
        FACE_CONFIRM -- "否" --> GO1[goNext]

        subgraph goNext
            GO1 --> G1[计算 nextStep]
            G1 -- "到最后PREPARE" --> P1[stage=-4 -> API -> 更新 store.step]
            G1 -- "到最后EXAM" --> E1[stage=5 -> API -> 更新 store.step]
            G1 -- "中间步骤" --> R1[API -> 更新 store.step]
        end

        %% 同步监控状态到步骤条
        subgraph 状态同步
            U1 -- "监控状态更新" --> W1[watch store.STATUS]
            W1 --> W2[syncStepStaus 更新步骤条状态]
            W2 --> W3[上报埋点]
        end
    end

    %% ---------- 正式答题阶段 ----------
    subgraph 正式答题阶段
        REP --> H1[初始化 Hooks]

        subgraph HooksInit
            H1 --> HM[useMonitor]
            H1 --> HF[useFullScreen]
            H1 --> HS[useSwitchScreen]
            H1 --> HR[useReLogin]
            H1 --> HA[useAntiCheat]
            H1 --> HD[useDialogHint]
        end

        REP --> C1[渲染 Exam/Eval/... 组件]
        C1 --> UI[用户答题交互]

        %% 超时自动交卷
        subgraph 自动交卷
            U1 -- "examEndCountdown <= 0" --> T1[forceSubmitExam]
            T1 --> CP[调用 commitPaper/onSubmitPaper]
        end

        %% 手动或强制交卷
        UI -- "点击提交" --> T2[调用 commitPaper/onSubmitPaper]
        HS -- "触发" --> T3[forceSubmitExam]
        HR -- "触发" --> T4[commitPaper]
        REP --> T2
        T2 & T3 & T4 --> CP[API postCommit -> navigate /status]
    end

    %% ---------- MQTT 事件 ----------
    subgraph MQTT通信
        C1 -- "listener" --> MQTTR[emitter.on]
        MQTTR -- "指令类型" --> MQ1[处理逻辑-弹窗/交卷/关RTC]
    end

    %% ---------- 退出与清理 ----------
    subgraph 卸载与清理
        MonitorVue -- "onBeforeUnmount" --> UQ1[removeMqtt]
        UQ1 --> UQ2[store.closeRtcAll]
        UQ2 --> UQ3[调用 cleanup*]
        UQ3 --> UQ4[RTC 清理 -> 重置 store.STATUS]
        UQ4 --> UQ5[通知手机端关闭 API]
        MonitorVue -- "onBeforeUnmount" --> UQ6[emitter.off]

        subgraph Hooks_onUnmounted
            HF --> UQ7[清理FullScreen]
            HS --> UQ8[清理SwitchScreen]
            HA --> UQ9[清理AntiCheat]
        end
    end

    %% ---------- 结束 ----------
    UQ5 --> End([结束])
