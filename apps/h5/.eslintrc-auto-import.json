{"globals": {"Component": "readonly", "ComponentPublicInstance": "readonly", "ComputedRef": "readonly", "DirectiveBinding": "readonly", "EffectScope": "readonly", "ExtractDefaultPropTypes": "readonly", "ExtractPropTypes": "readonly", "ExtractPublicPropTypes": "readonly", "InjectionKey": "readonly", "MaybeRef": "readonly", "MaybeRefOrGetter": "readonly", "PropType": "readonly", "Ref": "readonly", "SessionStorage": "readonly", "Slot": "readonly", "Slots": "readonly", "Storage": "readonly", "VNode": "readonly", "WritableComputedRef": "readonly", "acceptHMRUpdate": "readonly", "apmSendAxiosError": "readonly", "apmSendCustom": "readonly", "apmSendRouteErr": "readonly", "clearPMcustomUid": "readonly", "computed": "readonly", "createApp": "readonly", "createPinia": "readonly", "customComponents": "readonly", "customRef": "readonly", "defineAsyncComponent": "readonly", "defineComponent": "readonly", "defineStore": "readonly", "effectScope": "readonly", "formatParams": "readonly", "getActivePinia": "readonly", "getCurrentInstance": "readonly", "getCurrentScope": "readonly", "getPMcustomUid": "readonly", "getVisibilityState": "readonly", "h": "readonly", "inject": "readonly", "isExternal": "readonly", "isProxy": "readonly", "isReactive": "readonly", "isReadonly": "readonly", "isRef": "readonly", "jumpExamList": "readonly", "jumpLogin": "readonly", "mapActions": "readonly", "mapGetters": "readonly", "mapState": "readonly", "mapStores": "readonly", "mapWritableState": "readonly", "markRaw": "readonly", "nextTick": "readonly", "onActivated": "readonly", "onBeforeMount": "readonly", "onBeforeRouteLeave": "readonly", "onBeforeRouteUpdate": "readonly", "onBeforeUnmount": "readonly", "onBeforeUpdate": "readonly", "onDeactivated": "readonly", "onErrorCaptured": "readonly", "onMounted": "readonly", "onRenderTracked": "readonly", "onRenderTriggered": "readonly", "onScopeDispose": "readonly", "onServerPrefetch": "readonly", "onUnmounted": "readonly", "onUpdated": "readonly", "onWatcherCleanup": "readonly", "parseURL": "readonly", "postLog": "readonly", "provide": "readonly", "reactive": "readonly", "readonly": "readonly", "ref": "readonly", "resolveComponent": "readonly", "setAPMcustomUid": "readonly", "setActivePinia": "readonly", "setMapStoreSuffix": "readonly", "shallowReactive": "readonly", "shallowReadonly": "readonly", "shallowRef": "readonly", "storeToRefs": "readonly", "toRaw": "readonly", "toRef": "readonly", "toRefs": "readonly", "toValue": "readonly", "triggerRef": "readonly", "unref": "readonly", "useAttrs": "readonly", "useCssModule": "readonly", "useCssVars": "readonly", "useId": "readonly", "useLink": "readonly", "useModel": "readonly", "useRoute": "readonly", "useRouter": "readonly", "useSlots": "readonly", "useSwitchScreen": "readonly", "useTemplateRef": "readonly", "userCutScreenDialog": "readonly", "userForceRefresh": "readonly", "userKickLoginOutDialog": "readonly", "visibilityChange": "readonly", "watch": "readonly", "watchEffect": "readonly", "watchPostEffect": "readonly", "watchSyncEffect": "readonly"}}