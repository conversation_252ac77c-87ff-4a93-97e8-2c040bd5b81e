{"name": "fe-kanjian-zhice-h5", "version": "0.0.0", "type": "module", "scripts": {"build:pre": "vite build --mode pre", "build:prod": "vite build --mode prod", "build:qa": "vite build --mode qa", "build:rd": "vite build --mode rd", "build:webfont": "boss-cli build:icons -b -d src/assets/svg --name bossicon  --namespace b-icon ", "dev": "vite --mode dev --force", "dev:pre": "vite --mode pre --force", "dev:qa": "vite --mode qa --force", "dev:rd": "vite --mode rd --force", "lint": "eslint --fix --cache", "lint:fix": "eslint src --fix --ext .ts,.tsx,.vue,.js,.jsx", "preview": "vite preview", "up-design": "pnpm install -D @boss/eslint-config@latest @boss/stylelint-config@latest @boss/prettier-config@latest @boss/tsconfig@latest"}, "dependencies": {"@vant/touch-emulator": "1.4.0", "v-viewer": "3.0.21", "vconsole": "3.15.1", "viewerjs": "1.11.7"}, "devDependencies": {"@rollup/plugin-inject": "5.0.5", "sourcemap-set-path-plugin": "1.2.3", "vant": "4.9.19"}}