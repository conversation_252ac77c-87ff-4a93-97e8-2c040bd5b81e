html,
body {
    // height: 100%;
    // background-color: #f5f5f5;
    background: linear-gradient(180deg, #e6f5f5, #f5fafa);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: var(--van-text-color);
    touch-action: manipulation; // 禁止双击放大
}

#app {
    max-width: 750px;
    margin: 0 auto;
    background-color: #fff;
}

/* 针对 iPhone X 及以上型号的刘海屏和底部安全区域 */
/* stylelint-disable */
@media only screen and (device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) {
    /* constant在iOS < 11.2的版本中生效，env在iOS >= 11.2的版本中生效 */
    /* 在顶部增加安全区域的 padding */
    body {
        padding-top: constant(safe-area-inset-top);
        padding-top: env(safe-area-inset-top);
    }

    /* 在底部增加安全区域的 padding */
    .footer {
        padding-bottom: constant(safe-area-inset-bottom);
        padding-bottom: env(safe-area-inset-bottom);
    }
}


a:visited,
a:link,
a:active {
    color: var(--van-primary-color);
}

@font-face {
    font-family: kanzhun; // 方正兰亭黑简体
    src: url('@/assets/fonts/kanzhun.ttf') format('truetype');
}


// 统一处理滚动条样式
* {
    &::-webkit-scrollbar {
        width: 4px;
        // border-radius: 3px;
    }
    // &::-webkit-scrollbar-corner {
    //     // background-color: red;
    //     // background: #e0e0e0;
    // }
    &::-webkit-scrollbar-thumb {
        height: 10px;
        border-radius: 5px;
        border: 0;
        background-color: #e0e0e0;
    }
    &::-webkit-scrollbar-track-piece {
        background-color: #fff;
        border-radius: 0;
    }
}
