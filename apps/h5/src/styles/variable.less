:root:root {
    --van-tree-select-nav-background: #f8f8f8;
    --van-floating-bubble-background: transparent;
    --van-back-top-background: #fff;
    --van-back-top-text-color: var(--van-blue);
    --van-button-mini-height: 22px;
    --van-button-mini-padding: 0 9px 0 10px;
    --van-button-radius: var(--van-radius-lg);
    --van-button-default-height: 42px;
    --van-button-normal-font-size: 15px;
    --van-button-disable-background: #e0e0e0; // vant组件库中没有定义这个变量
    --van-checkbox-label-margin: var(--van-padding-base);
    --van-gray-1: #f5f5f6;
    --van-gray-3: #f0f0f0;
    --van-gray-5: #ccc;
    --van-gray-7: #858585;
    --van-gray-8: #5e5e5e; // vant gray系列color最深只到序号8
    --van-gray-9: #2a2a2a;
    --van-gray-10: #292929;
    --van-gray-11: #141414;
    --van-gray-12: #1f1f1f;
    --van-progress-background: #e3f5f5;
    // 主色
    --van-blue-light: rgba(21, 179, 179, 0.16);
    --van-blue: #15b3b3;
    --van-blue-dark: #0d9ea3; // 品牌高亮重要文字色、超链接文本颜色
    // 警告色
    --van-orange-light: rgba(255, 120, 71, 0.16);
    --van-orange: #ff7847;
    // 文字色
    --text-primary-color: var(--van-blue-dark);
    --van-text-color-heavy-1: var(--van-gray-9);
    --van-text-color-heavy-2: var(--van-gray-10);
    --van-text-color-heavy-3: var(--van-gray-11);
    --van-text-color-heavy-4: var(--van-gray-12);
    // 透明度
    --van-disabled-opacity: 1;
    // 水印层级
    --van-watermark-z-index: 1000;
}
