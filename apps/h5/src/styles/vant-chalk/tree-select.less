/*
<template #nav-text="item">
    <div class="side-item">
        <span class="name">{{ item.text }}</span>
        <span v-if="item.count > 0" class="count">{{ item.count }}</span>
    </div>
</template>
*/

.van-tree-select {
    .van-sidebar {
        min-width: 115px;

        .van-sidebar-item {
            padding: 0;

            &::before {
                display: none;
            }

            &.van-sidebar-item--select {
                background-color: #fff;

                .van-sidebar-item__text {
                    .side-item {
                        .name {
                            color: var(--theme-color-8);
                            font-weight: 500;
                        }
                    }
                }
            }

            // width: 115px;
            .van-sidebar-item__text {
                width: 100%;

                // 左侧自定义类名
                .side-item {
                    width: 100%;
                    padding: 15px 10px 15px 20px;
                    display: inline-flex;
                    justify-content: space-between;
                    align-items: center;

                    .name {
                        color: #333333;
                    }

                    .count {
                        flex-shrink: 0;
                        min-width: 18px;
                        line-height: 16px;
                        padding: 1px 4px;
                        border-radius: 18px;
                        background-color: var(--theme-color-7);
                        font-weight: 500;
                        color: #fff;
                        text-align: center;
                        transform: scale(0.9);
                    }
                }
            }
        }
    }

    // 右侧每行显示两个
    &.half {
        .van-tree-select__content {
            display: flex;
            flex-wrap: wrap;
            flex-basis: 50%;
            justify-content: space-between;
            height: fit-content;
            max-height: 100%;

            .van-tree-select__item {
                width: calc(~'50% - 6px');
            }
        }
    }

    .van-tree-select__content {
        padding: 0 20px;

        .van-tree-select__item {
            padding: 8px 16px;
            font-weight: 400;
            text-align: center;
            font-size: 13px;
            line-height: 20px;

            color: #333333;

            background: #f8f8f8;
            border-radius: 2px;
            border: 1px solid transparent;

            white-space: initial;
            text-overflow: initial;
            transition: all 0.2s;

            margin-bottom: 12px;

            // 去掉右侧的对钩
            .van-badge__wrapper {
                display: none;
            }

            &--active {
                background-color: var(--theme-color-1);
                border-color: var(--theme-color-6);
                color: var(--theme-color-8);
                font-weight: 500;
            }
        }
    }
}
