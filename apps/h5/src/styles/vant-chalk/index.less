@import './tree-select.less';
@import './toast.less';
@import './button.less';
@import './user-kick-login-out-dialog.less';


/* 文本高亮 */
.text-primary {
    color: var(--text-primary-color);
}

/* 图片查看器覆盖样式 */
.viewer-backdrop {
    background-color: rgba(0, 0, 0, 0.85) !important;
    .viewer-footer {
        display: none;
    }
}

// 隐藏滚动条
.hide-scroll-bar {
    -ms-overflow-style: none;
    overflow: -moz-scrollbars-none;
    &::-webkit-scrollbar {
        width: 0 !important;
        display: none;
    }
}

// 单行省略
.ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 解决iOS不流畅问题 */
.ios-scrolling-touch {
    -webkit-overflow-scrolling: touch;
}