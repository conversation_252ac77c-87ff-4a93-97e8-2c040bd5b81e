import { createApp } from 'vue';
import { customComponents } from '@/components';
import { pinia } from '@/stores';
import Vue<PERSON>iewer from 'v-viewer';
// import Vconsole from 'vconsole'
import App from './App.vue';
import router from './router';
import 'virtual:svg-icons-register'; // svg-sprite
import '@/styles/index.less';
import '@/utils/vant-setting';
import 'viewerjs/dist/viewer.css';
import { DEPLOY_ENV } from '@/constant/app';
import BossAnalytics from '@datastar/warlock-jssdk';

export const app = createApp(App);

// if (DEPLOY_ENV !== 'prod') {
//     // eslint-disable-next-line no-new
//     new Vconsole()
// }

if (DEPLOY_ENV !== 'deployEnv') {
    // 添加Apm监控
    app.config.errorHandler = (err, vm, info) => {
        try {
            (window as any).vueErrorHandler(err, vm, info);
        } catch (err) {}
    };
}

app.use(pinia).use(router).use(customComponents).use(VueViewer).mount('#app');
BossAnalytics.init({
    server_url: 'https://logapi-dev.weizhipin.com/dap/api/json',
    token: 'kaoping3CE17456A9D81D9CBC20A2436',
    app_ver: '1.0',
    app_name: 'kaoping',
});
