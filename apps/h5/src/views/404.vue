<template>
    <div id="error-404">
        <div class="box" />
        <p>很抱歉，您访问的页面不存在。</p>
    </div>
</template>

<script lang="ts" setup>
import { apmSendRouteErr } from '@/utils/apm';

defineOptions({
    name: 'ErrorPage404',
    beforeRouteEnter: async (to, from, next) => {
        let url = '';
        if (to.redirectedFrom) {
            const { fullPath } = to.redirectedFrom;
            url = fullPath;
        }

        apmSendRouteErr({
            redirected: url,
        });

        next();
    },
});
</script>

<style lang="less" scoped>
#error-404 {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .box {
        width: 400px;
        height: 400px;
        background: url('@/assets/images/404.png');
        background-size: cover;
        background-repeat: no-repeat;
    }
}
</style>
