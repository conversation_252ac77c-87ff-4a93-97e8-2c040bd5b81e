<template>
    <div class="container">
        <VanImage :src="complete" />
        <VanRow justify="center" align="center" class="title-m" style="margin-top: 20px">
            <VanImage :src="success" />
            <VanCol class="text-center title-l"> 测验1 答题完成 </VanCol>
        </VanRow>
        <VanRow justify="center" style="margin-top: 8px">
            <VanCol class="desc-m"> 感谢你的配合 </VanCol>
        </VanRow>
        <VanRow justify="center" style="margin-top: 8px">
            <VanCol class="desc-m"> （预览试卷仅预览前3道题） </VanCol>
        </VanRow>
        <VanRow justify="center" class="w-full" style="margin-top: 36px; width: 200px">
            <VanCol span="24">
                <VanButton style="height: 44px" type="primary" size="large" @click="handleClick"> 返回测验列表 </VanButton>
            </VanCol>
        </VanRow>
    </div>
</template>

<script lang="ts" setup>
import complete from '@/assets/svg-sprite/complete.svg';
import success from '@/assets/svg-sprite/success.svg';

defineOptions({
    name: 'Complete',
});
const emit = defineEmits(['reset']);

function handleClick() {
    emit('reset');
}
</script>

<style lang="less" scoped>
.container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
</style>
