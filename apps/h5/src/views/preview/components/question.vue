<template>
    <div v-if="loading" class="base-paper-content loading-wrap">
        <VanLoading color="#15b3b3" />
    </div>
    <div class="base-paper-content">
        <PaperTime @paperTimeEnd="onFinished" />
        <Ordinary v-if="examInfo.answerListType === 1" @submitPaper="onFinished" />
        <Exam v-if="examInfo.answerListType === 4" @submitPaper="onFinished" />
    </div>
</template>

<script setup lang="ts">
import { useExamStore } from '@/stores/exam/index';
import { usePaperStore } from '@/stores/paper/index';
import { onMounted, ref } from 'vue';
import PaperTime from '@/views/evaluation/question/components/paper-time.vue';
import Exam from '@/views/evaluation/question/exam/index.vue';
import Ordinary from '@/views/evaluation/question/ordinary/index.vue';

// 场次时间结束会触发
defineOptions({
    name: 'EvaluationPage',
});

const props = defineProps({
    previewKey: {
        type: String || null,
        default: '',
    },
});

const emit = defineEmits(['next']);

const loading = ref(true);
const examStore = useExamStore();
const examQuestionStore = usePaperStore();
const examInfo = computed(() => examStore.examBaseInfo.examInfo);

function onFinished() {
    emit('next');
}

async function getData() {
    loading.value = true;

    await examQuestionStore.getPreviewQuestion({
        key: props.previewKey,
    });

    loading.value = false;
}
onMounted(() => {
    getData();
});
</script>

<style lang="less" scoped>
.base-paper-content {
    height: 100vh;
    flex: 1;
    display: flex;
    flex-direction: column;

    &.loading-wrap {
        justify-content: center;
        align-items: center;
    }
}
</style>
