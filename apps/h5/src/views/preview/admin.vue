<template>
    <div class="exam-wrap">
        <div v-if="questionStore.allQuestionList.length && templateName" class="exam-content">
            <Progress v-if="questionType !== 'group'" />
            <div class="question-content">
                <BackgroundInfoQuestion v-if="parentType && parentType === 20" :questionBackgroundInfo="questionBackgroundInfo" :questionInfo="questionStore.currentQuestion">
                    <component :is="templateName" :hideBackgroundEntry="true" :questionInfo="questionStore.currentQuestion" :answer="answer" />
                </BackgroundInfoQuestion>
                <component :is="templateName" v-else :questionInfo="questionStore.currentQuestion" :answer="answer" />
            </div>
            <BtnControl v-if="questionType !== 'group'" :isReview="true" @prev="onPrev" @next="controlNext()" />
        </div>
        <div v-else class="status-wrap">
            <p class="text">暂无数据-请先选择数据</p>
        </div>
        <SceneDetailPopup />
        <!-- :questionInfo="examQuestionStore.currentQuestion" -->
        <!-- :answer="examQuestionStore.currentQuestion?.answerContent" -->
    </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue';
import { uniqueId } from 'lodash-es';
import BackgroundInfoQuestion from '@/views/evaluation/question/exam/component/background-info-question.vue';
import { usePaperStore } from '@/stores/paper/index';
import Progress from '@/views/evaluation/question/components/progress.vue';

import { questionTypeMap } from '@/views/evaluation/constant';
import BtnControl from '@/views/evaluation/question/ordinary/component/btn-control.vue';
import SceneDetailPopup from '@/views/evaluation/question/ordinary/component/scene-detail-popup.vue';

const questionStore = usePaperStore();

const answer = ref('');
const questionBackgroundInfo = computed(() => {
    return {
        backgroundInfo: questionStore.currentQuestion.parentQuestion.backgroundInfo,
        backgroundFiles: questionStore.currentQuestion.parentQuestion.backgroundFiles || [],
    };
});
const questionType = computed(() => {
    return questionStore.currentQuestion?.questionType ?? 0;
});

const parentType = computed(() => {
    return questionStore.currentQuestion?.parentType ?? 0;
});

const templateName = computed(() => {
    let name = questionTypeMap[questionType.value]?.templateName;
    if (questionType.value === 20) {
        controlNext();
        name = true;
    }
    return name;
});

function controlNext() {
    const { jumpToQuestion, currentQuestionIndex } = questionStore;
    jumpToQuestion(currentQuestionIndex + 1);
}

function onPrev() {
    const { jumpToQuestion, currentQuestionIndex } = questionStore;
    jumpToQuestion(currentQuestionIndex - 1);
}

function messageEvent(event: any) {
    if (event?.data && event.data.type === 'setFormData') {
        const data = JSON.parse(event?.data.data);
        const newQuestionList = [];
        let questionSnapshot = {
            ...data,
        };

        const children: { questionSnapshot: any }[] = [];

        if (data.questionType && (data.questionType === 11 || data.questionType === 20)) {
            const id = 11;

            questionSnapshot = {
                ...questionSnapshot,
                backgroundInfo: data.backgroundInfo,
                backgroundFiles: data.backgroundFiles || [],
            };

            const childrenQuestionList = (data.childrenQuestionList as any[]) ?? [];

            childrenQuestionList.forEach((item) => {
                const newItem = {
                    ...item,
                    questionOptionList: item.questionOptionList.map((option: any) => {
                        return {
                            ...option,
                            encryptId: option.encryptId || uniqueId(),
                        };
                    }),
                };

                if (data?.questionType === 11) {
                    newItem.parentEncryptId = item.parentEncryptId || id;
                    newItem.parentId = item.parentId || id;
                }

                if (data?.questionType === 20) {
                    newItem.parentEncryptId = null;
                    newItem.parentId = null;
                    newItem.parentType = 20;
                    newItem.parentQuestion = data;
                }

                children.push({
                    ...newItem,
                    answerStatus: 0,
                    questionSnapshot: {
                        ...newItem,
                    },
                });
            });
        }

        newQuestionList.push({
            ...data,
            answerStatus: 0,
            questionSnapshot: {
                ...questionSnapshot,
            },
            children,
        });

        questionStore.initData({
            reviewRuleType: 3,
            questionList: [...newQuestionList],
        });
    }
}

onMounted(() => {
    questionStore.isPreview = true; // 开启预览模式
    // 监听父页面发送的消息
    window.addEventListener('message', messageEvent);
});

onUnmounted(() => {
    questionStore.isPreview = false; // 开启预览模式
    // 移除监听
    window.removeEventListener('message', messageEvent);
});
</script>

<style lang="less" scoped>
.exam-wrap {
    min-height: 100vh;
    height: 100vh;
    display: flex;
    flex-direction: column;
    transition: all 0.15s;

    .exam-head {
        min-height: 50px;
    }

    .exam-content {
        flex: 1;
        overflow: auto;
        display: flex;
        flex-direction: column;
        .question-content {
            flex: 1;
        }
    }
}

.status-wrap {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .text {
        text-align: center;
        font-size: 14px;
        color: #999;
    }
}
</style>
