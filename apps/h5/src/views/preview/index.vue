<template>
    <template v-if="!loading">
        <Overview v-if="step === 1" :isPreview="true" @previewNext="next" />
        <Question v-if="step === 2" :previewKey="previewKey as string" @next="next" />
        <Complete v-if="step === 3" @reset="reset" />
    </template>
</template>

<script setup lang="ts">
import { useExamStore } from '@/stores/exam/index';
import { usePaperStore } from '@/stores/paper/index';
import { showFailToast } from 'vant';
import { computed, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import Overview from '@/views/evaluation/guidance/index.vue';
import Complete from './complete.vue';
import Question from './components/question.vue';

defineOptions({
    name: 'Preview',
});

const examQuestionStore = usePaperStore();

const commonStore = useExamStore();
const $route = useRoute();
const previewKey = computed(() => $route.query.key);
const step = ref(1);
const loading = ref(true);

function next() {
    step.value = step.value >= 3 ? 3 : step.value + 1;
}
function reset() {
    step.value = 1;
}

async function getPreviewBaseInfo() {
    loading.value = true;
    await commonStore.fetchPreviewBaseInfo({ key: previewKey.value as string });
    examQuestionStore.isPreview = true;
    loading.value = false;
}
onMounted(() => {
    getPreviewBaseInfo();
});
</script>
