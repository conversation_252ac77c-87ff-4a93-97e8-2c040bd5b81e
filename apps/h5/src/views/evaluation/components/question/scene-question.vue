<template>
    <div
        class="scene-question-wrap"
        :class="{
            popup: isPopup,
        }"
    >
        <div class="title-s">背景信息</div>
        <div class="content">
            <p class="desc-s">本题为情景题，共{{ childList.length }}道小题，请阅读背景信息后再进行答题，背景信息如下：</p>
            <div v-if="currentQuestion?.backgroundFiles && currentQuestion.backgroundFiles.length" class="background-image">
                <ImgWrap :imgList="currentQuestion.backgroundFiles" />
            </div>
            <p class="desc-s text-desc">
                {{ currentQuestion?.backgroundInfo }}
            </p>
        </div>
        <div v-if="!isPopup" class="btn-wrap">
            <VanButton class="btn" type="primary" plain @click="onComplete"> 阅读完成 </VanButton>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import ImgWrap from './component/img-wrap.vue';
import { usePaperStore } from '@/stores/paper';

defineProps({
    isPopup: {
        type: Boolean,
        default: false,
    },
});
const emit = defineEmits(['onComplete']);
const paperStore = usePaperStore();

const currentQuestion = computed(() => {
    const { backgroundInfoList, currentQuestion } = paperStore;
    const { backgroundInfoEncryptId } = currentQuestion;
    const data = backgroundInfoList.find((item) => item.encryptId === backgroundInfoEncryptId);

    return data;
});

const childList = computed(() => {
    const { allQuestionList, currentQuestion } = paperStore;
    const { backgroundInfoEncryptId } = currentQuestion;
    const list = allQuestionList.filter((item) => item.backgroundInfoEncryptId === backgroundInfoEncryptId);
    return list;
});

function onComplete() {
    emit('onComplete');
}
function checkAnswer() {
    return [true, '', {}];
}
defineExpose({
    check: checkAnswer,
    checkQuestionStatus: checkAnswer,
});
</script>

<style lang="less" scoped>
.scene-question-wrap {
    // min-height: 100vh - 60px;
    // height: 100vh - 60px;
    flex: 1;
    display: flex;
    flex-direction: column;

    &.popup {
        height: calc(100vh - 200px);
    }

    .title-s {
        padding: 0 20px 12px;
    }
    .content {
        padding: 0 20px;
        overflow: scroll;
        -webkit-overflow-scrolling: touch;
        transform: rotateZ(0);
        flex: 1;

        .background-image {
            margin: 12px 0;
            width: 100%;
        }

        .text-desc {
            color: #141414;
            word-break: break-all;
            white-space: pre-wrap;
        }
    }

    .btn-wrap {
        padding: 10px 20px 20px;

        .btn {
            width: 100%;
        }
    }
}
</style>
