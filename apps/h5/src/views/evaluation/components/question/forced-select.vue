<template>
    <div class="single-choice-question-wrap">
        <QuestionTitle :title="questionTitle" :questionInfo="questionInfo" />
        <VanCheckboxGroup>
            <VanCheckbox
                v-for="item of questionOption"
                :key="item.encryptId"
                :class="{ 'van-radio-checked': findCheckedList(item.encryptId) }"
                :name="item.encryptId"
                @click="changeAnswer(item)"
            >
                <OptionItem
                    :imgList="item.files"
                    :isChecked="!!findCheckedList(item.encryptId)"
                    :icon="String(findCheckedList(item.encryptId))"
                    :content="`${item.letters}. ${item.optionContent}`"
                />
            </VanCheckbox>
        </VanCheckboxGroup>
    </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import OptionItem from './component/option-item.vue';
import QuestionTitle from './component/question-title.vue';

const props = withDefaults(
    defineProps<{
        questionInfo?: any; // 使用接口定义对象类型
        answer?: string | string[]; // 联合类型，支持 string 或 string[]
    }>(),
    {
        questionInfo: () => ({}), // 默认值为空对象
        answer: () => '', // 默认值为空字符串
    },
);
const questionOption = ref<any[]>([]);
const checked = ref<any[]>([]);

const questionTitle = computed(() => {
    const title = props.questionInfo.questionTitle;

    const titStr = title.replace(/___/g, `<span class="answer"></span>`);

    return titStr;
});

function findCheckedList(id: string) {
    let flag = 0;
    const falgIndex = checked.value.findIndex((item) => item?.id === id);
    if (falgIndex > -1) {
        flag = falgIndex + 1;
    }
    return flag;
}

async function changeAnswer(data: any) {
    const { encryptId, letters } = data || {};
    const flagIndex = checked.value.findIndex((item) => item?.id === encryptId);

    // 取消动作
    if (flagIndex > -1) {
        checked.value[flagIndex] = { letters: '', id: '' };
        return;
    }

    // 查看需要补充第几个空
    const needIndex = checked.value.findIndex((item) => !item?.id);
    if (needIndex > -1) {
        checked.value[needIndex] = {
            letters,
            id: encryptId,
        };
    }
}

function checkAnswer() {
    const { encryptId, questionType } = props.questionInfo;
    let flag = true;
    let message = '';

    const data = checked.value.find((item) => item.id === '');

    if (data) {
        message = '请选择完所有的题目';
        flag = false;
    }
    const params = {
        evaPaperSnapshotId: encryptId,
        questionType,
        answerContent: checked.value.map((item) => item.id),
    };

    return [flag, message, params];
}

watch(
    () => props.questionInfo,
    async () => {
        const questionList: any[] = [];
        const { questionOptionList, needSelectedCount } = props.questionInfo;

        // 丰富选项数据 满足展示条件
        if (questionOptionList && questionOptionList.length) {
            questionOptionList.forEach((item: { encryptId: any; optionContent: any; files: any[] }, index: number) => {
                const { encryptId, optionContent, files } = item;
                const letters = String.fromCharCode(64 + Number.parseInt(String(index + 1), 10));
                questionList.push({
                    encryptId,
                    optionContent,
                    letters,
                    files,
                });
            });
        }

        questionOption.value = [...questionList];

        // 回显答案
        const checkedList = Array.from({ length: needSelectedCount });

        const currentAnswerVal = props.answer || [];

        for (let i = 0; i < checkedList.length; i++) {
            const answerId = currentAnswerVal && currentAnswerVal.length ? currentAnswerVal[i] : false;

            let data = { letters: '', id: '' };

            if (answerId) {
                const questionData = questionList.find((item) => item.encryptId === answerId);
                if (questionData) {
                    data = { letters: questionData.letters ?? '', id: answerId };
                }
            }

            checkedList[i] = data;
        }
        checked.value = [...checkedList];
    },
    { immediate: true },
);

watch(
    () => checked.value,
    async () => {
        setTimeout(() => {
            const doms = document.querySelectorAll('.answer');

            checked.value.forEach((data, index) => {
                if (data && doms[index]) {
                    doms[index].innerHTML = data.letters || '&nbsp';
                }
            });
        }, 0);
    },
    { deep: true, immediate: true },
);

defineExpose({
    check: checkAnswer,
});
</script>

<style lang="less" scoped>
.single-choice-question-wrap {
    // padding: 0 20px;

    :deep(.answer) {
        margin: 0 4px;
        display: inline-block;
        width: 40px;
        height: 19px;
        line-height: 20px;
        border-bottom: 1px solid var(--van-text-color-heavy-3);
        text-align: center;
        font-size: 16px;
        font-weight: 600;
        color: var(--van-primary-color);
    }

    :deep(.van-checkbox-group) {
        .van-checkbox {
            padding-bottom: 12px;

            &:last-child {
                padding-bottom: 18px;
            }

            .van-checkbox__icon {
                display: none;
            }
            .van-checkbox__label {
                margin: 0;
                padding: 0 20px;
                width: 100%;
            }
        }
    }
}
</style>
