<template>
    <QuestionTitle :questionInfo="questionInfo" />
    <div class="input-wrap">
        <VanField v-model="answerContentVal" class="input-content" :placeholder="placeholder" autosize :showWordLimit="true" type="textarea" maxlength="300" />
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import QuestionTitle from './component/question-title.vue';

const props = defineProps({
    questionInfo: {
        type: Object,
        default: () => ({}),
    },
    answer: {
        type: [String, Array],
        default: () => '',
    },
});

const answerContentVal = ref<any>('');
const placeholder = ref('');

watch(
    () => props.questionInfo,
    async (val: any) => {
        const { required } = val;
        answerContentVal.value = props.answer || '';
        placeholder.value = required ? '请输入' : '选填';
    },
    {
        deep: true,
        immediate: true,
    },
);

function checkAnswer() {
    const { required, encryptId, questionType } = props.questionInfo;
    let flag = true;
    let message = '';
    const val = answerContentVal.value;

    if (required) {
        if (!val) {
            flag = false;
            message = '请输入您的回答';
        }
    }

    const params = {
        evaPaperSnapshotId: encryptId,
        questionType,
        answerContent: val,
    };

    return [flag, message, params];
}

defineExpose({
    check: checkAnswer,
});
</script>

<style lang="less" scoped>
.input-wrap {
    padding: 0 20px;
    .input-content {
        padding: 12px;
        background: #f5f5f6;
        border-radius: 8px;
        :deep(.van-field__control) {
            color: #141414;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            min-height: 200px;
        }

        :deep(.van-field__word-num) {
            color: #858585;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 18px;
        }
    }
}
</style>
