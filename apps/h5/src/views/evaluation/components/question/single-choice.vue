<template>
    <QuestionTitle :questionInfo="questionInfo" />
    <VanRadioGroup v-model="checked">
        <VanRadio v-for="item of questionInfo.questionOptionList" :key="item.encryptId" :name="item.encryptId" @click="clickAnswer(item.encryptId)">
            <OptionItem :imgList="item.files" :content="item.optionContent" :isChecked="checked === item.encryptId" />
        </VanRadio>
    </VanRadioGroup>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { paperQuestionNextMethod } from '../../constant';
import OptionItem from './component/option-item.vue';
import QuestionTitle from './component/question-title.vue';

const props = defineProps({
    questionInfo: {
        type: Object,
        default: () => ({}),
    },
    answer: {
        type: [String, Array],
        default: () => '',
    },
    nextMethodType: {
        type: Number,
        default: 0,
    },
});

const emit = defineEmits(['select']);

const lastChecked = ref('');
const checked = ref('');
watch(
    () => [props.questionInfo, props.answer],
    async () => {
        const answer = props.answer as string;

        lastChecked.value = answer;
        checked.value = answer;
    },
    { immediate: true },
);

function clickAnswer(value: any) {
    const [flag, , params] = checkAnswer(value);

    if (!flag) {
        return;
    }

    checked.value = value;

    // 如果配置项是 自动提交
    if (props.nextMethodType === paperQuestionNextMethod.auto) {
        setTimeout(() => {
            emit('select', params);
        }, 200);
    }
}

function checkAnswer(value: string) {
    let flag = true;
    let message = '';
    const val = value || checked.value;
    const { encryptId, questionType } = props.questionInfo;

    if (!val) {
        flag = false;
        message = '答案不能为空';
    }

    const params = {
        evaPaperSnapshotId: encryptId,
        questionType,
        answerContent: val,
    };

    return [flag, message, params];
}

defineExpose({
    check: checkAnswer,
});
</script>

<style lang="less" scoped>
:deep(.van-radio) {
    padding-bottom: 12px;

    &:last-child {
        padding-bottom: 18px;
    }

    .van-radio__icon {
        display: none;
    }

    .van-radio__label {
        margin: 0;
        padding: 0 20px;
        width: 100%;
    }
}
</style>
