<template>
    <div v-if="commitAnswerLoading" class="commit-answer-loading-wrap">
        <div class="commit-answer-loading-content">
            <p
                class="text"
                :class="{
                    show: commitAnswerLoading,
                }"
            >
                提交中...
            </p>
        </div>
    </div>
    <component
        :is="templateName"
        ref="questionComponentRef"
        :questionInfo="paperStore.currentQuestion"
        :answer="answer"
        :nextMethodType="paperStore.config.next_method"
        @select="onSelect"
        @onComplete="onComplete"
    />
</template>

<script setup lang="ts">
import { usePaperStore } from '@/stores/paper';
import { showToast } from 'vant';
import { computed, ref, watch } from 'vue';
import { questionTypeMap } from '../../constant';

defineProps({
    paperTimeEnd: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(['questionChange', 'answerSelect']);

export interface ISceneTemplate {
    id: number | null;
    type: number | string;
    isShow: boolean;
}

const paperStore = usePaperStore();
const questionComponentRef = ref<any>(null);
const commitAnswerLoading = ref(false); // 提交答案 loading(防止重复请求)
const isShowSceneTemplate = ref<ISceneTemplate>({
    id: null,
    type: 11,
    isShow: false,
});

// 当前题-类型
const currentQuestionType = computed(() => {
    const { questionType } = paperStore?.currentQuestion || {};
    return questionType || 1;
});

// 当前题-答案
const answer = computed(() => {
    const { answerContent, answerContentList, questionType } = paperStore.currentQuestion || {};
    let newAnswer = null;
    const answerList = answerContentList || [];
    const config = questionTypeMap[questionType];
    const answersType = config?.answersType;
    if (answersType && answersType === 'array') {
        newAnswer = answerList;
    } else {
        newAnswer = answerContent;
    }

    return newAnswer;
});

// 匹配对应模板
const templateName = computed(() => {
    const { isShow, type } = isShowSceneTemplate.value;
    if (isShow) {
        return questionTypeMap[type].templateName;
    } else {
        return questionTypeMap[currentQuestionType.value].templateName;
    }
});

function onSelect() {
    emit('answerSelect');
}

// 情景信息展示完成回调
function onComplete() {
    isShowSceneTemplate.value = {
        ...isShowSceneTemplate.value,
        isShow: false,
    };

    const { encryptId } = paperStore.currentQuestion;

    emit('questionChange', {
        type: '',
        currentQuestionId: encryptId,
    });
}

function showSceneTemplate(data: ISceneTemplate) {
    isShowSceneTemplate.value = {
        ...data,
    };
}

// 提交答案
function submitAnswer({ isShowToast = true }) {
    return new Promise((resolve) => {
        if (commitAnswerLoading.value) {
            resolve(false);
            return;
        }
        commitAnswerLoading.value = true;

        const [result, message, params] = checkQuestionStatus();

        if (!result && message) {
            if (isShowToast) {
                showToast(message as string);
            }
            commitAnswerLoading.value = false;
            resolve(false);
            return;
        }

        paperStore
            .postCommitAnswer(params)
            .then((res) => {
                if (res) {
                    resolve({ params });
                } else {
                    resolve(false);
                }
            })
            .finally(() => {
                commitAnswerLoading.value = false;
            });
    });
}

function checkQuestionStatus() {
    let result = [true, ''];
    if (questionComponentRef.value && questionComponentRef.value.check) {
        result = questionComponentRef.value.check();
    }
    return result;
}

function getTemplateInfo() {
    return templateName.value;
}

watch(
    () => paperStore.currentQuestionIndex,
    (newIndex, oldIndex) => {
        const { currentQuestion } = paperStore;
        if (!currentQuestion || Object.keys(currentQuestion).length < 1) {
            return;
        }
        const { backgroundInfoEncryptId, encryptId, backgroundInfoType, isHasGroupInfo, questionGroup } = currentQuestion;

        const { id } = isShowSceneTemplate.value;

        const isFirst = oldIndex === undefined; // 是不是第一次触发;
        let data: ISceneTemplate = {
            id: 0,
            type: 0,
            isShow: false,
        };

        // 情景题 背景信息数据控制
        if (backgroundInfoType === 11) {
            data = {
                ...data,
                id: backgroundInfoEncryptId,
                type: backgroundInfoType,
            };
        }

        // 题组信息 数据控制
        if (isHasGroupInfo) {
            data = {
                ...data,
                id: questionGroup,
                type: 'group',
            };
        }

        if (isFirst && data.id) {
            // 第一次打开
            data.isShow = true;
        } else if (newIndex > (oldIndex || 0)) {
            // 正向翻题
            data.isShow = !!(id && id !== data.id);
        } else {
            // 反向回看 查看上一题
            if (isHasGroupInfo && newIndex === 0) {
                // 题组信息回看第一题需要打开展示
                data.isShow = true;
            } else {
                data.isShow = false;
            }
        }

        emit('questionChange', {
            type: data.isShow ? 'end' : '',
            currentQuestionId: encryptId,
        });

        showSceneTemplate({
            ...data,
        });
    },
    {
        immediate: true,
        deep: true,
    },
);

defineExpose({
    checkQuestionStatus,
    submit: submitAnswer,
    getTemplateInfo,
});
</script>

<style lang="less" scoped>
.commit-answer-loading-wrap {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    width: 100vw;
    height: 100vh;
    z-index: 99;

    .commit-answer-loading-content {
        position: relative;
        width: 100%;
        height: 100%;

        .text {
            position: absolute;
            left: 50%;
            top: 50%;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 6px;
            padding: 8px 12px;
            transform: translateX(-50%) translateY(-50%);
            color: #fff;
            opacity: 0;

            &.show {
                animation: example 0.3s 0.5s;
                animation-fill-mode: forwards;
            }
        }
    }
}

@keyframes example {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}
</style>
