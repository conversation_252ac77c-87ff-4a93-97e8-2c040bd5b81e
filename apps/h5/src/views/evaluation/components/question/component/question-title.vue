<template>
    <!-- 判断是否展示背景信息入口 -->
    <div class="question-title-wrap">
        <SceneInfoBar v-if="isShowSceneEntry" />
        <div class="question-subject" v-html="title || questionInfo.questionTitle" />
        <ImgWrap :key="questionInfo.encryptId" :imgList="questionInfo.files" />
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import ImgWrap from './img-wrap.vue';
import SceneInfoBar from './scene-info-bar.vue';

const props = defineProps({
    title: {
        type: String,
        default: () => '',
    },
    questionInfo: {
        type: Object,
        default: () => ({}),
    },
});

const isShowSceneEntry = computed(() => {
    return props.questionInfo?.backgroundInfoType === 11;
});
</script>

<style lang="less" scoped>
.question-title-wrap {
    padding: 0 20px;
    margin-bottom: 24px;
}
.question-subject {
    font-size: 16px;
    font-weight: 500;
    color: var(--van-text-color-heavy-3);
    line-height: 24px;
    margin-bottom: 20px;
    word-break: break-all;
    white-space: pre-wrap;
}
</style>
