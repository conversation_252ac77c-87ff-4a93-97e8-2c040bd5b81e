<template>
    <div class="single-choice-question-wrap">
        <QuestionTitle :questionInfo="questionInfo" />
        <p class="desc">按照意向程度，拖动选项从上到下排序</p>
        <DraggableList v-model="optionList" class="drag-area">
            <template #item="{ item }">
                <OptionItem :isDrap="true" :isChecked="false" :content="item.optionContent" :imgList="item.files" />
            </template>
        </DraggableList>
    </div>
</template>

<script setup lang="ts">
import { DraggableList } from '@crm/exam-components';
import { computed, ref, watch } from 'vue';
import OptionItem from './component/option-item.vue';
import QuestionTitle from './component/question-title.vue';

const props = defineProps({
    questionInfo: {
        type: Object,
        default: () => ({}),
    },
    answer: {
        type: [String, Array],
        default: () => '',
    },
});

const optionList = ref<any[]>([]);
const answerVal = computed(() => {
    return optionList.value.map((item) => item.encryptId);
});

watch(
    () => props.questionInfo,
    async () => {
        const { questionOptionList } = props.questionInfo;
        const currentAnswer = props.answer;

        let list = [];

        if (currentAnswer && currentAnswer.length) {
            (currentAnswer as any[]).forEach((id) => {
                const data = questionOptionList.find((item: { encryptId: any }) => item.encryptId === id);
                list.push({ ...data });
            });
        } else {
            list = questionOptionList.map((item: { encryptId: any }, index: any) => ({
                ...item,
                id: item?.encryptId || index,
            }));
        }
        optionList.value = list || [];
    },
    {
        deep: true,
        immediate: true,
    },
);

function checkAnswer() {
    const { encryptId, questionType } = props.questionInfo;

    const params = {
        evaPaperSnapshotId: encryptId,
        questionType,
        answerContent: answerVal.value.map((item) => item),
    };

    return [true, '', params];
}

defineExpose({
    check: checkAnswer,
});
</script>

<style lang="less" scoped>
.single-choice-question-wrap {
    .desc {
        padding: 0 20px;
        margin-bottom: 12px;
        color: #858585;
        font-size: 12px;
        font-weight: 400;
    }
}

// 重置拖拽样式

:deep(.drag-area) {
    padding: 0 20px;

    & > div {
        margin-bottom: 12px;
        border-radius: 8px;

        .icon-drap {
            margin-right: 8px;
        }
    }

    .sortable-chosen {
        // 被拖拽元素
        &.ghost {
            opacity: 0;
        }

        // 拖拽元素
        &.sortable-drag {
            border: 1px solid var(--van-blue);
            background-color: #e3f6f6;
            opacity: 1;
        }
    }
}
</style>
