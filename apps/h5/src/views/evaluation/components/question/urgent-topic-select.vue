<template>
    <div class="single-choice-question-wrap">
        <div class="question-subject" v-html="questionTitle" />
        <VanCheckboxGroup>
            <VanCheckbox
                v-for="item of questionOption"
                :key="item.encryptId"
                :class="{ 'van-radio-checked': findCheckedList(item.encryptId) }"
                :name="item.encryptId"
                @click="changeAnswer(item)"
            >
                <div>{{ item.letters }}. {{ item.optionContent }}</div>
                <p class="icon">
                    {{ findCheckedList(item.encryptId) }}
                </p>
            </VanCheckbox>
        </VanCheckboxGroup>
    </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';

const props = defineProps({
    questionInfo: {
        type: Object,
        default: () => ({}),
    },
    answer: {
        type: [String, Array],
        default: () => '',
    },
});

const questionOption = ref<any[]>([]);
const checked = ref<any[]>([]);

const questionTitle = computed(() => {
    const title = props.questionInfo.questionTitle;

    const titStr = title.replace(/___/g, `<span class="answer"></span>`);

    return titStr;
});

function findCheckedList(id: string) {
    let flag = 0;
    const falgIndex = checked.value.findIndex((item) => item?.id === id);
    if (falgIndex > -1) {
        flag = falgIndex + 1;
    }
    return flag;
}

async function changeAnswer(data: any) {
    const { encryptId, letters } = data || {};

    const flagIndex = checked.value.findIndex((item) => item?.id === encryptId);

    if (flagIndex >= 0) {
        checked.value[flagIndex] = { letters: '', id: '' };
        return;
    }

    for (let i = 0; i < checked.value.length; i++) {
        const id = checked.value[i]?.id || '';

        if (id === '') {
            checked.value[i] = {
                letters,
                id: encryptId,
            };

            break;
        }
    }
}

function checkAnswer() {
    const { encryptId, questionType } = props.questionInfo;
    let flag = true;
    let message = '';

    const data = checked.value.find((item) => item.id === '');

    if (data) {
        message = '请选择完所有的题目';
        flag = false;
    }
    const params = {
        evaPaperSnapshotId: encryptId,
        questionType,
        answerContent: checked.value.map((i: any) => i.id),
    };

    return [flag, message, params];
}

watch(
    () => props.questionInfo,
    (data) => {
        const questionList: any[] = [];
        const { questionOptionList, needSelectedCount } = data;

        let needSelectedCountVal = 0;

        // 丰富选项数据 满足展示条件
        if (questionOptionList && questionOptionList.length) {
            questionOptionList.forEach((item: { encryptId: any; optionContent: any }, index: number) => {
                const { encryptId, optionContent } = item;
                const letters = String.fromCharCode(64 + Number.parseInt(String(index + 1), 10));
                questionList.push({
                    encryptId,
                    optionContent,
                    letters,
                });
                needSelectedCountVal += 1;
            });
        }

        questionOption.value = [...questionList];

        // 回显答案
        const checkedList = Array.from({ length: needSelectedCount || needSelectedCountVal });
        const currentAnswerVal = props.answer || [];

        for (let i = 0; i < checkedList.length; i++) {
            const answerId = currentAnswerVal && currentAnswerVal.length ? currentAnswerVal[i] : false;

            let data = { letters: '', id: '' };

            if (answerId) {
                const questionData = questionList.find((item) => item.encryptId === answerId);
                if (questionData) {
                    data = { letters: questionData.letters ?? '', id: answerId as string };
                }
            }

            checkedList[i] = data;
        }

        checked.value = [...checkedList];
    },
    { immediate: true },
);

watch(
    () => checked.value,
    async () => {
        setTimeout(() => {
            const doms = document.querySelectorAll('.answer');

            checked.value.forEach((data, index) => {
                if (data && doms[index]) {
                    doms[index].innerHTML = data.letters || '&nbsp';
                }
            });
        }, 0);
    },
    { deep: true, immediate: true },
);

defineExpose({
    check: checkAnswer,
});
</script>

<style lang="less" scoped>
.single-choice-question-wrap {
    padding: 0 20px;

    .question-subject {
        font-size: 16px;
        font-weight: 500;
        color: var(--van-text-color-heavy-3);
        line-height: 24px;
        margin-bottom: 24px;
        word-break: break-all;
        white-space: pre-wrap;

        :deep(.answer) {
            margin: 0 4px;
            display: inline-block;
            width: 40px;
            height: 19px;
            line-height: 20px;
            border-bottom: 1px solid var(--van-text-color-heavy-3);
            text-align: center;
            font-size: 16px;
            font-weight: 600;
            color: var(--van-primary-color);
        }
    }
    :deep(.van-checkbox-group) {
        .van-checkbox {
            padding: 14px 16px;
            box-shadow: 0 6px 16px 0 rgba(36, 45, 46, 0.1);
            border-radius: 8px;
            border: 1px solid transparent;
            & + .van-checkbox {
                margin-top: 12px;
            }
            .van-checkbox__icon {
                display: none;
            }
            .van-checkbox__label {
                margin-left: 0;
                font-size: 15px;
                color: var(--van-text-color-heavy-2);
                line-height: 22px;
                word-break: break-all;
            }

            &.van-radio-checked {
                border-color: var(--van-primary-color);
                position: relative;

                .van-radio__label {
                    color: var(--text-primary-color);
                }

                .icon {
                    display: block;
                    position: absolute;
                    right: -8px;
                    top: -8px;
                    width: 30px;
                    height: 30px;
                    border-radius: 15px;
                    background: var(--text-primary-color);
                    color: #fff;
                    padding-left: 9px;
                    line-height: 36px;
                    font-size: 14px;
                }
            }

            .icon {
                display: none;
            }
        }
    }
}
</style>
