<template>
    <div class="question-group-fixed">
        <div class="question-group-wrap">
            <div class="info-wrap">
                <div class="info-box">
                    <p class="info-text">
                        <span />
                        <span v-if="paperStore.config.timedRuleType === 2" class="time-box-wrap"
                            >{{ answeringTimeCountdown.hours }}:{{ answeringTimeCountdown.minutes }}:{{ answeringTimeCountdown.seconds }}</span
                        >
                        <span v-else class="time-box-wrap">{{ answeringTime.hours }}:{{ answeringTime.minutes }}:{{ answeringTime.seconds }}</span>
                    </p>
                    <div class="box">
                        <div class="box-info">
                            <p class="tit">
                                <VanImage :src="stars" :width="20" />
                                <span>Part {{ paperStore.currentQuestion?.questionGroup }}</span>
                                <VanImage style="transform: scaleX(-1)" :src="stars" :width="20" />
                            </p>
                            <div class="text-wrap">
                                {{ groupInfo }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="btn-wrap">
                <VanButton type="primary" size="large" @click="onClickComplete"> 我知道了 </VanButton>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import stars from '@/assets/svg-sprite/star.svg';
import { usePaperStore } from '@/stores/paper';
import { computed } from 'vue';

const emit = defineEmits(['onComplete']);

const paperStore = usePaperStore();

// 倒计时
const answeringTimeCountdown = computed(() => paperStore.answeringTimeCountdown.remainingTime);
// 正计时
const answeringTime = computed(() => paperStore.answeringTime.elapsedTime);

const groupInfo = computed(() => {
    const { currentQuestion, questionGroupInfo } = paperStore;
    const { questionGroup } = currentQuestion || {};

    return questionGroupInfo ? questionGroupInfo[questionGroup] : {};
});

function onClickComplete() {
    emit('onComplete');
}
</script>

<style lang="less" scoped>
.question-group-fixed {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 100 !important;
    background: #fff;
    overflow: hidden;
}

.question-group-wrap {
    position: relative;
    width: 100%;
    height: 100%;
    background: url('@/assets/svg-sprite/radial-green.svg') no-repeat;
    display: flex;
    flex-direction: column;

    &::after {
        content: '';
        position: absolute;
        right: -3%;
        bottom: 0;
        width: 401px;
        height: 401px;
        background: #fff url('@/assets/svg-sprite/radial-blue.svg') no-repeat;
        background-position-x: 18px;
        z-index: -1;
    }
    &::before {
        position: absolute;
        right: -2%;
        // bottom: 0;
        content: '';
        width: 401px;
        height: 401px;
        background: url('@/assets/svg-sprite/radial-blues.svg') no-repeat;
        background-position-x: 18px;
        z-index: -1;
    }

    .info-wrap {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 0 20px;

        .info-box {
            .info-text {
                margin-bottom: 8px;
                display: flex;
                justify-content: space-between;

                span {
                    color: #757575;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: normal;
                }
            }

            .box {
                position: relative;
                top: 0;
                left: 0;
                height: 400px;
                border-radius: 12px;
                overflow: hidden;

                &::after {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(-35deg, transparent 62%, #72dbda);
                }

                &::before {
                    content: '';
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    width: 100%;
                    height: 93%;
                    background: linear-gradient(-40deg, #a4bcfe, transparent 42%);
                }

                .box-info {
                    padding-top: 40px;
                    position: absolute;
                    left: 1px;
                    top: 1px;
                    width: calc(100% - 2px);
                    height: calc(100% - 2px);
                    background: #fff;
                    z-index: 100;
                    border-radius: 11px;
                    display: flex;
                    flex-direction: column;

                    .tit {
                        margin-bottom: 32px;
                        display: flex;
                        justify-content: center;

                        & > span {
                            margin: 0 10px 0 12px;
                            font-size: 32px;
                            font-style: normal;
                            font-weight: 600;
                            line-height: normal;
                            text-align: center;
                            color: #000;
                        }
                    }

                    .text-wrap {
                        flex: 1;
                        padding: 0 40px 20px;
                        overflow-y: scroll;
                        color: #141414;
                        font-size: 16px;
                        font-style: normal;
                        font-weight: 500;
                        line-height: 24px;
                        text-align: justify;
                        white-space: pre-wrap;
                    }
                }
            }
        }
    }

    .btn-wrap {
        padding: 0 20px 44px;
    }
}
</style>
