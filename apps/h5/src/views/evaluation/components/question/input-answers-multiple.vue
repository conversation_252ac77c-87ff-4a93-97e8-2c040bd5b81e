<template>
    <QuestionTitle :questionInfo="questionInfo" />
    <VanCheckboxGroup>
        <VanCheckbox v-for="(item, index) of questionInfo.questionOptionList" :key="item.encryptId" :name="item.encryptId" @click="changeAnswer(item)">
            <OptionItem :imgList="item.files" :content="`${index + 1}.${item.optionContent}`" :isChecked="!!findCheckedList(item.encryptId)" />
        </VanCheckbox>
    </VanCheckboxGroup>
    <template v-for="item of checked" :key="item.optionId">
        <div class="input-wrap">
            <p class="input-title">
                {{ item.shortQuestion }}
            </p>
            <VanField v-model="item.shortAnswer" class="input-content" :placeholder="placeholder" autosize :showWordLimit="true" type="textarea" maxlength="300" />
        </div>
    </template>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import OptionItem from './component/option-item.vue';
import QuestionTitle from './component/question-title.vue';

const props = defineProps({
    questionInfo: {
        type: Object,
        default: () => ({}),
    },
    answer: {
        type: [String],
        default: () => '',
    },
});

const placeholder = ref('');
const checked = ref<any[]>([]);

watch(
    () => props.questionInfo,
    async (val: any) => {
        const { required } = val;
        let checkedVal: any = [];

        if (props.answer) {
            checkedVal = JSON.parse(props.answer);
        }

        placeholder.value = required ? '请输入' : '选填';
        checked.value = checkedVal;
    },
    { immediate: true },
);

function findCheckedList(id: string) {
    let flag = 0;
    const list = [...(checked.value || [])];
    const falgIndex = list.findIndex((item: { optionId: string }) => item?.optionId === id);
    if (falgIndex > -1) {
        flag = falgIndex + 1;
    }
    return flag;
}

async function changeAnswer(data: any) {
    const { encryptId, shortAnswer, shortQuestion } = data || {};

    const flagIndex = checked.value.findIndex((item: any) => item?.optionId === encryptId);

    if (flagIndex > -1) {
        checked.value.splice(flagIndex, 1);
    } else {
        checked.value.push({
            optionId: encryptId,
            shortAnswer,
            shortQuestion,
        });
    }
}

function checkAnswer() {
    const { required, encryptId, questionType } = props.questionInfo;
    let flag = true;
    let message = '';

    const val = [...checked.value];

    if (val.length <= 0) {
        flag = false;
        message = '请选择您的答案';
    } else if (required) {
        const hasEmpty = val.find((item: { shortAnswer: any }) => !item.shortAnswer);
        if (hasEmpty) {
            flag = false;
            message = '请输入您的回答';
        }
    }

    const params = {
        evaPaperSnapshotId: encryptId,
        questionType,
        answerContent: JSON.stringify(val),
    };

    return [flag, message, params];
}

defineExpose({
    check: checkAnswer,
});
</script>

<style lang="less" scoped>
.input-wrap {
    margin-top: 24px;
    padding: 0 20px;

    .input-title {
        margin-bottom: 10px;
        color: #292929;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
    }

    .input-content {
        padding: 12px;
        background: #f5f5f6;
        border-radius: 8px;
        :deep(.van-field__control) {
            color: #141414;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            min-height: 106px;
        }

        :deep(.van-field__word-num) {
            color: #858585;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 18px;
        }
    }
}

:deep(.van-checkbox) {
    padding-bottom: 12px;

    &:last-child {
        padding-bottom: 18px;
    }

    .van-checkbox__icon {
        display: none;
    }

    .van-checkbox__label {
        margin: 0;
        padding: 0 20px;
        width: 100%;
    }
}
</style>
