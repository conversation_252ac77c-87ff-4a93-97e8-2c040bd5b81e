<template>
    <VanPopup :show="paperStore.sceneDetailPopupShow" round position="bottom" class="popup" safeAreaInsetBottom overlayClass="evaluation-list-popup-overlay">
        <div class="popup-container">
            <SceneQuestion :isPopup="true" />
        </div>
        <div class="popup-footer">
            <VanDivider style="margin: 0" />
            <p class="text-btn" @click="handleClosePopup">阅读完成，继续答题</p>
        </div>
    </VanPopup>
</template>

<script setup lang="ts">
import SceneQuestion from '../../../components/question/scene-question.vue';
import { usePaperStore } from '@/stores/paper';

const paperStore = usePaperStore();

function handleClosePopup() {
    paperStore.sceneDetailPopupShow = false;
}

defineExpose({
    open,
    close: handleClosePopup,
});
</script>

<style lang="less" scoped>
.popup {
    background: #fff;
    left: 0;
    right: 0;
    max-width: 750px;
    margin: auto;

    .popup-container {
        padding-top: 20px;
    }

    .popup-footer {
        .text-btn {
            padding: 16px;
            text-align: center;
            font-size: 16px;
            font-weight: 500;
            color: #0d9ea3;
            line-height: 24px;
        }
    }
}

.evaluation-list-popup-overlay {
    left: 0;
    right: 0;
    max-width: 750px;
    margin: auto;
}
</style>
