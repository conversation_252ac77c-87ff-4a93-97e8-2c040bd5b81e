<template>
    <div class="btn-wrap">
        <VanButton v-if="showPrevBtn" class="normal-btn" type="default" round @click="onClickPrev"> 上一题 </VanButton>
        <div v-else />
        <VanButton v-if="showNextBtn" class="normal-btn" type="default" round @click="onClickNext"> 下一题 </VanButton>
        <VanButton v-if="showSubmitBtn" :disabled="!submitDisabled" class="submit-btn" type="primary" round @click="onClickSubmit"> 提交 </VanButton>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { usePaperStore } from '@/stores/paper';
import { debounce } from 'lodash-es';
import { PAPER_REVIEW_TYPE, paperQuestionNextMethod } from '../../../constant';

const props = defineProps({
    unansweredList: {
        type: Array,
        default: () => [],
    },
    nextDisabled: {
        type: Boolean,
        default: () => false,
    },
    isReview: {
        type: Boolean,
        default: false,
    },
});
const emit = defineEmits(['next', 'submit', 'prev']);
const paperStore = usePaperStore();

const showPrevBtn = computed(() => {
    let flag = false;
    const { config, currentQuestionIndex, allQuestionList } = paperStore;
    const { reviewRuleType } = config;
    const notFirst = currentQuestionIndex > 0; // 不是第一题（除第一题外的每道题都显示）
    const isLast = currentQuestionIndex === allQuestionList.length - 1;
    if (props.isReview) {
        return allQuestionList.length > 1 && notFirst;
    }

    if (reviewRuleType === PAPER_REVIEW_TYPE.不可回看) {
        flag = false;
    } else if (reviewRuleType === PAPER_REVIEW_TYPE.不限制回看) {
        flag = notFirst;
    } else if (reviewRuleType === PAPER_REVIEW_TYPE.可回看一题) {
        const notAanswerIndex = allQuestionList.findIndex((item) => item.status === 0);
        flag = (notFirst && notAanswerIndex === currentQuestionIndex) || isLast;
    }

    return flag;
});

const showSubmitBtn = computed(() => {
    const { config, currentQuestionIndex, allQuestionList } = paperStore;
    const { next_method } = config;

    const isCurrentLast = currentQuestionIndex === allQuestionList.length - 1;

    // 如果配置了手动提交
    if (next_method === paperQuestionNextMethod.manual && isCurrentLast) {
        return true;
    }

    return isCurrentLast;
});

const showNextBtn = computed(() => {
    const { currentQuestionIndex, currentQuestion, allQuestionList, config } = paperStore;
    const { next_method, reviewRuleType } = config || {};
    const isCurrentLast = currentQuestionIndex < allQuestionList.length - 1;

    if (showSubmitBtn.value || !currentQuestion) {
        return false;
    }

    // 手动提交模式下
    if (next_method === paperQuestionNextMethod.manual) {
        return isCurrentLast;
    }

    if (next_method === paperQuestionNextMethod.auto && currentQuestion?.status === 0) {
        return false;
    }

    if ([PAPER_REVIEW_TYPE.不限制回看, PAPER_REVIEW_TYPE.可回看一题].includes(reviewRuleType)) {
        return currentQuestion?.status === 1 && isCurrentLast;
    }

    return isCurrentLast;
});

const submitDisabled = computed(() => {
    const { config } = paperStore;
    const { next_method } = config;

    if (next_method === paperQuestionNextMethod.manual) {
        return props.nextDisabled;
    }

    return props.unansweredList.length <= 0;
});

const onClickNext = debounce(
    () => {
        const { currentQuestion } = paperStore;
        if (showSubmitBtn.value || !currentQuestion) {
            return;
        }
        emit('next');
    },
    300,
    { leading: true },
);

const onClickSubmit = debounce(
    () => {
        emit('submit');
    },
    300,
    { leading: true },
);

const onClickPrev = debounce(
    () => {
        emit('prev');
    },
    300,
    { leading: true },
);
</script>

<style lang="less" scoped>
.btn-wrap {
    padding: 36px 20px 28px;
    display: flex;
    justify-content: space-between;

    --van-button-default-color: var(--van-text-color-heavy-2);

    .normal-btn {
        width: 100px;
        flex-shrink: 0;
        border-color: #e0e0e0;
    }
    .submit-btn {
        flex-grow: 1;
        flex-shrink: 0;
    }

    .normal-btn,
    .submit-btn {
        height: 44px;
        font-size: 16px;
    }

    .van-button + .van-button {
        margin-left: 20px;
    }
}
</style>
