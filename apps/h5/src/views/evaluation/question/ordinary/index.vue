<template>
    <div
        class="hide-scroll-bar question-detail-page"
        :class="{
            'no-scroll': currentQuestionType === QUESTION_TYPE.SCENE,
        }"
    >
        <Progress v-if="templateInfo.name !== 'question-group-info'" />
        <div
            class="question-wrap"
            :style="{
                overflow: currentQuestionType === QUESTION_TYPE.SCENE ? 'scroll' : '',
            }"
        >
            <Question ref="questionRef" @answerSelect="onAnswerSelect" @questionChange="questionChange" />
        </div>
        <BtnControl
            v-if="!templateInfo.flag"
            :unansweredList="unansweredList"
            :nextDisabled="!hasAnswer"
            @next="controlNext({ isAutoSelect: false, commitType: 0 })"
            @submit="controlNext({ commitType: 1 })"
            @prev="onPrev"
        />
    </div>
    <SceneDetailPopup />
</template>

<script setup lang="ts">
import { QUESTION_TYPE } from '@/constant/evaluation';
import { useExamStore } from '@/stores/exam';
import { usePaperStore } from '@/stores/paper';
import { showToast } from 'vant';
import { computed, ref, watch } from 'vue';
import Question from '../../components/question/index.vue';
import { paperQuestionNextMethod } from '../../constant';
import Progress from '../components/progress.vue';
import BtnControl from './component/btn-control.vue';
import SceneDetailPopup from './component/scene-detail-popup.vue';

const props = defineProps({
    paperTimeEnd: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(['submitPaper', 'onQuestionChange']);
const paperStore = usePaperStore();
const examStore = useExamStore();
// 试题切换回调
function questionChange(data: any) {
    emit('onQuestionChange', data);
}

// 当前题-类型
const currentQuestionType = computed(() => {
    return paperStore.currentQuestion?.questionType || 1;
});

// 所有未答题列表
const unansweredList = computed(() => {
    return paperStore.allQuestionList.filter((item) => {
        return item.status === 0;
    });
});
// 题型组件ref
const questionRef = ref<any>(null);

const hasAnswer = computed(() => {
    let flag = true;
    if (questionRef.value && questionRef.value.checkQuestionStatus) {
        const [result] = questionRef.value?.checkQuestionStatus();
        if (result) {
            flag = false;
        }
    }

    return flag;
});

// 当前试题模板信息 特殊展示类模板隐藏部分模块
const templateInfo = computed(() => {
    let flag = false;
    const name = questionRef.value?.getTemplateInfo()?.__name ?? '';
    if (name && ['scene-question', 'question-group-info'].includes(name)) {
        flag = true;
    }
    return {
        flag,
        name,
    };
});

// 试题选择答案以后回调
function onAnswerSelect() {
    controlNext({ isAutoSelect: true, commitType: 0 });
}

// 上一题
function onPrev() {
    questionRef.value
        .submit({
            isShowToast: false,
        })
        .then((res: any) => {
            const prevIndex = paperStore.currentQuestionIndex - 1;
            paperStore.jumpToQuestion(prevIndex < 0 ? 0 : prevIndex);
        });
}

function checkQuestionStatus() {
    let flag = false;
    const [result, message] = questionRef.value.checkQuestionStatus();
    if (!result && message) {
        showToast(message);
        flag = false;
    } else {
        flag = true;
    }
    return flag;
}

function controlNext({ isAutoSelect = false, commitType = 1 | 2 | 3 | 0 }) {
    const { config, currentQuestionIndex, allQuestionList } = paperStore;
    const isCurrentLast = currentQuestionIndex === allQuestionList.length - 1;
    const nextQuestionIndex = currentQuestionIndex + 1;
    const { next_method } = config;

    // 如果配置项是 自动提交 并且是 手动点击下一题
    if (next_method === paperQuestionNextMethod.auto && !isAutoSelect && !commitType) {
        if (checkQuestionStatus()) {
            paperStore.jumpToQuestion(nextQuestionIndex);
        }
        return;
    }

    questionRef.value
        ?.submit({
            isShowToast: commitType !== 2,
        })
        .then((res: any) => {
            if (isAutoSelect && isCurrentLast) {
                return;
            }

            if (commitType === 0 && res.params) {
                if (res.params.evaPaperSnapshotId) {
                    const index = allQuestionList.findIndex((item) => item.encryptId === res.params.evaPaperSnapshotId);
                    paperStore.jumpToQuestion(index + 1);
                } else {
                    paperStore.jumpToQuestion(nextQuestionIndex);
                }
            }
        })
        .finally(() => {
            if (commitType > 0) {
                postSubmit(commitType as 0 | 1 | 2 | 3);
            }
        });
}

// 提交试卷
function postSubmit(commitType: 1 | 2 | 3 | 0) {
    emit('submitPaper', { commitType });
}

// 场次倒计时结束
watch(
    () => props.paperTimeEnd,
    (val) => {
        if (val) {
            controlNext({
                commitType: 2,
            });
        }
    },
    {
        immediate: true,
    },
);
</script>

<style lang="less" scoped>
.question-detail-page {
    // height: 100%;
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    background: #fff;

    &.no-scroll {
        height: 100%;
        max-height: 100%;
        overflow: hidden;
    }

    .question-scroll-wrap {
        flex: 1;
        overflow: scroll;
    }

    .question-wrap {
        // padding: 0 20px;
        flex: 1;
        position: relative;
        z-index: 1;
        display: flex;
        flex-direction: column;
    }
}
</style>

<style>
.paper-close-dialog-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    line-height: 23px;
    .img-bg {
        width: 85px;
        margin-bottom: 15px;
    }
}
</style>
