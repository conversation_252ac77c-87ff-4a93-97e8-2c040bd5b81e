<template>
    <div class="exam-wrap" :style="{ height: windowHeight, overflow: 'hidden' }">
        <div class="exam-head">
            <Progress @forceSubmit="onPaperTimeEnd" />
        </div>
        <div class="exam-content">
            <BackgroundInfoQuestion v-if="questionBackgroundInfo" :questionBackgroundInfo="questionBackgroundInfo" :questionInfo="paperStore.currentQuestion">
                <Question ref="questionRef" />
            </BackgroundInfoQuestion>
            <Question v-else ref="questionRef" />
        </div>
        <div class="exam-footer">
            <ExamBtnControl
                :unansweredList="unansweredList"
                :nextDisabled="hasAnswer"
                @jump="jumpNextUnansweredList"
                @next="onNext({ commitType: 0 })"
                @submit="onNext({ commitType: 1 })"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { usePaperStore } from '@/stores/paper';
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
import Question from '../../components/question/index.vue';
import Progress from '../components/progress.vue';
import BackgroundInfoQuestion from './component/background-info-question.vue';
import ExamBtnControl from './component/exam-btn-control.vue';

const props = defineProps({
    paperTimeEnd: {
        type: Boolean,
        default: false,
    },
});
const emit = defineEmits(['submitPaper', 'onQuestionChange']);
const paperStore = usePaperStore();
const windowHeight = ref('100%');
const questionRef = ref();

const questionBackgroundInfo = computed(() => {
    let dataInfo = null;
    const { currentQuestion, backgroundInfoList } = paperStore;
    const { backgroundInfoEncryptId, backgroundInfoType } = currentQuestion || {};
    if (backgroundInfoEncryptId && backgroundInfoType === 20) {
        const data = backgroundInfoList.find((item) => item.encryptId === backgroundInfoEncryptId);
        if (data) {
            dataInfo = data;
        }
    } else {
        dataInfo = null;
    }
    return dataInfo;
});

// 所有未答题列表
const unansweredList = computed(() => {
    return paperStore.allQuestionList.filter((item) => {
        return item.status === 0;
    });
});

const hasAnswer = computed(() => {
    let flag = true;
    if (questionRef.value && questionRef.value.checkQuestionStatus) {
        const [, , params] = questionRef.value?.checkQuestionStatus();
        if (params && params.answerContent && params.answerContent.length) {
            flag = false;
        }
    }

    return flag;
});

function onNext({ commitType = 0 }: { commitType: 1 | 2 | 3 | 0 }) {
    const isShowToast = commitType !== 1;
    questionRef.value
        .submit({ isShowToast })
        .then((res: any) => {
            if (res && res.params) {
                const { evaPaperSnapshotId } = res.params || {};
                const data = evaPaperSnapshotId ? { encryptId: evaPaperSnapshotId } : null;
                jumpNextUnansweredList(data);
            }
        })
        .finally(() => {
            if (commitType) {
                emit('submitPaper', { commitType });
            }
        });
}

function jumpNextUnansweredList(data: any) {
    const { currentQuestion, allQuestionList, jumpToQuestion } = paperStore;
    const unanswered = [...unansweredList.value]; // 未答列表
    const currentData = data || currentQuestion; // 是否是选题跳转

    let nextQuestionInfo = unanswered[0];
    const currentDataIndex = allQuestionList.findIndex((item) => item.encryptId === currentData.encryptId);
    const sliceIndex = data ? currentDataIndex : currentDataIndex + 1;
    const afterQuestionList = allQuestionList.slice(sliceIndex);
    const nextUnanswered = afterQuestionList.find((item) => item.status === 0);

    if (nextUnanswered) {
        nextQuestionInfo = nextUnanswered;
    }

    if (nextQuestionInfo) {
        const nextQuestionIndex = allQuestionList.findIndex((item) => item.encryptId === nextQuestionInfo.encryptId);
        jumpToQuestion(nextQuestionIndex);
    }
}

function getWindowInnerHeight() {
    const windowInnerHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
    windowHeight.value = `${windowInnerHeight}px`;
}

window.addEventListener('resize', getWindowInnerHeight);

onMounted(() => {
    getWindowInnerHeight();
});

onUnmounted(() => {
    document.removeEventListener('resize', getWindowInnerHeight);
});

watch(
    () => paperStore.currentQuestion,
    (newVal: any) => {
        emit('onQuestionChange', {
            currentQuestionId: newVal.encryptId,
        });
    },
    {
        deep: true,
        immediate: true,
    },
);

function onPaperTimeEnd() {
    onNext({
        commitType: 2,
    });
}

// 场次倒计时结束
watch(
    () => props.paperTimeEnd,
    (val) => {
        if (val) {
            onPaperTimeEnd();
        }
    },
    {
        immediate: true,
    },
);
</script>

<style lang="less" scoped>
.exam-wrap {
    height: 100%;
    display: flex;
    flex-direction: column;
    transition: all 0.15s;

    .exam-head {
        min-height: 50px;
    }

    .exam-content {
        flex: 1;
        overflow: auto;
    }
}
</style>
