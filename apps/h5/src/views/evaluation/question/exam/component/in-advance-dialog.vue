<template>
    <VanDialog
        :show="show"
        confirmButtonText="取消"
        cancelButtonText="确定"
        :showCancelButton="true"
        cancelButtonColor="#FF4A55"
        confirmButtonColor="#858585"
        @confirm="onRefuse"
        @cancel="onAgree"
    >
        <div class="dialog-container-wrap">
            <div class="title-m">提前交卷</div>
            <div class="desc-s">尚未完成题目作答，是否提前交试卷？</div>
        </div>
    </VanDialog>
</template>

<script setup lang="ts">
import { debounce } from 'lodash-es';

const show = defineModel('visible', {
    default: false,
});
const emit = defineEmits(['submit']);

// 弹窗组件 确认 = 取消逻辑
function onRefuse() {
    show.value = false;
}

// 弹窗组件 取消 = 确认逻辑
const onAgree = debounce(
    () => {
        show.value = false;
        emit('submit');
    },
    300,
    { leading: true },
);
</script>

<style lang="less" scoped>
.dialog-container-wrap {
    padding: 24px 20px;

    .title-m {
        margin-bottom: 12px;
    }
}
</style>
