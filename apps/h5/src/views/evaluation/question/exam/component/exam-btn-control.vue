<template>
    <div class="btn-wrap">
        <VanPopover v-model:show="showPopover" placement="top-start">
            <div style="width: 114px">
                <div class="more-popover-option-content">
                    <VanButton class="option-item" type="default" @click="unansweredQuestionsPopupVisible = true">
                        <div class="option-item-text">
                            <span>未答题目</span><span class="count">{{ unansweredList.length }}</span>
                        </div>
                    </VanButton>
                    <VanButton class="option-item" type="default" @click="onClickSubmit()">
                        <div class="option-item-text">
                            <span style="color: #ff4a55">提前交卷</span>
                        </div>
                    </VanButton>
                </div>
            </div>
            <template #reference>
                <VanButton class="popover-more-btn" type="default">
                    <p class="icon" />
                    <p class="text">
                        更多
                        <span v-if="showUnansweredQuestionsTips && unansweredList.length" class="tips-icon" />
                    </p>
                </VanButton>
            </template>
        </VanPopover>
        <VanButton
            v-if="isNoUnanswered"
            class="jump-btn"
            type="default"
            round
            @click="
                () => {
                    onClickJump(null);
                }
            "
        >
            跳过
        </VanButton>
        <VanButton v-if="isNoUnanswered" class="submit-btn" type="primary" round :disabled="nextDisabled" @click="onClickNext()"> 提交并下一题 </VanButton>
        <VanButton v-else class="submit-btn" type="primary" round @click="onClickSubmit()"> 提交 </VanButton>
    </div>
    <UnansweredQuestionsPopup
        v-model:visible="unansweredQuestionsPopupVisible"
        :unansweredList="unansweredList"
        :currentQuestion="paperStore.currentQuestion"
        @jump="onClickJump"
    />
    <InAdvanceDialog v-model:visible="inAdvanceSubmitDialog" @submit="inAdvanceSubmit()" />
</template>

<script setup lang="ts">
import { usePaperStore } from '@/stores/paper';
import { debounce } from 'lodash-es';
import { computed, ref } from 'vue';
import InAdvanceDialog from './in-advance-dialog.vue';
import UnansweredQuestionsPopup from './unanswered-questions-popup.vue';

const props = defineProps({
    unansweredList: {
        type: Array,
        default: () => [],
    },
    nextDisabled: {
        type: Boolean,
        default: () => false,
    },
});
const emit = defineEmits(['next', 'submit', 'jump']);
const paperStore = usePaperStore();
const showPopover = ref(false);
const showUnansweredQuestionsTips = ref(true);
const unansweredQuestionsPopupVisible = ref(false);
const inAdvanceSubmitDialog = ref(false);
const isNoUnanswered = computed(() => !(props.unansweredList.length <= 1));
function onClickNext() {
    emit('next');
}
const onClickSubmit = debounce(
    () => {
        // 如果有未答题，调用提前交卷弹窗
        if (isNoUnanswered.value) {
            inAdvanceSubmitDialog.value = true;
            showPopover.value = false;
            return;
        }
        emit('submit');
    },
    300,
    { leading: true },
);

const onClickJump = debounce(
    (data: any) => {
        showUnansweredQuestionsTips.value = false;

        emit('jump', data);
    },
    300,
    { leading: true },
);

function inAdvanceSubmit() {
    emit('submit');
}
</script>

<style lang="less" scoped>
.btn-wrap {
    padding: 10px 20px;
    display: flex;
    background: #fff;

    --van-button-default-color: var(--van-text-color-heavy-2);

    .popover-more-btn {
        margin-right: 28px;
        border: none;
        width: 30px;
        padding: 0;
        color: #858585;

        .icon {
            margin: 0 auto;
            width: 16px;
            height: 16px;
            background: url('https://img.bosszhipin.com/static/file/2024/q8engvdx471730362294158.png.webp') no-repeat center;
            background-size: 100% 100%;
        }

        .text {
            position: relative;
            margin-top: 3px;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            text-align: justify;

            .tips-icon {
                position: absolute;
                left: 90%;
                width: 6px;
                height: 6px;
                border-radius: 6px;
                background-color: #ff4a55;
            }
        }
    }

    .jump-btn {
        margin-right: 12px;
        width: 100px;
    }

    .submit-btn {
        flex: 1;
    }
}
</style>

<!-- eslint-disable vue/enforce-style-attribute -->
<style lang="less">
.more-popover-option-content {
    .option-item {
        border-radius: 0 !important;
        padding: 0 !important;
        height: 36px;
        width: 100%;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 36px;
        border: none;
        text-align: left;

        .van-button__content {
            justify-content: initial;
        }

        .option-item-text {
            width: 114px;
            padding: 0 16px !important;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .count {
                padding: 0 2px;
                min-width: 16px;
                min-height: 16px;
                background: #ff4a55;
                border-radius: 20px;
                color: #fff;
                font-size: 10px;
                font-style: normal;
                font-weight: 400;
                line-height: 16px;
                text-align: center;
            }
        }
    }
}
</style>
