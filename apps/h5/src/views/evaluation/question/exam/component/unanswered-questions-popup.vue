<template>
    <VanPopup v-model:show="visible" position="bottom" safeAreaInsetBottom class="unanswered-questions-popup" round overlayClass="evaluation-list-popup-overlay">
        <VanImage class="close-icon" :src="close" @click="handleClosePopup" />
        <div class="content">
            <div class="tit-wrap">
                <p class="tit-text">未答题目</p>
                <div class="desc-wrap">
                    <p class="item">
                        <em class="act flag-item icon" />
                        <span>正在作答</span>
                    </p>
                    <p class="item">
                        <em class="flag-item icon" />
                        <span>未作答</span>
                    </p>
                </div>
            </div>
            <div class="serial-number-wrap">
                <div
                    v-for="item in unansweredList"
                    :key="item.serialNumber"
                    class="flag-item item"
                    :class="{
                        act: currentQuestion.serialNumber === item.serialNumber,
                    }"
                    @click="onClickJump(item)"
                >
                    <span>{{ item.serialNumber }}</span>
                </div>
            </div>
        </div>
    </VanPopup>
</template>

<script setup lang="ts">
import close from '@/assets/svg-sprite/close.svg';
import { showToast } from 'vant';
import { nextTick } from 'vue';

const visible = defineModel<boolean>('visible', {
    default: false,
});

const props = withDefaults(defineProps<Props>(), {
    unansweredList: () => [],
    currentQuestion: () => ({}),
});

const emit = defineEmits(['jump']);

interface Props {
    unansweredList?: any[];
    currentQuestion?: any;
}

function handleClosePopup() {
    visible.value = false;
}

function onClickJump(data: any) {
    const { serialNumber } = props.currentQuestion || {};
    if (serialNumber && serialNumber !== data.serialNumber) {
        emit('jump', data);
        nextTick(() => {
            showToast('切换成功');
        });
    }
}
</script>

<style lang="less" scoped>
.evaluation-list-popup-overlay {
    left: 0;
    right: 0;
    max-width: 750px;
    margin: auto;
}
.unanswered-questions-popup {
    left: 0;
    right: 0;
    max-width: 750px;
    margin: auto;
    border-radius: 16px 16px 0 0;
    overflow: hidden;
    background-color: #fff;
    // height: 30%;

    .close-icon {
        position: absolute;
        right: 20px;
        top: 20px;
    }

    .content {
        height: 220px;
        display: flex;
        flex-direction: column;

        .tit-wrap {
            padding: 20px;
            display: flex;

            .tit-text {
                color: #292929;
                font-size: 18px;
                font-style: normal;
                font-weight: 500;
                line-height: 26px;
            }
            .desc-wrap {
                margin-left: 28px;
                display: flex;

                .item {
                    display: flex;
                    align-items: center;
                    margin-right: 10px;
                    color: #5e5e5e;
                    font-size: 13px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: normal;

                    & + .item {
                        margin-left: 12px;
                    }

                    .icon {
                        margin-right: 6px;
                        width: 10px;
                        height: 10px;
                        border-radius: 2px;
                    }
                }
            }
        }

        .serial-number-wrap {
            padding-right: 11px;
            padding-bottom: 20px;
            padding-left: 20px;
            flex: 1;
            overflow: auto;

            &::before,
            &::after {
                clear: both;
            }

            .item {
                float: left;
                margin-top: 9px;
                margin-right: 9px;
                width: 40px;
                height: 40px;
                line-height: 40px;
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                text-align: center;
                border-radius: 8px;
            }
        }
    }

    .flag-item {
        background: #ebedf2;
        border: 1px solid #d3d8e6;
        color: #292929;
        transition: all 0.2s;

        &:active {
            background: rgba(0, 166, 167, 0.1);
            border: 1px solid #00a6a7;
            color: #00a6a7;
        }

        &.act {
            background: rgba(0, 166, 167, 0.1);
            border: 1px solid #00a6a7;
            color: #00a6a7;
        }
    }
}
</style>
