<template>
    <div v-if="loading" class="base-paper-content loading-wrap">
        <VanLoading color="#15b3b3" />
    </div>
    <div v-else class="base-paper-content">
        <UserWatermark />
        <PaperTime />
        <Ordinary v-if="examInfo.answerListType === 1" :paperTimeEnd="isPaperTimeEnd" @submitPaper="onSubmitPaper" @onQuestionChange="questionShowTimeEvent" />
        <Exam v-if="examInfo.answerListType === 4" :paperTimeEnd="isPaperTimeEnd" @submitPaper="onSubmitPaper" @onQuestionChange="questionShowTimeEvent" />
    </div>
</template>

<script lang="ts" setup>
import { useExamStore } from '@/stores/exam';
import { usePaperStore } from '@/stores/paper';
import { jumpExamList } from '@/utils/jump-url';
import { ExamStatusEnum } from '@crm/exam-types';
import { showDialog } from 'vant';
import PaperTime from './components/paper-time.vue';
import UserWatermark from './components/user-watermark.vue';
import Exam from './exam/index.vue';
import Ordinary from './ordinary/index.vue';
import useSwitchScreen from '@/hooks/useSwitchScreen';
import postLog from '@/utils/post-log';
import { useEndTimeStore } from '@/stores/exam-time';

const route = useRoute();
const router = useRouter();
const paperStore = usePaperStore();
const examStore = useExamStore();
const endTimeStore = useEndTimeStore();
const lastQuestionId = ref('');
const loading = ref(true);
const isPaperTimeEnd = ref(false);
const examInfo = computed(() => examStore.examBaseInfo.examInfo);

useSwitchScreen({
    seqId: examStore.seqId,
    examId: examStore.examId,
});

// 试卷提交
async function onSubmitPaper({ commitType }: { commitType: 1 | 2 | 3 }) {
    questionShowTimeEvent({ type: 'end' });
    try {
        const submitTypeMap = {
            1: 'click_paper_submit',
            2: 'paper_auto_submit',
            3: 'paper_auto_submit',
        };

        const { code, data, message } = await Invoke.paper.postEvaluationPaperCommit({
            encryptExamId: examStore.examId, // 当前小场次id
            remarks: submitTypeMap[commitType],
            commitType,
        });

        if (code === 0) {
            router.replace({
                path: '/evaluation/status',
                query: {
                    ...route.query,
                    status: ExamStatusEnum.已交卷,
                    text: `成功交卷，剩余${data.pendingCount}场待完成`,
                },
            });
        }
    } catch (error) {}
}

// 试题切换监听 （埋点计算每道题展示的时间）
function questionShowTimeEvent({ type = '', currentQuestionId = '' }) {
    const fromId = lastQuestionId.value || '';
    const toId = type === 'end' ? '' : currentQuestionId;
    if (fromId || toId) {
        postLog('eval_question_change', {
            encExamId: examStore.examId || '',
            p2: '',
            p3: fromId,
            p4: toId,
        });
    }

    window.scrollTo({
        top: 0,
        behavior: 'smooth',
    });

    nextTick(() => {
        lastQuestionId.value = toId;
    });
}

async function getAnswerList() {
    loading.value = true;
    await paperStore.getData({
        encryptExamId: examStore.examId,
    });
    loading.value = false;
}

// 场次超时结束回调
function onFinished() {
    showDialog({
        message: `<div class="paper-close-dialog-content"><img class="img-bg" src='https://img.bosszhipin.com/static/zhipin/kanjian/evaluate/515348786447186735.png' /><span>抱歉，${examStore.examBaseInfo.examInfo.examName}的作答时间已结束</span></div>`,
        allowHtml: true,
        confirmButtonText: '我知道了',
    }).then(() => {
        jumpExamList();
    });
}

// 场次倒计时结束
watch(
    () => [endTimeStore.examEndTime.remainingTime.total, endTimeStore.examEndTimeStatus],
    ([time, status]) => {
        if (time <= 0 && status === 1) {
            onFinished();
            isPaperTimeEnd.value = true;
        }
    },
    {
        immediate: true,
    },
);

onMounted(() => {
    getAnswerList();
});

onUnmounted(() => {
    endTimeStore.clearExamEndTime();
});
</script>

<style lang="less" scoped>
.base-paper-content {
    flex: 1;
    display: flex;
    flex-direction: column;

    &.loading-wrap {
        justify-content: center;
        align-items: center;
    }
}
</style>
