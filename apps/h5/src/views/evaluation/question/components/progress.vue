<template>
    <div v-if="cont" class="progress-wrap">
        <div class="progress-content">
            <VanProgress :percentage="((answered + 1) / cont) * 100" :showPivot="false" />
            <div class="progress-number-box">
                <div class="number-box">
                    <span class="current-number">{{ currentNumber }}</span>
                    <span class="seprator">/</span>
                    <span class="total-number">{{ cont }}</span>
                    <span class="question-type-tag">{{ questionTypeName }}</span>
                </div>

                <template v-if="isStart">
                    <!-- 倒计时 -->
                    <div
                        v-if="isCountdown"
                        class="time-box-wrap"
                        :class="{
                            'time-warn': answeringTimeCountdown.total <= timeWarn,
                            'time-end': answeringTimeCountdown.total < 1000,
                        }"
                    >
                        {{ answeringTimeCountdown.hours }}:{{ answeringTimeCountdown.minutes }}:{{ answeringTimeCountdown.seconds }}
                    </div>
                    <!-- 正计时 -->
                    <div v-else class="time-box-wrap">{{ answeringTime.hours }}:{{ answeringTime.minutes }}:{{ answeringTime.seconds }}</div>
                </template>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { usePaperStore } from '@/stores/paper';
import { questionTypeMap } from '../../constant';
import { TimerStatus } from '@crm/exam-hooks';

const emit = defineEmits(['forceSubmit']);
const paperStore = usePaperStore();
const timeWarn = 30 * 1000;

// 当前试题标记
const currentNumber = computed(() => {
    const serialNumber = paperStore.currentQuestion?.serialNumber || 1;
    return serialNumber < 10 ? `0${serialNumber}` : serialNumber;
});

// 计算获取当前题型名称
const questionTypeName = computed(() => {
    let currentQuestionType = 1;
    const { questionType } = paperStore.currentQuestion || {};
    currentQuestionType = questionType;

    return questionTypeMap[currentQuestionType]?.shortName;
});

// 当前试卷总题数
const cont = computed(() => paperStore.allQuestionList.length);

// 所有未答题列表
const answered = computed(() => {
    const list = paperStore.allQuestionList.filter((item) => {
        return item.status === 0;
    });
    let result = cont.value - list.length;
    return result >= cont.value ? result - 1 : result;
});

// 是不是倒计时模式
const isCountdown = computed(() => paperStore.config.timedRuleType === 2);

// 正计时
const answeringTime = computed(() => paperStore.answeringTime.elapsedTime);

// 倒计时
const answeringTimeCountdown = computed(() => paperStore.answeringTimeCountdown.remainingTime);

// 计时器是否开始工作
const isStart = computed(() => {
    if (isCountdown.value) {
        return paperStore.answeringTime.status !== TimerStatus.idled;
    }

    return paperStore.answeringTimeCountdown.status !== TimerStatus.idled;
});

watch(
    () => [isCountdown.value, isStart.value, answeringTimeCountdown.value.total],
    ([isCountdown, isStart, total]) => {
        if (!total && isStart && isCountdown) {
            emit('forceSubmit');
        }
    },
);
</script>

<style lang="less" scoped>
.progress-wrap {
    position: relative;
    min-height: 80px;
}
.progress-content {
    box-sizing: border-box;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1;
    background: #fff;
    padding: 12px 20px;

    .progress-number-box {
        margin-top: 12px;
        display: flex;
        justify-content: space-between;
        align-items: baseline;
        .number-box {
            .current-number {
                font-size: 26px;
                line-height: 31px;
                color: var(--text-primary-color);
                font-family: kanzhun, sans-serif;
            }
            .seprator {
                margin: 0 4px;
            }
            .seprator,
            .total-number {
                font-size: 16px;
                line-height: 19px;
                color: var(--van-text-color-heavy-2);
                font-family: kanzhun, sans-serif;
                position: relative;
                top: -1px;
            }
            .total-number {
                margin-right: 8px;
            }
            .question-type-tag {
                font-size: 13px;
                color: var(--van-text-color-heavy-1);
                background: var(--van-gray-3);
                line-height: 18px;
                padding: 0 5px;
                border-radius: 4px;
                position: relative;
                top: -1px;
            }
        }
        .time-box-wrap {
            font-size: 14px;
            line-height: 20px;

            &.time-warn {
                color: #f06a39;
            }

            &.time-end {
                color: #e03741;
                animation: colorChange 2s linear forwards infinite;
            }
        }
    }
}

@keyframes colorChange {
    0% {
        opacity: 0.2;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.2;
    }
}
</style>
