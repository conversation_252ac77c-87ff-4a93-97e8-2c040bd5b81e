<template>
    <VanWatermark :width="210" :height="110" rotate="-20">
        <template #content>
            <p style="color: rgba(0, 0, 0, 0.1); font-size: 13px; text-align: center; padding-top: 50px">
                {{ content }}
            </p>
        </template>
    </VanWatermark>
</template>

<script setup lang="ts">
import { useExamStore } from '@/stores/exam';
import { ref } from 'vue';

const examStore = useExamStore();
const { userName, examineeId } = examStore.examBaseInfo;
const content = ref(`${userName}-${examineeId}`);
</script>
