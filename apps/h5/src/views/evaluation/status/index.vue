<template>
    <div class="status-home-page">
        <MonoStatus :status="status" :text="text" @onButtonClick="goListPage" @onTimeout="goListPage" />
    </div>
</template>

<script setup lang="ts">
import { jumpExamList } from '@/utils/jump-url';
import { MonoStatus } from '@crm/exam-components';
import { ExamStatusEnum } from '@crm/exam-types';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

defineOptions({
    name: 'StatusHomePage',
});

const route = useRoute();
const router = useRouter();
const status = ref(Number(route.query?.status || ExamStatusEnum.未开始));
const text = ref(String(route.query?.text || ''));

function goListPage() {
    jumpExamList();
}
</script>

<style lang="less" scoped>
.status-home-page {
    --margin-top: 12px;
    display: flex;
    justify-content: center;
    margin-top: var(--margin-top);
    margin-left: 10px;
    margin-right: 10px;
    padding-top: 145px;
    border-radius: 8px;
    min-height: calc(100vh - 64px - var(--margin-top));
    background-color: #ffffff;
}
</style>
