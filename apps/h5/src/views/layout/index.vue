<template>
    <div id="page-content" ref="pageContent">
        <RouterView />
    </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, ref } from 'vue';
import { RouterView } from 'vue-router';

defineOptions({
    name: 'LayoutCommon',
});
const pageContent = ref();

function resize() {
    if (pageContent.value) {
        pageContent.value.style.minHeight = `${window.innerHeight}px`;
    }
}

onMounted(() => {
    resize();
    window.addEventListener('resize', resize);
});

onUnmounted(() => {
    window.removeEventListener('resize', resize);
});
</script>

<style lang="less">
#page-content {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}
</style>
