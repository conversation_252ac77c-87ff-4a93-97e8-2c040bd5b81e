import type { Router, RouteRecordRaw } from 'vue-router';
import { BASE_URL } from '@/constant/app';
import { createRouter, createWebHistory } from 'vue-router';
import { evaluationRouter } from './routers/evaluation';
// import statusRoutes from './status.r'

const routes: RouteRecordRaw[] = [
    {
        path: '/preview',
        name: 'PreviewPage', // 预览试题相关页面
        component: () => import('@/views/preview/index.vue'),
    },
    {
        path: '/preview/admin',
        name: 'AdminPreviewPage', // 管理端预览试题相关页面
        component: () => import('@/views/preview/admin.vue'),
    },
    {
        path: '/',
        redirect: '/evaluation', // 默认重定向到测评项目
        component: () => import('@/views/layout/index.vue'),
        children: [...evaluationRouter],
    },
];

const router: Router = createRouter({
    history: createWebHistory(BASE_URL),
    routes: [
        ...routes,
        {
            path: '/:pathMatch(.*)*',
            name: 'page404',
            component: () => import(/** webpackChunkName: error */ '@/views/404.vue'),
        },
    ],
    scrollBehavior() {
        return { left: 0, top: 0 };
    },
});

export default router;
