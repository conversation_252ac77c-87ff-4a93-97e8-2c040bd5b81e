import type { NavigationGuardNext, RouteLocationNamedRaw, RouteLocationNormalized, RouteLocationPathRaw, Router } from 'vue-router';

export function useRouterNext(to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) {
    const nextWithQuery = (nextParams?: RouteLocationPathRaw | RouteLocationNamedRaw) => {
        const toQuery = JSON.parse(JSON.stringify(to.query));
        toQuery.pid = from.query.pid || to.query.pid;

        if (to.query.pid) {
            if (!nextParams) {
                next();
            } else {
                next({ query: toQuery, ...nextParams });
            }
            return;
        }
        if (from.query.pid) {
            next({
                path: to.path,
                query: toQuery,
                ...nextParams,
            });
        } else {
            if (!nextParams) {
                next();
            } else {
                next({ query: toQuery, ...nextParams });
            }
        }
    };
    return { nextWithQuery };
}
