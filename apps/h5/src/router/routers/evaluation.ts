// 测评项目
export const evaluationRouter = [
    {
        path: '/evaluation',
        name: 'Evaluation',
        component: () => import('@/views/evaluation/index.vue'),
        children: [
            {
                path: 'guidance', // 指导语页面
                name: 'EvaluationGuidance',
                component: () => import('@/views/evaluation/guidance/index.vue'),
            },
            {
                path: 'question', // 作答页面   /evaluation/question 作答页面路由不可修改，心跳检测依据当前路由地址判断用户当前是否处于答题阶段
                name: 'EvaluationQuestion',
                component: () => import('@/views/evaluation/question/index.vue'),
            },
            {
                path: 'status', // 状态页面
                name: 'EvaluationStatus',
                component: () => import('@/views/evaluation/status/index.vue'),
            },
            {
                path: 'preview', // 预览页面
                name: 'EvaluationPreview',
                component: () => import('@/views/evaluation/preview/index.vue'),
            },
        ],
    },
];
