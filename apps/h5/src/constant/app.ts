import type { IDeployEnv } from 'types/global';

// 应用层
export const APP_NAME = '瞰荐职测';
// 监控
export const MAGPIE_APP_KEY = '98dfa8f3498381f875911'; // 啄木鸟key
export const MAGPIE_APP_KEY_QA = 'fb45a94a45f59b9d19261'; // 啄木鸟key(测试环境)
// 环境变量
export const BUILD_ARG_VERSION = 'deployVersion'; // npm run build --deployVersion=1
export const BUILD_ARG_ENV = 'deployEnv'; // npm run build --deployEnv=prod

// 运行环境
export const DEPLOY_ENV = import.meta.env?.VITE_DEPLOY_ENV || 'prod';

export const BASE_URL = '/h5';

// 考试管理端域名
export const B_SITES = {
    rd: 'https://zhice-rd.weizhipin.com',
    qa: 'https://zhice-qa.weizhipin.com',
    pre: 'https://pre-zhice-admin.zhipin.com',
    prod: 'https://zhice-admin.zhipin.com',
};
