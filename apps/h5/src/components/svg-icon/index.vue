<template>
    <div v-if="isExternal" :style="styleExternalIcon" class="svg-external-icon svg-icon" :class="className" />
    <svg v-else class="svg-icon" :class="className" aria-hidden="true" :style="{ width: width || size, height: height || size }">
        <use :href="iconName" />
    </svg>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { isExternal as external } from '@/utils';

defineOptions({ name: 'SvgIcon' });

const props = defineProps({
    // icon图标
    icon: {
        type: String,
        required: true,
    },
    // 图标类名
    className: {
        type: String,
        default: '',
    },
    size: {
        type: String,
        default: '1em',
    },
    width: {
        type: String,
    },
    height: {
        type: String,
    },
});

// 判断是否为外部图标
const isExternal = computed(() => external(props.icon));
// 外部图标样式
const styleExternalIcon = computed(() => ({
    width: props.width || props.size,
    height: props.height || props.size,
    mask: `url(${props.icon}) no-repeat 50% 50%`,
    '-webkit-mask': `url(${props.icon}) no-repeat 50% 50%`,
}));
// 项目内的图标
const iconName = computed(() => `#icon-${props.icon}`);
</script>

<style lang="less" scoped>
.svg-icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}

.svg-external-icon {
    background-color: currentColor;
    mask-size: cover !important;
    display: inline-block;
}
</style>
