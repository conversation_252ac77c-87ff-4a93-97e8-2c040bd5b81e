/**
 * 对本地存贮对象
 */
export const Storage = {
    get(key: string) {
        const storage = this._getStorage();
        let str: string | null = '';
        if (storage) {
            str = storage.getItem(key);
            return str && JSON.parse(str);
        }
    },
    set(key: string, value: any) {
        const storage: Storage | undefined = this._getStorage();
        if (key && typeof value !== 'undefined') {
            storage?.setItem(key, JSON.stringify(value));
        }
    },
    /**
     * 清除本地存贮数据
     * @param key
     * @param {string} prefix 可选，如果包含此参数，则删除的是包含key的所有项，如果什么都不传则清空
     */
    del(key: string, prefix: any) {
        const storage = this._getStorage();
        if (storage) {
            if (key && prefix) {
                for (const key in storage) {
                    if (key.indexOf(prefix) === 0) {
                        storage.removeItem(key);
                    }
                }
            } else if (key) {
                storage.removeItem(key);
            } else {
                storage.clear();
            }
        }
    },
    _getStorage() {
        let _localStorage;
        try {
            /* 在Android 4.0下，如果webview没有打开localStorage支持，在读取localStorage对象的时候会导致js运行出错，所以要放在try{}catch{}中 */
            _localStorage = window.localStorage;
        } catch (e) {
            /* empty */
        }

        return _localStorage;
    },
};

/**
 * 对本地会话存贮对象
 */
export const SessionStorage = {
    get(key: string) {
        const storage = this._getStorage();
        let str: string | null = '';
        if (storage) {
            str = storage.getItem(key);
            return str && JSON.parse(str);
        }
    },
    set(key: string, value: any) {
        const storage: Storage | undefined = this._getStorage();
        if (key && typeof value !== 'undefined') {
            storage?.setItem(key, JSON.stringify(value));
        }
    },
    /**
     * 清除本地存贮数据
     * @param key
     * @param {string} prefix 可选，如果包含此参数，则删除的是包含key的所有项，如果什么都不传则清空
     */
    del(key: string, prefix: any) {
        const storage = this._getStorage();
        if (storage) {
            if (key && prefix) {
                for (const key in storage) {
                    if (key.indexOf(prefix) === 0) {
                        storage.removeItem(key);
                    }
                }
            } else if (key) {
                storage.removeItem(key);
            } else {
                storage.clear();
            }
        }
    },
    _getStorage() {
        let _sessionStorage;
        try {
            /* 在Android 4.0下，如果webview没有打开localStorage支持，在读取localStorage对象的时候会导致js运行出错，所以要放在try{}catch{}中 */
            _sessionStorage = window.sessionStorage;
        } catch (e) {
            /* empty */
        }

        return _sessionStorage;
    },
};
