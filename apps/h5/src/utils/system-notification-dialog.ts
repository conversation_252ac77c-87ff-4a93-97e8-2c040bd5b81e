import { showDialog, showLoadingToast } from 'vant';
import { jumpLogin } from '@/utils/jump-url';
import { HELP_PHONE } from '@crm/exam-constants';

let openTopNotification = false;

function onConfirm() {
    openTopNotification = false;
}

function initDialog(config: any) {
    if (openTopNotification) {
        return;
    }

    return showDialog({
        messageAlign: 'left',
        confirmButtonText: '知道了',
        allowHtml: true,
        className: 'user-kick-login-out-dialog-wrap',
        ...config,
    }).then(() => {
        onConfirm();
    });
}

// 用户被动踢出登录
export function userKickLoginOutDialog() {
    initDialog({
        title: '您已被移出本场考试',
        message: `即将退出登录，如有疑问请联系<a href="tel+${HELP_PHONE}">${HELP_PHONE}</a>`,
        width: '295px',
    })?.then(() => {
        jumpLogin();
    });

    openTopNotification = true;
}

// 用户切换屏幕提示弹窗
export function userCutScreenDialog() {
    return initDialog({
        title: '切换屏幕',
        message: '检测到您刚刚有切换屏幕操作，切换超过一定次数会影响测评可信度，作答期间请勿离开答题页面。',
    });
}

export function userForceRefresh() {
    if (openTopNotification) {
        return;
    }

    showLoadingToast({
        message: '网络异常\n即将刷新页面',
        duration: 3000,
    });
    openTopNotification = true;
    setTimeout(() => {
        window.location.reload();
    }, 2000);
}
