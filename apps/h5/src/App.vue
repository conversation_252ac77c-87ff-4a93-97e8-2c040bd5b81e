<template>
    <VanConfigProvider :themeVars="themeVars">
        <Suspense>
            <RouterView />
        </Suspense>
    </VanConfigProvider>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import { RouterView } from 'vue-router';
import { setAPMcustomUid } from '@/utils/apm';

const themeVars = reactive({});

setAPMcustomUid();
</script>

<style lang="less" scoped>
* {
    ::-webkit-scrollbar {
        width: 12px; /* 设置滚动条的宽度 */
    }

    ::-webkit-scrollbar-thumb {
        background-color: rgba(188, 188, 188, 0.3); /* 设置滑块的颜色 */
        border-radius: 5px; /* 设置滑块的圆角 */
    }
}
</style>
