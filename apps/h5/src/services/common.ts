import { get, postForm } from './http';

let serverTimeLastData: {
    data: {
        ts: number;
    };
} = {
    data: {
        ts: 0,
    },
};
let computeTimeTs: number = 0;
let computeTimeInterval: any = -1;

// 黑名单，预览页面过滤
function blackListCheck() {
    const url = window.location.href;
    const blackList = ['/preview', '/admin-preview'];
    let flag = false;
    for (let i = 0; i < blackList.length; i++) {
        if (url.includes(blackList[i])) {
            flag = true;
            break;
        }
    }
    return flag;
}

/**
 * https://api.weizhipin.com/project/2353/interface/api/651817
 * 获取服务器时间
 */
export function _getServerTime(params = {}) {
    return new Promise((resolve, reject) => {
        // if (blackListCheck()) {
        //     resolve({
        //         code: 0,
        //         data: {
        //             ts: new Date().getTime(),
        //         },
        //     })
        //     return
        // }

        if (serverTimeLastData && serverTimeLastData.data.ts) {
            if (serverTimeLastData.data.ts - computeTimeTs <= 1000) {
                resolve({
                    ...serverTimeLastData,
                    ts: computeTimeTs,
                });
                return;
            }
        }

        get(`/mapi/wap/common/time/currentTs.json`, params)
            .then((res) => {
                if (res.code === 0) {
                    serverTimeLastData = res;
                    computeTimeTs = res.data.ts;

                    if (computeTimeInterval > 0) {
                        clearInterval(computeTimeInterval);
                        computeTimeInterval = -1;
                    }

                    computeTimeInterval = setInterval(() => {
                        computeTimeTs -= 200;
                    }, 200);
                }
                resolve(res);
            })
            .catch((err: any) => {
                reject(err);
            });
    });
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/657473
 * 埋点接口
 */
export function _activeLog(params: { action: string; encExamId: string; p1?: string; p2?: number | string; p3?: string; p4?: string }) {
    return new Promise((resolve, reject) => {
        if (blackListCheck()) {
            resolve({ code: 0 });
            return;
        }

        postForm(`/mapi/wap/common/log/activeLog.json`, params)
            .then((res) => {
                resolve(res);
            })
            .catch((err: any) => {
                reject(err);
            });
    });
}
