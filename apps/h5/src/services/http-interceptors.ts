import type { AxiosRequestHeaders, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { DEPLOY_ENV } from '@/constant/app';
import { parseURL } from '@/utils';
import { apmSendAxiosError, clearPMcustomUid } from '@/utils/apm';
import { jumpLogin } from '@/utils/jump-url';
// import { getTraceID, magpieSend } from '@/utils/magpie'
import { userForceRefresh, userKickLoginOutDialog } from '@/utils/system-notification-dialog';
import axios from 'axios';
import { showToast } from 'vant';

const pid = (parseURL(window.decodeURIComponent(window.decodeURIComponent(window.location.href))).params as any).pid || '';
const isDevelopment = DEPLOY_ENV === 'dev';
const instance = axios.create({
    timeout: 40000, // 请求超时时间
    withCredentials: true, // 跨域
});

// 请求拦截器
instance.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
        const { url, data } = config;
        const params = Object.assign(config.params || {}, {
            _: Date.now(),
        });

        // 为所有请求添加自定义请求头
        // const traceId = getTraceID() // 链路追踪id
        const headers = {
            ...(config.headers || {}),
            // traceId,
        } as AxiosRequestHeaders & { traceId: string };

        if (config) {
            config.headers = headers;
        }

        if (!config.params) {
            config.params = params;
        }
        return config;
    },
    (error) => {
        Promise.reject(error);
    },
);
// 定义一个新的接口，扩展 AxiosRequestConfig，加入 noErrorToast 属性
interface CustomAxiosRequestConfig extends InternalAxiosRequestConfig {
    noErrorToast?: boolean; // 添加 noErrorToast 属性，类型可以是 boolean
}

// 扩展 AxiosResponse 类型，使其 config 使用 CustomAxiosRequestConfig
interface CustomAxiosResponse<T = any> extends AxiosResponse<T> {
    config: CustomAxiosRequestConfig;
}

const loginFailureCodes = [7, 108]; // 7: 登录方式发生变更！/ 当前登录状态已失效

// 响应拦截
instance.interceptors.response.use(
    (response: CustomAxiosResponse) => {
        const { data, config } = response;

        if (response.status !== 200) {
            // apmSendAxiosError({
            //     url: config.url,
            //     param: JSON.stringify(config.data) || JSON.stringify(config.params),
            //     config,
            //     response,
            //     traceId: config.headers.traceId,
            // }, 'status-error')

            Promise.reject(data);
        }

        // 用户被踢出
        if (data && data.code === 99) {
            userKickLoginOutDialog();
            return data;
        }

        // 强制用户刷新
        if (data && data.code === 31) {
            userForceRefresh();
            return data;
        }

        if (data && loginFailureCodes.includes(data.code)) {
            // 清空APM标记
            clearPMcustomUid();

            jumpLogin();
        }

        if (data.code !== 0) {
            const { traceId, ...otherData } = data;
            if (!config.noErrorToast) {
                showToast(data.message);
            }
            // 异常处理；与服务端约定，code为1、2、17、19时，进行错误监控，剩余情况服务端监控
            if ([1, 2, 17, 19].includes(data.code)) {
                apmSendAxiosError(
                    {
                        url: config.url,
                        param: JSON.stringify(config.data) || JSON.stringify(config.params),
                        config,
                        response,
                        traceId: config.headers.traceId,
                    },
                    'code-error',
                );
            }

            // magpieSend({
            //     errorType: 'codeError',
            //     errorCode: JSON.stringify(otherData),
            //     apiUrl: config.url,
            //     apiParam: JSON.stringify(config.data) || JSON.stringify(config.params),
            //     userId: undefined,
            //     json: JSON.stringify(config),
            //     traceId: config.headers.traceId,
            // })
        }

        return data;
    },
    (error) => {
        showToast(error.message === 'Network Error' ? '网络差，请保持网络通畅' : error.message);

        apmSendAxiosError(
            {
                url: error.config.url,
                param: error.config.data || error.config.params,
                config: error.config,
                info: error,
                traceId: error.config.headers?.traceId,
            },
            'internal-error',
        );

        // magpieSend({
        //     errorType: 'httpCatchError',
        //     errorCode: JSON.stringify(error.message),
        //     apiUrl: error.config.url, // 接口路径
        //     apiParam: JSON.stringify(error.config.data) || JSON.stringify(error.config.params), // 接口需要的参数
        //     userId: undefined,
        //     json: JSON.stringify(error),
        //     traceId: error.config.headers?.traceId,
        // })

        return Promise.reject(error);
    },
);

// 返回类型
export interface ResponseType {
    code: number;
    message: string;
    zpData: any;
    callback?: any;
}

export default instance;
