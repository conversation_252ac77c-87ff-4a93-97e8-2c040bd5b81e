const API = {
    login: {
        postLogout: '/wapi/web/logout',
    },
    common: {
        getInfo: '/mapi/wap/common/info.json',
        postFormActiveLog: '/common/log/activeLog.json', // 日志上报
        postSwitchScreen: '/wapi/web/exam/switchScreen/increase.json', // 切换屏幕上报
        postEnterStage: '/wapi/web/exam/enter/stage/save.json', // 进入场次，开始作答上报
        postHeartBeat: '/wapi/web/exam/common/heartbeat/report', // 心跳上报
        postFormLog: '/mapi/wap/common/log/activeLog.json', // 埋点接口
    },
    preview: {
        // 试卷预览
        paperBase: '/mapi/wap/evaluation/paper/sequence/template/preview.json', // 获取场次信息
        getAnswerList: '/mapi/wap/evaluation/paper/common/preview/answerList.json', // 试题列表
    },
    session: {
        getBaseInfo: '/wapi/web/exam/getMonoBase.json', // 获取大场次或小考试或小测评的基础信息
    },
    paper: {
        getPaperInfo: '/mapi/wap/examinee/paper/common/answerList.json', // 试题列表
        postFormAddAnswer: '/mapi/wap/evaluation/paper/addAnswer.json', // 试题提交
        postEvaluationPaperCommit: '/wapi/web/exam/evaluation/paper/commit.json', // 测评交卷接口
    },
};

export default {
    ...API,
};
