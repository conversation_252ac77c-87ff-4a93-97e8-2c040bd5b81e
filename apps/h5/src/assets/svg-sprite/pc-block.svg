<?xml version="1.0" encoding="utf-8" ?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="150.9998" height="140" viewBox="0 0 150.9998 140">
	<path transform="matrix(1 0 0 1 33.9998 11)" d="M65.7766 -7.19112e-14C73.2289 -7.19112e-14 80.1747 1.6498 86.3694 5.0236C92.7969 8.52418 97.9181 13.6044 101.479 20.0046C104.935 26.215 106.627 33.1912 106.627 40.6759C106.627 47.2538 105.193 53.3694 102.262 58.7624L102.157 58.9486L102.406 59.2644C107.553 65.9059 110.149 73.9243 110.247 82.7683L110.25 83.2895C110.25 91.2178 108.488 98.5543 104.869 105.046C101.165 111.691 95.8398 116.976 89.1512 120.658C82.6314 124.248 75.2851 126 67.3615 126L17.6945 126C7.92208 126 6.11088e-13 118.078 6.11088e-13 108.306L6.11088e-13 17.6945C6.11088e-13 7.92208 7.92208 -2.02834e-13 17.6945 -1.80719e-13L65.7766 -7.19112e-14Z" fill-rule="nonzero" fill="rgb(245, 245, 246)"/>
	<path transform="matrix(0.856164 0.516704 -0.516704 0.856164 64.6605 63.9108)" d="M0.122517 9.18343C0.0551188 4.1315 4.13187 3.6611e-14 9.18425 5.2906e-14L9.19803 5.29504e-14C14.2504 6.92449e-14 18.3134 4.1315 18.246 9.18343C18.0895 20.9125 17.8209 41.0436 17.6698 52.3696C17.6079 57.0119 13.827 60.7427 9.18425 60.7427L9.17169 60.7427C4.52897 60.7427 0.760602 57.0119 0.698668 52.3696L0.122517 9.18343Z" fill-rule="evenodd" fill="rgb(245, 154, 147)"/>
	<g style="mix-blend-mode: overlay">
		<path transform="matrix(0.856164 0.516704 -0.516704 0.856164 64.8902 64.0517)" d="M8.91413 0C13.8373 0 17.8283 3.98866 17.8283 8.90893C17.8283 13.8292 13.8373 17.8179 8.91413 17.8179C3.99099 17.8179 0 13.8292 0 8.90893C0 3.98866 3.99099 0 8.91413 0Z" fill-rule="nonzero" fill="rgb(0, 0, 0)" fill-opacity="0.2"/>
	</g>
	<path transform="matrix(0.856164 0.516704 -0.516704 0.856164 67.8282 75.9147)" d="M0.270125 0C0.419311 0 0.54025 0.120869 0.54025 0.269968C0.54025 0.419067 0.419311 0.539935 0.270125 0.539935C0.120939 0.539935 0 0.419067 0 0.269968C0 0.120869 0.120939 0 0.270125 0Z" fill-rule="nonzero" fill="rgb(163, 93, 83)"/>
	<path transform="matrix(1 0 0 1 78.8711 57.367)" d="M3.84908e-13 1.31324C3.84908e-13 1.31324 7.81544 0.368791 12.627 0.143999C17.4386 -0.0807934 27.139 0.0453307 27.139 0.0453307L10.7444 12.5483L3.84908e-13 1.31324Z" fill-rule="evenodd" fill="rgb(21, 179, 179)"/>
	<path transform="matrix(1 0 0 1 81.4004 49.2635)" d="M-4.02729e-15 23.4872L-4.02729e-15 13.5688C1.97977 13.506 3.81129 11.4508 4.05188 9.44886C4.29246 7.44695 4.322 -4.46552e-14 4.322 -4.46552e-14L16.7478 -9.25698e-14C16.7478 -9.25698e-14 16.7709 7.70301 17.288 9.44886C17.8051 11.1947 20.0457 13.2889 21.8801 13.3634L21.8801 23.4872L-4.02729e-15 23.4872Z" fill-rule="evenodd" fill="rgb(245, 153, 147)"/>
	<defs>
		<mask id="mask2856732081" style="mask-type:alpha">
			<path transform="matrix(1 0 0 1 81.4004 49.2635)" d="M-4.02729e-15 23.4872L-4.02729e-15 13.5688C1.97977 13.506 3.81129 11.4508 4.05188 9.44886C4.29246 7.44695 4.322 -4.46552e-14 4.322 -4.46552e-14L16.7478 -9.25698e-14C16.7478 -9.25698e-14 16.7709 7.70301 17.288 9.44886C17.8051 11.1947 20.0457 13.2889 21.8801 13.3634L21.8801 23.4872L-4.02729e-15 23.4872Z" fill-rule="evenodd" fill="rgb(245, 153, 147)"/>
		</mask>
	</defs>
	<g mask="url(#mask2856732081)">
		<g style="mix-blend-mode: overlay">
			<path transform="matrix(1 0 0 1 75.9741 39.0193)" d="M10.2458 19.8173C15.9044 19.8173 20.4916 14.3868 20.4916 9.26816C20.4916 4.1495 15.9044 0 10.2458 0C4.5872 0 0 4.1495 0 9.26816C0 14.3868 4.5872 19.8173 10.2458 19.8173Z" fill-rule="evenodd" fill="rgb(0, 0, 0)" fill-opacity="0.5"/>
		</g>
	</g>
	<path transform="matrix(1 0 0 1 80.465 23.0471)" d="M11.1427 0C15.7207 0 19.6539 1.57196 21.3681 3.81958C22.9884 4.46903 24.58 6.32615 25.4327 8.80256C26.2581 11.1998 26.1806 13.5661 25.3793 15.084C24.9297 16.6571 24.1981 18.5445 23.2384 20.5121C20.9257 25.2537 18.1649 28.6654 17.0719 28.1323C15.979 27.5993 16.9677 23.3232 19.2803 18.5816C19.3981 18.3402 19.517 18.1023 19.6367 17.8681C17.226 17.4433 15.3361 15.1494 15.0713 12.2829C13.8495 12.545 12.5255 12.6885 11.1427 12.6885C4.98874 12.6885 0 9.84806 0 6.34424C0 2.84041 4.98874 0 11.1427 0Z" fill-rule="evenodd" fill="rgb(20, 20, 20)"/>
	<path transform="matrix(-1 0 0 1 87.1379 37.7812)" d="M3.4846 12.2145C6.01684 12.7612 8.64082 10.4881 9.34543 7.1376C10.05 3.78705 8.56845 0.62777 6.03622 0.0811412C3.50399 -0.465488 0.88001 1.80754 0.1754 5.15808C-0.52921 8.50863 0.952371 11.6679 3.4846 12.2145Z" fill-rule="evenodd" fill="rgb(21, 179, 179)"/>
	<path transform="matrix(1 0 0 1 79.3854 26.4062)" d="M9.31931 0C14.4662 0 18.6386 4.16997 18.6386 9.31388C18.6386 14.4578 14.4662 18.6278 9.31931 18.6278C4.1724 18.6278 0 14.4578 0 9.31388C0 4.16997 4.1724 0 9.31931 0Z" fill-rule="nonzero" fill="rgb(20, 20, 20)"/>
	<path transform="matrix(1 0 0 1 81.1413 30.293)" d="M13.4979 23.8634C14.3349 23.3189 16.0805 22.0434 17.2266 21.0126C17.9783 20.3366 19.449 17.0439 19.449 13.5181C19.449 6.52138 18.8595 1.66164e-13 9.7245 4.02157e-14C0.589537 -8.57328e-14 3.20892e-15 5.14248 -3.6165e-18 13.2482C0 17.0686 0.921551 19.4027 1.69906 21.2233C2.47701 23.045 5.48324 25.1672 6.70596 25.6693C8.938 26.5857 11.3942 25.2322 13.4979 23.8634Z" fill-rule="evenodd" fill="rgb(245, 153, 147)"/>
	<defs>
		<mask id="mask2459666172" style="mask-type:alpha">
			<path transform="matrix(1 0 0 1 81.1404 28.836)" d="M-2.28759e-15 10.0372C-7.27831e-15 4.49381 4.49381 -1.071e-14 10.0372 -9.04489e-15L10.0523 -9.04036e-15C15.5957 -7.37486e-15 20.0744 4.49381 20.0744 10.0372L20.0744 12.5781L0 12.5781L-2.28759e-15 10.0372Z" fill-rule="evenodd" fill="rgb(0, 0, 0)"/>
		</mask>
	</defs>
	<g mask="url(#mask2459666172)">
		<path transform="matrix(1 0 0 1 81.4105 23.1575)" d="M19.4268 2.93297C17.521 1.1639 14.285 0 10.6135 0C4.75184 0 0 2.96678 0 6.6265C0 10.2862 4.75184 13.253 10.6135 13.253C12.3028 13.253 13.8999 13.0066 15.3177 12.5682C15.7149 15.4281 17.2378 17.844 19.3347 19.1684C19.2712 19.2954 19.208 19.4233 19.1451 19.5523C16.8324 24.2939 15.8437 28.57 16.9367 29.103C18.0297 29.6361 20.7905 26.2244 23.1031 21.4828C23.3096 21.0594 23.5056 20.6397 23.6904 20.2261C27.6996 19.8337 30.8492 15.8884 30.8492 11.0793C30.8492 6.00992 27.3495 1.90039 23.0323 1.90039C21.7318 1.90039 20.5055 2.27328 19.4268 2.93297Z" fill-rule="evenodd" fill="rgb(20, 20, 20)"/>
	</g>
	<path transform="matrix(1 0 0 1 87.6149 43.1172)" d="M0.69056 -0.363474C0.48982 -0.421284 0.28022 -0.305415 0.22241 -0.104675L-0.267667 1.59707Q-0.467567 2.06879 -0.314894 2.5611Q-0.158282 3.0661 0.292672 3.34521L0.900549 3.67145C1.08472 3.77004 1.31395 3.70066 1.41253 3.51649C1.51112 3.33232 1.44174 3.1031 1.25757 3.00451L0.681766 2.69627Q0.478876 2.56669 0.407652 2.33702Q0.335057 2.10294 0.434697 1.87881L0.949359 0.104675C1.00717 -0.0960644 0.891299 -0.305664 0.69056 -0.363474Z" fill-rule="evenodd" fill="rgb(165, 0, 0)"/>
	<path transform="matrix(1 0 0 1 84.2769 40.7519)" d="M-1.44671e-15 0.86173C-6.47714e-16 0.38581 0.38581 1.7794e-15 0.86173 3.9744e-15L0.863022 3.98036e-15C1.33894 6.17551e-15 1.72346 0.38581 1.72346 0.86173L1.72346 1.34177C1.72346 1.81769 1.33765 2.2035 0.86173 2.2035L0.860437 2.2035C0.384517 2.2035 -3.05162e-15 1.81769 -2.25262e-15 1.34177L-1.44671e-15 0.86173Z" fill-rule="evenodd" fill="rgb(0, 0, 0)"/>
	<path transform="matrix(1 0 0 1 92.916 40.7519)" d="M-1.44671e-15 0.86173C-6.47714e-16 0.38581 0.38581 1.7794e-15 0.86173 3.9744e-15L0.863022 3.98036e-15C1.33894 6.17551e-15 1.72346 0.38581 1.72346 0.86173L1.72346 1.34177C1.72346 1.81769 1.33765 2.2035 0.86173 2.2035L0.860437 2.2035C0.384517 2.2035 -3.05162e-15 1.81769 -2.25262e-15 1.34177L-1.44671e-15 0.86173Z" fill-rule="evenodd" fill="rgb(0, 0, 0)"/>
	<path transform="matrix(1 0 0 1 91.521 38.8046)" d="M1.26109 -0.292969Q0.47308 -0.182323 -0.12448 0.0105456C-0.33748 0.0792935 -0.454421 0.307699 -0.385673 0.520699C-0.316925 0.733698 -0.0885196 0.85064 0.12448 0.781892Q0.655266 0.610576 1.37379 0.509686Q2.77289 0.313234 3.61777 0.654249C3.82532 0.738023 4.06148 0.63768 4.14526 0.43013C4.22903 0.222579 4.12869 -0.0135898 3.92114 -0.0973632Q2.87506 -0.519591 1.26109 -0.292969Z" fill-rule="evenodd" fill="rgb(0, 0, 0)"/>
	<path transform="matrix(0.997564 0.0697565 -0.0697565 0.997564 82.6372 38.0527)" d="M0.0680916 0.441394Q-0.106661 0.511785 -0.208879 0.573265C-0.400679 0.688625 -0.462647 0.937632 -0.347288 1.12943C-0.231928 1.32123 0.0170792 1.3832 0.208879 1.26784Q0.255913 1.23955 0.370931 1.19322Q0.624233 1.09119 0.936918 1.03122Q1.96888 0.833284 3.19616 1.16729C3.41213 1.22606 3.63485 1.09863 3.69363 0.882664C3.7524 0.6667 3.62497 0.443976 3.409 0.385201Q2.00058 0.00190139 0.784239 0.235198Q0.39433 0.309984 0.0680916 0.441394Z" fill-rule="evenodd" fill="rgb(0, 0, 0)"/>
	<path transform="matrix(1 0 0 1 87.3462 49.0937)" d="M4.68385 -0.134304C4.60968 -0.32959 4.39124 -0.427773 4.19595 -0.3536Q2.56101 0.267381 1.19204 0.0225497Q0.775588 -0.0519298 0.437443 -0.198837Q0.280189 -0.267156 0.211831 -0.313366C0.0387651 -0.430356 -0.196376 -0.384897 -0.313366 -0.211831C-0.430356 -0.0387651 -0.384897 0.196376 -0.211831 0.313366Q-0.085467 0.398786 0.136002 0.495004Q0.555304 0.67717 1.05886 0.767227Q2.63391 1.04891 4.46456 0.3536C4.65984 0.279427 4.75803 0.0609824 4.68385 -0.134304Z" fill-rule="evenodd" fill="rgb(165, 0, 0)"/>
	<path transform="matrix(1 0 0 1 86.671 22.8066)" d="M17.304 13.9189C17.304 6.23172 11.6803 0 4.74316 0C3.65988 0 2.60864 0.151957 1.60584 0.437685C1.60901 0.437903 1.61219 0.438122 1.61536 0.438342C1.46952 0.480047 1.20679 0.561209 1.00345 0.657504C0.706073 0.798339 0 1.19974 0 1.19974C0 1.19974 1.18937 0.859253 2.26434 0.803969C3.22176 0.75473 4.27896 0.942073 4.4987 0.983631C10.0173 2.71879 14.0465 8.1802 14.0465 14.649C14.0465 15.6499 13.95 16.6267 13.7665 17.5693C14.6817 18.0814 15.5332 18.6835 16.3071 19.3624C16.9489 17.6904 17.304 15.8506 17.304 13.9189Z" fill-rule="evenodd" fill="rgb(21, 179, 179)"/>
	<path transform="matrix(1 0 0 1 97.7028 37.9473)" d="M3.4846 12.2145C6.01684 12.7612 8.64082 10.4881 9.34543 7.1376C10.05 3.78705 8.56845 0.62777 6.03622 0.0811412C3.50399 -0.465488 0.88001 1.80754 0.1754 5.15808C-0.52921 8.50863 0.952371 11.6679 3.4846 12.2145Z" fill-rule="evenodd" fill="rgb(21, 179, 179)"/>
	<path transform="matrix(1 0 0 1 100.681 40.1934)" d="M2.39872 8.56117C4.1486 8.93892 5.96759 7.34103 6.46155 4.9922C6.9555 2.64336 5.93737 0.433034 4.18748 0.0552898C2.4376 -0.322455 0.618609 1.27543 0.124655 3.62426C-0.369298 5.9731 0.648834 8.18343 2.39872 8.56117Z" fill-rule="evenodd" fill="rgb(115, 209, 209)"/>
	<path transform="matrix(1 0 0 1 70.491 110.816)" d="M-4.55991e-14 1.35088C-4.55991e-14 0.60481 0.60481 -5.05785e-16 1.35088 -5.05785e-16L41.8691 -5.05785e-16C42.6152 -5.05785e-16 43.22 0.604808 43.22 1.35088C43.22 4.40178 43.22 11.7963 43.22 14.8472C43.22 15.5932 42.6152 16.1981 41.8691 16.1981L1.35088 16.1981C0.60481 16.1981 -4.55991e-14 15.5932 -4.55991e-14 14.8472L-4.55991e-14 1.35088Z" fill-rule="evenodd" fill="rgb(245, 154, 147)"/>
	<path transform="matrix(1 0 0 1 65.4608 62.2227)" d="M0.0833328 14.9893C0.0278273 14.5309 -8.45529e-14 14.0696 -8.45529e-14 13.6079L-8.45529e-14 13.6014C-8.45529e-14 8.529 3.32552 4.06293 8.18311 2.60233L14.057 0.836139C15.9009 0.281696 17.8161 -8.09255e-15 19.7416 -8.09255e-15L33.8783 -8.09255e-15C35.7182 -8.09255e-15 37.5523 0.207531 39.3457 0.618657L50.5251 3.18145C55.1022 4.23071 58.347 8.30353 58.347 12.9993L58.347 13.0017C58.347 13.866 58.2358 14.7243 58.0161 15.5601C56.7833 20.2496 52.4251 36.829 50.3917 44.5644C49.7675 46.9389 47.6209 48.5942 45.1657 48.5942L8.94109 48.5942C6.20803 48.5942 3.90528 46.5534 3.57675 43.8402L0.0833328 14.9893Z" fill-rule="evenodd" fill="rgb(245, 153, 147)"/>
	<path transform="matrix(0.939693 -0.34202 0.34202 0.939693 105.885 70.2471)" d="M0.122517 9.18343C0.0551188 4.1315 4.13187 3.6611e-14 9.18425 5.2906e-14L9.19803 5.29504e-14C14.2504 6.92449e-14 18.3134 4.1315 18.246 9.18343C18.0895 20.9125 17.8209 41.0436 17.6698 52.3696C17.6079 57.0119 13.827 60.7427 9.18425 60.7427L9.17169 60.7427C4.52897 60.7427 0.760602 57.0119 0.698668 52.3696L0.122517 9.18343Z" fill-rule="evenodd" fill="rgb(245, 154, 147)"/>
	<g style="mix-blend-mode: overlay">
		<path transform="matrix(0.939693 -0.34202 0.34202 0.939693 106.139 70.1585)" d="M8.91413 0C13.8373 0 17.8283 3.98866 17.8283 8.90893C17.8283 13.8292 13.8373 17.8179 8.91413 17.8179C3.99099 17.8179 0 13.8292 0 8.90893C0 3.98866 3.99099 0 8.91413 0Z" fill-rule="nonzero" fill="rgb(0, 0, 0)" fill-opacity="0.2"/>
	</g>
	<path transform="matrix(0.939693 -0.34202 0.34202 0.939693 117.215 75.3101)" d="M0.270125 0C0.419311 0 0.54025 0.120869 0.54025 0.269968C0.54025 0.419067 0.419311 0.539935 0.270125 0.539935C0.120939 0.539935 0 0.419067 0 0.269968C0 0.120869 0.120939 0 0.270125 0Z" fill-rule="nonzero" fill="rgb(163, 93, 83)"/>
	<path transform="matrix(1 0 0 1 63.1999 61.9522)" d="M52.7247 2.60162C60.8588 4.56508 54.7411 28.9446 54.7411 28.9446L56.7997 71.5459C56.7997 71.5459 49.7998 75.0471 28.7998 75.047C11.2997 75.047 3.30004 75.0467 3.30004 75.0467L4.03915 32.8302C4.03915 32.8302 -3.51332 8.03231 2.03643 5.30122C7.58617 2.57013 20.2388 0 20.2388 0C20.2388 0 21.2751 7.27955 26.8282 7.27955C32.3813 7.27955 38.6039 0.269968 38.6039 0.269968C38.6039 0.269968 44.5907 0.638154 52.7247 2.60162Z" fill-rule="evenodd" fill="rgb(21, 179, 179)"/>
	<path transform="matrix(1 0 0 1 81.3977 68.1612)" d="M1.72025 -0.902322C1.22192 -0.96854 0.76425 -0.618234 0.698033 -0.119899Q0.569774 0.84533 0.38662 2.3901Q0.0205674 5.47747 -0.255357 8.38563Q-1.14292 17.7403 -0.786787 21.8258C-0.74313 22.3266 -0.30174 22.6972 0.199076 22.6535C0.699892 22.6099 1.0705 22.1685 1.02684 21.6677Q0.685098 17.7473 1.55701 8.55759Q1.83092 5.67063 2.19446 2.60444Q2.37611 1.07241 2.50268 0.119899C2.56889 -0.378435 2.21859 -0.836104 1.72025 -0.902322Z" fill-rule="evenodd" fill="rgb(255, 255, 255)"/>
	<path transform="matrix(1 0 0 1 98.8171 69.1748)" d="M0.566098 -0.0935884C0.617785 -0.593639 1.06507 -0.957116 1.56512 -0.905429C2.06517 -0.853742 2.42864 -0.406462 2.37696 0.0935884Q2.24386 1.38121 2.05893 3.55474Q1.68869 7.90615 1.43095 12.3236Q0.607168 26.4424 1.21484 35.9062C1.24706 36.4079 0.866471 36.8407 0.364789 36.873C-0.136893 36.9052 -0.569708 36.5246 -0.601921 36.0229Q-1.21675 26.4477 -0.386464 12.2176Q-0.127311 7.77594 0.244978 3.4004Q0.431309 1.21045 0.566098 -0.0935884Z" fill-rule="evenodd" fill="rgb(255, 255, 255)"/>
	<path transform="matrix(0.978148 0.207912 -0.207912 0.978148 47.9275 59.6592)" d="M-1.35048e-19 32.979C-1.35048e-19 32.979 12.9779 10.559 15.7437 6.78719C18.5096 3.01537 21.0868 2.02924 24.2777 0.1793C27.4686 -1.67064 37.3049 11.1838 32.7319 20.7955C28.159 30.4072 15.6753 48.0018 15.6753 48.0018L-1.35048e-19 32.979Z" fill-rule="evenodd" fill="rgb(21, 179, 179)"/>
	<path transform="matrix(-0.994522 0.104528 0.104528 0.994522 135.044 61.628)" d="M11.904 5.65898C14.8509 2.17082 17.0966 1.97396 21.8188 0.20791C26.541 -1.55814 27.7311 8.22363 26.7048 18.2848C25.6785 28.3459 20.0796 45.1476 20.0796 45.1476L-8.63197e-19 52.7975C-8.63197e-19 52.7975 1.56973 36.0485 4.4385 24.5317C7.30728 13.0149 10.5802 7.22596 11.904 5.65898Z" fill-rule="evenodd" fill="rgb(21, 179, 179)"/>
	<g opacity="0.2">
		<path transform="matrix(0.999391 0.0348995 -0.0348995 0.999391 64.7488 87.8604)" d="M0.181866 -0.35872C-0.016248 -0.339069 -0.160924 -0.162532 -0.141273 0.0355818L0.865755 10.188C0.885406 10.3862 1.06194 10.5308 1.26006 10.5112C1.45817 10.4915 1.60285 10.315 1.5832 10.1169L0.576168 -0.0355816C0.556517 -0.233696 0.37998 -0.378371 0.181866 -0.35872Z" fill-rule="evenodd" fill="rgb(0, 0, 0)" style="mix-blend-mode: multiply"/>
	</g>
	<g opacity="0.2">
		<path transform="matrix(1 0 0 1 117.995 93.7022)" d="M0.140153 -0.254677C-0.0589332 -0.25476 -0.220395 -0.0934329 -0.220478 0.105653L-0.225418 11.9209C-0.225501 12.12 -0.0641744 12.2814 0.134912 12.2815C0.333998 12.2816 0.49546 12.1203 0.495543 11.9212L0.500483 0.105955C0.500566 -0.0931314 0.339239 -0.254593 0.140153 -0.254677Z" fill-rule="evenodd" fill="rgb(0, 0, 0)" style="mix-blend-mode: multiply"/>
	</g>
	<g opacity="0.2">
		<path transform="matrix(1 0 0 1 97.7356 108.011)" d="M-0.355541 0.240788C-0.355541 0.439874 -0.194146 0.601269 0.00493984 0.601269L20.2643 0.601269C20.4634 0.601269 20.6248 0.439874 20.6248 0.240788C20.6248 0.0417016 20.4634 -0.119693 20.2643 -0.119693L0.00493984 -0.119693C-0.194146 -0.119693 -0.355541 0.0417016 -0.355541 0.240788Z" fill-rule="evenodd" fill="rgb(0, 0, 0)" style="mix-blend-mode: multiply"/>
	</g>
	<path transform="matrix(0.0348995 0.999391 -0.999391 0.0348995 140.573 107.323)" d="M0.16873 8.64235C0.0760804 3.89685 3.89759 -3.10027e-14 8.644 -3.10027e-14L8.65697 -3.10027e-14C13.4034 -3.10027e-14 17.2119 3.89685 17.1193 8.64235C16.9095 19.3894 16.5546 37.5624 16.3551 47.7813C16.2731 51.9814 12.8449 55.3433 8.644 55.3433L8.63265 55.3433C4.43178 55.3433 1.01486 51.9814 0.932862 47.7813L0.16873 8.64235Z" fill-rule="evenodd" fill="rgb(21, 179, 179)"/>
	<path transform="matrix(-0.987492 0.157669 -0.157669 -0.987492 55.4363 119.944)" d="M0.16873 8.64235C0.0760804 3.89685 3.89759 -3.10027e-14 8.644 -3.10027e-14L8.65697 -3.10027e-14C13.4034 -3.10027e-14 17.2119 3.89685 17.1193 8.64235C16.9095 19.3894 16.5546 37.5624 16.3551 47.7813C16.2731 51.9814 12.8449 55.3433 8.644 55.3433L8.63265 55.3433C4.43178 55.3433 1.01486 51.9814 0.932862 47.7813L0.16873 8.64235Z" fill-rule="evenodd" fill="rgb(245, 154, 147)"/>
	<g style="mix-blend-mode: overlay">
		<path transform="matrix(-0.987492 0.157669 -0.157669 -0.987492 55.1702 119.987)" d="M8.37388 0C12.9986 0 16.7478 3.74693 16.7478 8.369C16.7478 12.9911 12.9986 16.738 8.37388 16.738C3.74911 16.738 0 12.9911 0 8.369C0 3.74693 3.74911 0 8.37388 0Z" fill-rule="nonzero" fill="rgb(0, 0, 0)" fill-opacity="0.2"/>
	</g>
	<path transform="matrix(-0.987492 0.157669 -0.157669 -0.987492 45.8912 113.268)" d="M0.270125 0C0.419311 0 0.54025 0.120869 0.54025 0.269968C0.54025 0.419067 0.419311 0.539935 0.270125 0.539935C0.120939 0.539935 0 0.419067 0 0.269968C0 0.120869 0.120939 0 0.270125 0Z" fill-rule="nonzero" fill="rgb(163, 93, 83)"/>
	<path transform="matrix(-0.809751 -0.586774 -0.586774 0.809751 57.7706 57.6959)" d="M10.4748 29.8099L12.8514 27.6461C13.171 27.355 13.5076 27.0832 13.8594 26.8319L17.0143 24.5781C18.0355 23.8487 19.0066 23.0516 19.9212 22.1923L23.7072 18.6352C23.7735 18.5729 23.8389 18.5093 23.903 18.4446L29.0482 13.2536C29.7494 12.5461 29.8103 11.4551 29.2577 10.6262L29.2577 10.6262L29.6703 10.2011C30.1379 9.71936 30.2464 8.99307 29.9399 8.39567L29.5677 7.67001L29.5677 7.67001C29.5677 7.05621 29.8457 6.47837 29.4576 6.00287L29.4073 5.94125C29.0472 5.50016 28.4745 5.2916 27.9151 5.39784L27.3027 5.51415L27.3027 5.51415C26.7789 4.85946 25.9054 4.59824 25.1212 4.89488L18.0703 7.56216L10.4771 10.4845C10.1458 10.612 9.80791 10.3143 9.89261 9.96964L11.3488 4.04313C11.4779 3.51782 11.3736 2.96236 11.0628 2.51963L11.0608 2.51679C10.2542 1.36762 8.5188 1.48425 7.86881 2.72874L4.96984 8.27918C4.80727 8.59045 4.68556 8.92142 4.60774 9.26386L4.26209 10.785C3.61992 13.6109 2.22738 16.2113 0.231189 18.3121L0.000259284 18.5552L10.4748 29.8099Z" fill-rule="evenodd" fill="rgb(245, 154, 147)"/>
	<g opacity="0.5" style="mix-blend-mode: overlay">
		<path transform="matrix(-0.809751 -0.586774 -0.586774 0.809751 43.2419 60.0142)" d="M-0.0202324 -0.359912C-0.219005 -0.348739 -0.371086 -0.17854 -0.359912 0.0202324L-0.0408604 5.69588C-0.0296865 5.89465 0.140512 6.04673 0.339284 6.03556C0.538057 6.02438 0.690138 5.85418 0.678964 5.65541L0.359912 -0.0202322C0.348739 -0.219005 0.17854 -0.371086 -0.0202324 -0.359912Z" fill-rule="evenodd" fill="rgb(3, 3, 3)"/>
	</g>
	<g opacity="0.5" style="mix-blend-mode: overlay">
		<path transform="matrix(-0.809751 -0.586774 -0.586774 0.809751 40.7051 59.2228)" d="M-0.211246 0.292098C-0.372566 0.175431 -0.408765 -0.0499264 -0.292098 -0.211246C-0.175431 -0.372566 0.0499264 -0.408765 0.211246 -0.292098L8.21506 5.49629C8.37638 5.61296 8.41258 5.83831 8.29591 5.99963C8.17925 6.16095 7.95389 6.19715 7.79257 6.08049L-0.211246 0.292098Z" fill-rule="evenodd" fill="rgb(3, 3, 3)"/>
	</g>
	<g opacity="0.5" style="mix-blend-mode: overlay">
		<path transform="matrix(-0.809751 -0.586774 -0.586774 0.809751 40.3706 52.4318)" d="M10.3095 -0.161027C10.2205 -0.339146 10.004 -0.411448 9.82592 -0.322516L-0.161026 4.66378C-0.339146 4.75271 -0.411448 4.96921 -0.322516 5.14733C-0.233585 5.32544 -0.0170928 5.39775 0.161027 5.30882L10.148 0.322516C10.3261 0.233585 10.3984 0.0170927 10.3095 -0.161027Z" fill-rule="evenodd" fill="rgb(3, 3, 3)"/>
	</g>
	<g opacity="0.5" style="mix-blend-mode: overlay">
		<path transform="matrix(-0.809751 -0.586774 -0.586774 0.809751 37.1306 52.3488)" d="M9.07821 -0.316398C9.25295 -0.411798 9.47195 -0.347479 9.56735 -0.172739C9.66275 0.00200181 9.59843 0.220998 9.42369 0.316398L0.172738 5.36698C-0.00200197 5.46238 -0.220998 5.39806 -0.316398 5.22332C-0.411798 5.04858 -0.347479 4.82958 -0.172738 4.73418L9.07821 -0.316398Z" fill-rule="evenodd" fill="rgb(3, 3, 3)"/>
	</g>
	<g opacity="0.5" style="mix-blend-mode: overlay">
		<path transform="matrix(-0.809751 -0.586774 -0.586774 0.809751 33.4979 53.5033)" d="M6.86849 -0.18112C6.76846 -0.353252 6.54782 -0.411705 6.37569 -0.311676L-0.18112 3.49859C-0.353252 3.59862 -0.411705 3.81925 -0.311676 3.99138C-0.211647 4.16352 0.00898746 4.22197 0.18112 4.12194L6.73793 0.311676C6.91006 0.211647 6.96851 -0.00898752 6.86849 -0.18112Z" fill-rule="evenodd" fill="rgb(3, 3, 3)"/>
	</g>
	<path transform="matrix(0.945925 0.324386 -0.324386 0.945925 34.6469 64.5532)" d="M7.5635 0C11.7407 0 15.127 3.38432 15.127 7.55909C15.127 11.7339 11.7407 15.1182 7.5635 15.1182C3.38629 15.1182 0 11.7339 0 7.55909C0 3.38432 3.38629 0 7.5635 0Z" fill-rule="nonzero" fill="rgb(245, 154, 147)"/>
	<path transform="matrix(-0.99439 0.105771 -0.105771 -0.99439 40.1349 74.8181)" d="M0.720961 0C1.11914 0 1.44192 0.322785 1.44192 0.720961C1.44192 1.11914 1.11914 1.44192 0.720961 1.44192C0.322785 1.44192 0 1.11914 0 0.720961C0 0.322785 0.322785 0 0.720961 0Z" fill-rule="nonzero" fill="rgb(245, 154, 147)"/>
	<path transform="matrix(0.992546 0.121869 -0.121869 0.992546 71.892 102.252)" d="M24.8907 4.78983L21.7168 4.83631C18.8024 4.879 15.9325 4.1183 13.4218 2.63763L11.5119 1.51121C10.9831 1.1994 10.3983 0.994438 9.79065 0.907958L5.38676 0.281245C4.41035 0.142293 3.58222 0.995874 3.75066 1.96763L3.75085 1.96876C3.82263 2.38401 4.07135 2.74636 4.4323 2.96385L7.16818 4.61237C7.2294 4.64927 7.20702 4.74302 7.13573 4.74827L2.01759 5.12533C0.895479 5.208 0.0442399 6.17079 0.0996762 7.29457L0.0997858 7.29679C0.141534 8.14309 0.694089 8.87654 1.49528 9.15232L1.67395 9.21382L3.55257 14.6865C4.49043 17.4186 6.68989 19.5297 9.45816 20.3548L9.46095 20.3556C10.5467 20.6779 11.6746 20.8212 12.8063 20.7778L23.1579 20.3813L24.8907 4.78983Z" fill-rule="evenodd" fill="rgb(245, 154, 147)"/>
	<path transform="matrix(0.997564 0.0697565 -0.0697565 0.997564 92.6111 107.984)" d="M0 0L4.86497 0L4.86497 16.2L0 16.2L0 0Z" fill-rule="nonzero" fill="rgb(115, 209, 209)"/>
	<path transform="matrix(1 0 0 1 58.4958 120.383)" d="M-1.47316e-13 0.000183505L18.332 3.85899L35.1591 3.85899C35.5647 3.64194 35.8856 3.29488 36.0702 2.87415C36.2547 2.45342 36.2927 1.98285 36.1776 1.53793C36.0626 1.09336 35.801 0.699716 35.4352 0.420914C35.0694 0.142112 34.6203 -0.00595591 34.1601 0.000183505L-1.47316e-13 0.000183505Z" fill-rule="nonzero" fill="rgb(204, 205, 210)"/>
	<path transform="matrix(1 0 0 1 10.9997 80.3105)" d="M68.3758 39.469L56.1637 2.24978C55.9489 1.59585 55.5313 1.02596 54.9699 0.621606C54.4085 0.217616 53.7326 0 53.039 0L3.28577 0C2.76715 0 2.2559 0.121736 1.79383 0.355152C1.33176 0.588569 0.931904 0.927203 0.627135 1.3434C0.322365 1.75924 0.121348 2.2408 0.0403363 2.74893C-0.0406758 3.2567 0.000716656 3.77632 0.161127 4.26542L12.3732 41.4846C12.5878 42.1385 13.0057 42.7084 13.5669 43.1128C14.1282 43.5168 14.8041 43.7347 15.4978 43.7347L65.2511 43.7347C65.7698 43.7347 66.2812 43.613 66.743 43.3792C67.2052 43.1458 67.605 42.8072 67.9097 42.391C68.2143 41.9751 68.4156 41.4936 68.4968 40.9855C68.5775 40.4777 68.5363 39.9581 68.3758 39.469Z" fill-rule="nonzero" fill="rgb(204, 205, 210)"/>
	<path transform="matrix(1 0 0 1 38.569 96.9355)" d="M5.28204 10.4861C8.1992 10.4861 10.564 8.1388 10.564 5.24305C10.564 2.34731 8.1992 0 5.28204 0C2.36488 0 0 2.34731 0 5.24305C0 8.1388 2.36488 10.4861 5.28204 10.4861Z" fill-rule="nonzero" fill="rgb(255, 255, 255)"/>
	<path transform="matrix(1 0 0 1 12 124)" d="M1.48597 0C0.665291 0 0 0.665291 0 1.48597L0 1.51403C0 2.33471 0.665291 3 1.48597 3L128.514 3C129.335 3 130 2.33471 130 1.51403L130 1.48597C130 0.665291 129.335 0 128.514 0L1.48597 0Z" fill-rule="nonzero" fill="rgb(26, 26, 26)"/>
	<path transform="matrix(1 0 0 1 75.4554 58.4863)" d="M5.88927 1.21105C4.43116 -0.0960668 3.41083 -0.531868 2.39021 0.84793C1.36988 2.22773 0.281821 5.40395 0.281821 5.40395C0.281821 5.40395 -0.809226 7.13276 1.30215 8.23566C3.41353 9.33855 14.1131 11.0977 14.1131 11.0977C14.1131 11.0977 15.2984 11.3287 14.4952 10.4128C14.4952 10.4128 6.5162 1.77328 5.88927 1.21105Z" fill-rule="evenodd" fill="rgb(115, 209, 209)"/>
	<path transform="matrix(1 0 0 1 89.8992 57.2188)" d="M21.3556 4.84896C22.5002 6.8036 22.8807 7.22321 20.1514 8.33862C17.422 9.45403 9.84397 12.6553 0.278014 12.3916C-0.000860989 12.3841 -0.104394 12.0221 0.128915 11.869C3.41572 9.71326 13.8102 1.49385 14.3515 1.06781C15.1123 0.469413 15.8416 1.42574e-13 16.472 1.42574e-13C17.5668 1.42574e-13 20.2438 2.95062 21.3556 4.84896Z" fill-rule="evenodd" fill="rgb(115, 209, 209)"/>
</svg>
