import { jumpExamList } from '@/utils/jump-url';
import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useExamStore = defineStore('basePaperStore', () => {
    const seqId = ref('');
    const examId = ref('');
    const examBaseInfo = ref<any>({
        examineeId: '',
        encryptUserId: '', // 加密小考试id
        userName: '', // 考生姓名
        mobile: '', // 考生加密手机号
        seqInfo: {
            // 大场次信息
            seqId: '', // 大场次加密Id
            seqName: '', // 大场次名称
            seqStartTime: '', // 大场次开始时间
            seqEndTime: '', // 大场次结束时间
            seqRemarks: '', // 大场次作答说明
            canDebug: true, // 是否考前准备
            wsConnectSecret: '', // ws连接密码
        },
        examInfo: {
            // 小考试信息
            examType: 1, // 1-考试 2-量表 3-GBA 4-hots
            examId: '', // 加密小考试id
            examName: '', // 小考试名称
            examRemarks: '小考试答题说明', // 小考试答题说明
            operateGuide: '<h1>富文本</h1>', // 小考试（测评独有）操作指引：富文本
            interruptDuration: 6000, // 小考试（测评独有）试卷答题过程中 用户中断时长
            questionListType: 0, // 小考试（测评独有）答题列表类型
            sort: 1, // 排序 从1开始
            answerMode: 1, // 作答模式 1定时模式 2即时模式
            examStartTime: '2023-01-01 14:00:00', // 开始作答时间
            questionCount: 0, // 题目数量
            status: 1, // 考试状态：0-待启用；1-待开始；2-考试中；3-已结束
            statusDesc: '考试中', // 考试状态描述
            hasCommitPaper: true, // 是否已交卷:true-已交卷
            remainSeconds: 6000, // 考试结束剩余秒数
            adviseDurationStr: '90分钟', // 建议作答时长
            hasInvigilator: true, // 是否有监考官
            openCountDown: true, // （考试独有）开启倒计时
            productId: 0, // 产品id
            questionTypeList: [], // 题目类型列表
            timedRuleType: 1, // 倒计时类型 1-正计时 2-倒计时
            reviewRuleType: 1, // 回看规则类型 1-只能修改一次 2-只能修改一次
            paperReadyClickTimeTs: 0, // 试卷准备就绪点击时间戳(如果有时间，则表示看过指导语，已看过试卷)
        },
    });

    const initData = (data: any) => {
        examBaseInfo.value = {
            ...examBaseInfo.value,
            ...data,
        };
    };

    const getSessionInfo = async (params: { seqId: string; examId: string }) => {
        const { code, data } = await Invoke.session.getBaseInfo(params);

        // 小场次id 校验不通过 则跳转至考试列表页
        if (code === 108) {
            jumpExamList();
            return;
        }

        if (code === 0) {
            seqId.value = params.seqId;
            examId.value = params.examId;

            initData(data);
        }
    };

    const fetchPreviewBaseInfo = async (params: { key: string }) => {
        const { code, data } = await Invoke.preview.paperBase(params);
        if (code === 0 && data) {
            initData(data);
        }
    };

    function updateExamConfig(config: any) {
        examBaseInfo.value = {
            ...examBaseInfo.value,
            ...config,
        };
    }

    return {
        examBaseInfo,
        seqId,
        examId,
        getSessionInfo,
        fetchPreviewBaseInfo,
        updateExamConfig,
    };
});
