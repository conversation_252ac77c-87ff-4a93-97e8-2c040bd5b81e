export interface PaperInfo {
    earlyLogin: number; // 是否允许提前登录
    reviewRuleType: number; // 回看规则：1:可回看一题   2:不可回看   3:不限制回看
    timedRuleType: number; // 计时规则 ：1:正计时   2:倒计时
    submit: number; // 用户是否已提交  1:是   0:否
    nextMethod: number; // 进入下一题：1:选择后自动进入   2手动点击
    examEndTimeTs: number; // 场次结束时间戳
    adviseAnswerTime: string; // 预计答题时间
    totalQuestionCount: number; // 试卷试题数量
    operatingInstruction: string; // 试卷 操作指引（富文本）
    answerInstruction: string; //  试卷 作答说明（富文本）
    paperReadyClickTimeTs: number; //
    paperStartTimeTs: number; // 试卷开始时间
    paperEndTimeTs: number; // 试卷结束时间
    answerLeaveMillionSecs: number; // 试卷答题过程中 用户中断时长
    productId: number; // 产品ID
    answerStatus: number; // 答题状态
    answerMode: number; // 答题模式
    questionType: string[]; // 试卷类型
    maxAnswerTime: string; // 最大答题时间
    instruction: string; // 试卷 测验须知
    answerListType: number; // 答题列表类型
}

export interface PaperConfig {
    userName: string;
    userId: string;
    examStartTime: string;
    examEndTime: string;
    examStartTimeTs: number;
    examEndTimeTs: number;
    encExamId: string;
    encCorpId: string;
    corpName: string;
    customerName: string;
    currentTs: number;
    paperInfoList: PaperInfo[];
}

/*
 * 考试状态
 * - no-start 未开始
 * - preparing 等待开始中
 * - ready 可进行考试或正在考试
 * - err 错误页面（非法进入， 如参数不全等）
 * - finished 已完成，未结束（答题结束，场次未结束）
 * - over 未完成，已结束 (倒计时结束，未打答完，交卷)
 * - finished-over 已完成，已结束（正常答题结束，已回答完所有题）
 */

export type ExamStatus = 'no-start' | 'err' | 'preparing' | 'finished' | 'over' | 'finished-over' | 'answering' | 'ready';
