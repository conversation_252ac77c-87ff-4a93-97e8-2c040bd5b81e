import { parseURL } from '@/utils';
import { createTimeManager } from '@crm/exam-hooks';

export async function heartBeatCheck() {
    const { search, pathname } = window.location;
    const { seqId, examId } = parseURL(search).params as any;

    // 是否在答题页
    const answerQuestion = !!pathname.includes('/evaluation/question');

    const params = {
        encSeqId: seqId,
        encExamId: examId,
        answerQuestion,
        type: 0, // 写死参数 表示动作是在线
    };

    try {
        // 预览页面不需要校对时间
        if (['/h5/preview', '/h5/preview/admin'].includes(window.location.pathname)) {
            return Date.now();
        }
        const res = await Invoke.common.postHeartBeat(params, { noErrorToast: true });
        return res.code === 0 ? res.data.ts : Date.now();
    } catch (error) {
        return Date.now();
    }
}

export const { timeCenter, createTimer } = createTimeManager({ syncTime: heartBeatCheck });
