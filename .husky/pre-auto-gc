# This hook is called just before Git runs its automatic garbage collection

# Check if we're in a CI environment
if [ -n "$CI" ]; then
  # Skip GC in CI environments to save time
  echo "Skipping Git garbage collection in CI environment"
  exit 1
fi

# Check repository size before GC
REPO_SIZE_BEFORE=$(du -sh .git | cut -f1)

# Log the event
echo "🧹 Git is about to run garbage collection"
echo "📊 Repository size before GC: $REPO_SIZE_BEFORE"

# Optionally, you could add more checks here, like:
# - Check if the repo is too large and needs manual intervention
# - Check if there are too many loose objects
# - Check if this is a good time for GC (e.g., not during work hours)

# Let Git continue with GC
exit 0 