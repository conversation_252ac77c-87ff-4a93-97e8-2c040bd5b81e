# Check if package.json or pnpm-lock.yaml has changed
if git diff-tree -r --name-only --no-commit-id ORIG_HEAD HEAD | grep -E 'package.json|pnpm-lock.yaml|pnpm-workspace.yaml' > /dev/null; then
  echo "📦 Package files changed. Installing dependencies..."
  pnpm install
fi

# Check if there are new migrations or schema changes
if git diff-tree -r --name-only --no-commit-id ORIG_HEAD HEAD | grep -E 'migrations|schema' > /dev/null; then
  echo "🔄 Database schema may have changed. Consider running migrations."
fi
