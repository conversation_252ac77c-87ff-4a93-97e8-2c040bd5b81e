# This hook is invoked by git am. It takes a single parameter, the name of the file 
# that holds the proposed commit message.

# Run commitlint on the patch message
npx --no-install commitlint --edit "$1"

# Check for ticket reference in commit message
if ! grep -E '\[(([A-Z]+-)?[0-9]+)\]' "$1" > /dev/null; then
  echo "⚠️ Warning: Commit message doesn't contain a ticket reference like [ABC-123]"
  echo "   Consider adding a ticket reference to improve traceability."
fi 