# $1 is the upstream branch
# $2 is the rebased branch (or empty when rebasing the current branch)

CURRENT_BRANCH=$(git symbolic-ref --short HEAD)
TARGET_BRANCH=${2:-$CURRENT_BRANCH}

# Prevent rebasing protected branches
if [ "$TARGET_BRANCH" = "main" ] || [ "$TARGET_BRANCH" = "master" ] || [ "$TARGET_BRANCH" = "develop" ]; then
  echo "❌ Error: Rebasing $TARGET_BRANCH is not allowed. This is a protected branch."
  exit 1
fi

# Stash check - warn if there are uncommitted changes
if ! git diff --quiet || ! git diff --cached --quiet; then
  echo "⚠️ Warning: You have uncommitted changes that will be stashed during rebase."
  echo "   Consider committing or stashing them manually before rebasing."
fi

# Warn about potential conflicts based on history
CONFLICT_FILES=$(git log --name-only --pretty=format: $1..$TARGET_BRANCH | sort | uniq -c | sort -nr | head -5)
if [ -n "$CONFLICT_FILES" ]; then
  echo "ℹ️ Files with most changes (potential conflict areas):"
  echo "$CONFLICT_FILES"
fi 