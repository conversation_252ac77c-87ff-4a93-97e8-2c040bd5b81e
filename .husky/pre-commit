# Run lint-staged to lint and format files
# npm run precommit

# Check for console.log statements
# git diff --cached --name-only | grep -E '\.(js|jsx|ts|tsx|vue)$' | xargs grep -l 'console\.log' | xargs -r echo "Warning: console.log found in:"

# # Prevent committing to main/master directly
# branch="$(git symbolic-ref HEAD 2>/dev/null)" || branch="(unnamed branch)"
# branch=${branch##refs/heads/}
# if [ "$branch" = "main" ] || [ "$branch" = "master" ]; then
#   echo "Error: Direct commits to $branch branch are not allowed."
#   exit 1
# fi

# # Prevent committing large files
# git diff --cached --name-only | while read file; do
#   if [ -f "$file" ]; then
#     size=$(du -k "$file" | cut -f1)
#     if [ $size -gt 500 ]; then
#       echo "Error: $file is larger than 500KB. Please reconsider committing this file."
#       exit 1
#     fi
#   fi
# done

# 运行 code-guard 暂存区检查
echo "🔍 运行暂存区代码门禁检查..."
# pnpm run precommit

# 如果 code-guard 工具已实现，可以改用：
# pnpm code-guard staged
