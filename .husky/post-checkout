# Args: previous HEAD, new HEAD, is_branch_checkout (0 for file checkout, 1 for branch checkout)
if [ "$3" = "1" ]; then
  # Only run when checking out branches, not files
  
  # Run the postcheckout script from package.json
  npm run postcheckout
  
  # Show branch information
  echo "📋 Current branch: $(git branch --show-current)"
  
  # Check if there are stashed changes
  STASH_COUNT=$(git stash list | wc -l | tr -d ' ')
  if [ "$STASH_COUNT" -gt 0 ]; then
    echo "💾 You have $STASH_COUNT stashed change(s)"
  fi
  
  # Show recent commits on this branch
  echo "🔄 Recent commits on this branch:"
  git log --oneline -n 3
fi
