# This hook is called by git-receive-pack on the remote repository,
# which happens when a git push is done on a local repository.
# The hook executes once for each ref being updated.

# Parameters:
# $1 - the name of the ref being updated (e.g. "refs/heads/main")
# $2 - the old object name stored in the ref
# $3 - the new object name to be stored in the ref

# Extract the branch name from the ref
BRANCH=${1#refs/heads/}

# Check if this is a protected branch
# if [ "$BRANCH" = "main" ] || [ "$BRANCH" = "master" ] || [ "$BRANCH" = "develop" ]; then
#   echo "🔒 Validating push to protected branch: $BRANCH"
  
#   # Check for forced pushes (non-fast-forward updates)
#   if [ "$2" != "0000000000000000000000000000000000000000" ]; then
#     if ! git merge-base --is-ancestor "$2" "$3"; then
#       echo "❌ Error: Force-pushing to $BRANCH is not allowed"
#       exit 1
#     fi
#   fi
  
#   # Check commit messages in the pushed commits
#   git log --pretty=format:'%H %s' "$2".."$3" | while read -r COMMIT_HASH COMMIT_MSG; do
#     # Verify commit message format using commitlint
#     if ! echo "$COMMIT_MSG" | npx --no-install commitlint > /dev/null; then
#       echo "❌ Error: Commit $COMMIT_HASH has an invalid commit message format"
#       exit 1
#     fi
#   done
# fi

# Allow the push
exit 0 