# Get the current branch name
BRANCH_NAME=$(git symbolic-ref --short HEAD)

# Extract ticket number from branch name (e.g., feature/ABC-123-something)
TICKET=$(echo "$BRANCH_NAME" | grep -o -E '[A-Z]+-[0-9]+' | head -1)

# If a ticket number was found and the commit message doesn't already contain it
if [ -n "$TICKET" ] && ! grep -q "$TICKET" "$1"; then
  # Prepend the ticket number to the commit message
  echo "[$TICKET] $(cat $1)" > "$1"
fi 