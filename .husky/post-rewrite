# This hook is called after git commit --amend or git rebase
# $1 is the command (amend or rebase)

echo "🔄 Git $1 operation completed"

# If this was a rebase, check if any conflicts were resolved
if [ "$1" = "rebase" ]; then
  # Check if there are any merge conflict markers left in files
  if git grep -l "<<<<<<< HEAD" $(git ls-files) > /dev/null; then
    echo "⚠️ Warning: Some files may still have conflict markers (<<<<<<< HEAD)"
  fi
fi

# Remind about force push if needed
echo "💡 Tip: You may need to force push after a $1 operation: git push --force-with-lease" 