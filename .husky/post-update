# This hook is invoked by git-receive-pack on the remote repository,
# which happens when a git push is done on a local repository.
# It is called after the refs have been updated.

# The hook is given the list of refs that were updated as its arguments.

echo "🔄 Repository updated with new changes"

# Check if package files were updated
PACKAGE_FILES_UPDATED=false
for REF in "$@"; do
  if git diff-tree -r --name-only --no-commit-id ORIG_HEAD HEAD | grep -E 'package.json|pnpm-lock.yaml|pnpm-workspace.yaml' > /dev/null; then
    PACKAGE_FILES_UPDATED=true
    break
  fi
done

# If package files were updated, suggest running package installation
if [ "$PACKAGE_FILES_UPDATED" = true ]; then
  echo "📦 Package files were updated. You might want to run 'pnpm install' to update dependencies."
fi

# Check if documentation was updated
DOCS_UPDATED=false
for REF in "$@"; do
  if git diff-tree -r --name-only --no-commit-id ORIG_HEAD HEAD | grep -E 'docs/|README.md|\.md$' > /dev/null; then
    DOCS_UPDATED=true
    break
  fi
done

# If documentation was updated, notify
if [ "$DOCS_UPDATED" = true ]; then
  echo "📚 Documentation was updated. Check out the latest changes!"
fi 