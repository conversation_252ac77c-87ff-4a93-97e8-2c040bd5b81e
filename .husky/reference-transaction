# This hook is called when Git updates references
# It receives "prepared", "committed", or "aborted" as the first argument

# Get the transaction status
STATUS="$1"

# Only act on committed transactions
if [ "$STATUS" = "committed" ]; then
  # Check if any protected branches were updated
  while read -r OLD NEW REF; do
    # Skip if the reference is not a branch
    if ! echo "$REF" | grep -q "^refs/heads/"; then
      continue
    fi
    
    # Extract branch name
    BRANCH=${REF#refs/heads/}
    
    # Check if it's a protected branch
    if [ "$BRANCH" = "main" ] || [ "$BRANCH" = "master" ] || [ "$BRANCH" = "develop" ]; then
      # Log the change to a file for auditing
      echo "$(date '+%Y-%m-%d %H:%M:%S') - $BRANCH updated from $OLD to $NEW by $(git config user.name)" >> .git/branch-updates.log
      
      # Notify about the change
      echo "🔔 Protected branch $BRANCH was updated"
    fi
  done
fi 