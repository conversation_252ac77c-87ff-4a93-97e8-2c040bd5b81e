#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

# 获取最新的远程分支信息
echo "📡 正在同步远程分支信息..."
pnpm run prepush

# 运行全局 TypeScript 类型检查
echo "🔍 正在运行类型检查（这可能需要一些时间）..."
# pnpm run tsc || { echo "❌ 类型检查失败，推送终止。请修复所有类型错误后再尝试推送。"; exit 1; }

# 运行测试
echo "🧪 正在运行测试..."
pnpm run test:ci || { echo "❌ 测试失败，推送终止。请修复所有测试用例后再尝试推送。"; exit 1; }

echo "✅ 所有检查通过，正在推送..."

# 待 code-guard 工具实现后，可以改用：
# echo "🔍 运行分支代码门禁检查..."
# pnpm code-guard branch

# Check for outdated dependencies (optional, comment out if too slow)
# echo "📦 Checking for outdated dependencies..."
# pnpm outdated --depth=0
