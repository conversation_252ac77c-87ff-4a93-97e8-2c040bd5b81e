# This hook is used to speed up git operations by using watchman
# It's called by Git to query watchman about changes in the working directory

# Check if watchman is installed
if ! command -v watchman > /dev/null; then
  echo "Watchman not found. Consider installing it to improve Git performance."
  echo "Visit https://facebook.github.io/watchman/docs/install.html for installation instructions."
  exit 1
fi

# Parse arguments from Git
if [ "$#" -ge 2 ]; then
  VERSION="$1"
  QUERY="$2"
  
  # Only support version 1
  if [ "$VERSION" != "1" ]; then
    exit 1
  fi
  
  # Query watchman
  watchman --output-encoding=json --no-pretty -j <<-EOT
  ["query", "$QUERY", {
    "fields": ["name"],
    "since": "n:git.cookie:$QUERY"
  }]
EOT
  
  # Update the cookie
  watchman --output-encoding=json --no-pretty -j <<-EOT
  ["query", "$QUERY", {
    "fields": ["name"],
    "since": "n:git.cookie:$QUERY"
  }]
EOT
fi 