declare module 'woodpecker-monitor' {
    import type { AxiosResponse } from 'axios';

    export type IErrorType =
        | 'jsError' // JS报错           //自动捕获
        | 'codeError' // 数据异常        //需要在拦截器（axios等）配置
        | 'performance' // 接口性能  //只统计上报不报警，需要在拦截器（axios等
        | 'resource404' // 资源404   //自动捕获 JS css img  404
        | 'httpCatchError' // 请求异常 //需要在拦截器（axios等）配置
        | '404' // 路由404            //spa页面需要在路由进行捕获上报
        | 'serverError' // 服务器异常 //服务端异常上报类型（node 等）
        | 'collectData' // 自定义收集数据 //类似业务埋点，次类型不报警，
        | 'promiseCatch' // promise异常  //自动捕获
        | 'appAbnormal' // 客户端内嵌页异常 //由客户端进行监听上报，实时上报异常的H5页面
        | 'firstScreenPerformance'; // 首屏渲染性能  //配置后可以获取页面的fcp ,fmp ,tti关键数据

    export interface IWoodpeckerParams {
        appKey?: string;
        v?: string | number;
        userId?: string | number;
        resource404?: boolean; // 禁用资源404上报
        resourcesList?: string[]; // 禁用404上报的资源列表
        promiseCatchList?: string[]; // 排除PromiseCatch错误内容
        jsError?: boolean; // 禁用jsError上报
        jsErrorList?: string[]; // 排除jsError
        history?: boolean; // 屏蔽历史记录上报
        autoDesensitization?: boolean; // 上报数据脱敏处理
        unhandledrejection?: boolean; // 禁用promiseCatch上报
    }
    // 自定义上报参数
    interface IWdCustomSendParams {
        appKey?: string;
        errorCode: string;
        errorType: IErrorType;
        sceneType?: string;
        json?: string;
        apiUrl?: string;
        apiParam?: string;
        userId?: string;
        v?: string;
        row?: number;
        column?: number;
        url?: string;
    }

    // 性能监控 参数
    interface PerformanceParams {
        appKey?: string;
        runTime?: number;
        response?: AxiosResponse;
        apiList?: string[];
        traceId?: string;
    }

    export function Woodpecker(params: IWoodpeckerParams): void;
    export function wdCustomSend(params: IWdCustomSendParams): void;
    export function requestPerformance(params: PerformanceParams): void;
    export function filterData(params: string[], str: string): void;
    export function filterTools(params: string[], str: string): void;

    export const errorStackParser: {
        parse: (error: Error) => any[];
    };

    export const utils: {
        getTraceID: () => string;
    };
}
