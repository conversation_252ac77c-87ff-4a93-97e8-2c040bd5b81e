export interface ICameraMonitorRule {
    // 替考检测
    substituteExam: number;
    substituteExamLimit: number;
    substituteExamLimitTimeWindow: number;
    // 多人脸检测
    multipleFaces: number;
    multipleFacesLimit: number;
    multipleFacesLimitTimeWindow: number;
    // 离开检测
    leaveSeat: number;
    leaveSeatLimit: number;
    leaveSeatLimitTimeWindow: number;
    // 低头检测
    lowerHead: number;
    lowerHeadLimit: number;
    lowerHeadLimitTimeWindow: number;
    // 左右张望检测
    lookAround: number;
    lookAroundLimit: number;
    lookAroundLimitTimeWindow: number;
}

export interface IStepItem {
    label: string;
    type: string;
    cb?: (str: string) => void;
    component: any;
    nextClickable: boolean;
    check?: () => boolean;
    status: 'wait' | 'process' | 'finish' | 'danger'; // 当前步骤的状态
}

export interface IExamDebugInfo {
    canDebug: boolean;
    startTime: string;
    endTime: string;
    prepareCheck: 0 | 1; // 考前准备 0无 1有
}
export enum ExamStatusEnum {
    调试已结束,
    未开始 = 112,
    已结束 = 113,
    提前入场 = 114,
    禁止入场 = 115,
    即时模式下前一场未交卷且未结束 = 116,
    已交卷,
    兼容拦截,
}
