/* Cookie */
export const Cookie = {
    get(name: string) {
        const reg = new RegExp(`(^| )${name}=([^;]*)(;|$)`);

        const arr = document.cookie.match(reg);
        if (arr && arr[2]) {
            return decodeURIComponent(arr[2]);
        }
        return null;
    },
    getObj() {
        const arr = document.cookie.split(';');
        let jsonStr = '{';
        for (let i = 0; i < arr.length; i++) {
            if (arr[i]) {
                const cookie = arr[i]!.split('=');
                if (cookie[0] && cookie[1]) {
                    jsonStr += `"${cookie[0].replace(/\s+/g, '')}":"${decodeURIComponent(cookie[1])}",`;
                }
            }
        }
        jsonStr = jsonStr.slice(0, -1);
        jsonStr += '}';
        return JSON.parse(jsonStr);
    },
    set(name: string, value: any, time?: string | number | Date, domain?: any, path?: any) {
        let str = `${name}=${encodeURIComponent(value)}`;
        if (time) {
            const date = new Date(time).toUTCString();
            str += `;expires=${date}`;
        }
        str = domain ? `${str};domain=${domain}` : str;
        str = path ? `${str};path=${path}` : str;

        document.cookie = str;
    },
    // 删除一个cookie，必须域名和path都跟已有的cookie相同
    del(name: any, domain: any, path: any) {
        const date = new Date('1970-01-01');
        let str = `${name}=null;expires=${date.toUTCString()}`;
        str = domain ? `${str};domain=${domain}` : str;
        str = path ? `${str};path=${path}` : str;
        document.cookie = str;
    },
};
