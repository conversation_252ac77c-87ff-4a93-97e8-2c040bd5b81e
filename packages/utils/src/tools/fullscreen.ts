type RFSMethodName = 'webkitRequestFullScreen' | 'requestFullscreen' | 'msRequestFullscreen' | 'mozRequestFullScreen';
type EFSMethodName = 'webkitExitFullscreen' | 'msExitFullscreen' | 'mozCancelFullScreen' | 'exitFullscreen';
type FSEPropName = 'webkitFullscreenElement' | 'msFullscreenElement' | 'mozFullScreenElement' | 'fullscreenElement';
type ONFSCPropName = 'onfullscreenchange' | 'onwebkitfullscreenchange' | 'onmozfullscreenchange' | 'MSFullscreenChange';

// 扩展 HTMLElement 和 Document 接口以支持不同浏览器的全屏 API
interface ExtendedHTMLElement extends HTMLElement {
    webkitRequestFullScreen?: () => Promise<void>;
    msRequestFullscreen?: () => Promise<void>;
    mozRequestFullScreen?: () => Promise<void>;
}

interface ExtendedDocument extends Document {
    webkitExitFullscreen?: () => Promise<void>;
    msExitFullscreen?: () => Promise<void>;
    mozCancelFullScreen?: () => Promise<void>;
    webkitFullscreenElement?: Element | null;
    msFullscreenElement?: Element | null;
    mozFullScreenElement?: Element | null;
}

// 全屏 API 配置
let fullscreenConfig: {
    DOC_EL: ExtendedHTMLElement;
    DOC: ExtendedDocument;
    headEl: HTMLHeadElement | null;
    styleEl: HTMLStyleElement;
    TYPE_REQUEST_FULL_SCREEN: RFSMethodName;
    TYPE_EXIT_FULL_SCREEN: EFSMethodName;
    TYPE_FULL_SCREEN_ELEMENT: FSEPropName;
    TYPE_ON_FULL_SCREEN_CHANGE: ONFSCPropName;
} | null = null;

// 初始化状态标记，避免重复函数调用
let isInitialized = false;

/**
 * 初始化全屏 API 配置
 * 只在第一次使用时执行兼容性检测
 */
function initFullscreenConfig() {
    if (isInitialized) {
        return fullscreenConfig!;
    }

    /**
     * caniuse
     * https://caniuse.com/#search=Fullscreen
     * 参考 MDN, 并不确定是否有o前缀的, 暂时不加入
     * https://developer.mozilla.org/zh-CN/docs/Web/API/Element/requestFullscreen
     * 各个浏览器
     * https://www.wikimoe.com/?post=82
     */
    const DOC_EL = document.documentElement as ExtendedHTMLElement;
    const DOC = document as ExtendedDocument;
    let headEl = DOC_EL.querySelector('head');
    const styleEl = document.createElement('style');
    let TYPE_REQUEST_FULL_SCREEN: RFSMethodName = 'requestFullscreen';
    let TYPE_EXIT_FULL_SCREEN: EFSMethodName = 'exitFullscreen';
    let TYPE_FULL_SCREEN_ELEMENT: FSEPropName = 'fullscreenElement';
    let TYPE_ON_FULL_SCREEN_CHANGE: ONFSCPropName = 'onfullscreenchange';

    if (`webkitRequestFullScreen` in DOC_EL) {
        TYPE_REQUEST_FULL_SCREEN = 'webkitRequestFullScreen';
        TYPE_EXIT_FULL_SCREEN = 'webkitExitFullscreen';
        TYPE_FULL_SCREEN_ELEMENT = 'webkitFullscreenElement';
        TYPE_ON_FULL_SCREEN_CHANGE = 'onwebkitfullscreenchange';
    } else if (`msRequestFullscreen` in DOC_EL) {
        TYPE_REQUEST_FULL_SCREEN = 'msRequestFullscreen';
        TYPE_EXIT_FULL_SCREEN = 'msExitFullscreen';
        TYPE_FULL_SCREEN_ELEMENT = 'msFullscreenElement';
        TYPE_ON_FULL_SCREEN_CHANGE = 'MSFullscreenChange';
    } else if (`mozRequestFullScreen` in DOC_EL) {
        TYPE_REQUEST_FULL_SCREEN = 'mozRequestFullScreen';
        TYPE_EXIT_FULL_SCREEN = 'mozCancelFullScreen';
        TYPE_FULL_SCREEN_ELEMENT = 'mozFullScreenElement';
        TYPE_ON_FULL_SCREEN_CHANGE = 'onmozfullscreenchange';
    } else if (!(`requestFullscreen` in DOC_EL)) {
        console.warn('当前浏览器不支持Fullscreen API !');
    }

    fullscreenConfig = {
        DOC_EL,
        DOC,
        headEl,
        styleEl,
        TYPE_REQUEST_FULL_SCREEN,
        TYPE_EXIT_FULL_SCREEN,
        TYPE_FULL_SCREEN_ELEMENT,
        TYPE_ON_FULL_SCREEN_CHANGE,
    };

    isInitialized = true;
    return fullscreenConfig;
}

/**
 * 如果传入的不是HTMLElement,
 * 比如是EventTarget
 * 那么返回document.documentElement
 * @param el 目标元素
 * @returns 目标元素或者document.documentElement
 */
function getCurrentElement(el?: HTMLElement): ExtendedHTMLElement {
    const config = initFullscreenConfig();
    return (el instanceof HTMLElement ? el : config.DOC_EL) as ExtendedHTMLElement;
}

/**
 * 启用全屏
 * @param  元素
 * @param   选项
 */
export function beFull(el?: HTMLElement, backgroundColor?: string): Promise<void> {
    const config = initFullscreenConfig();

    if (backgroundColor) {
        if (config.headEl === null) {
            config.headEl = document.createElement('head');
        }
        config.styleEl.innerHTML = `:fullscreen{background-color:${backgroundColor};}`;
        config.headEl.appendChild(config.styleEl);
    }
    const element = getCurrentElement(el);
    const method = element[config.TYPE_REQUEST_FULL_SCREEN];
    if (method) {
        return method.call(element);
    }
    return Promise.reject(new Error('Fullscreen API not supported'));
}

/**
 * 退出全屏
 */
export function exitFull(): Promise<void> {
    const config = initFullscreenConfig();

    if (config.DOC_EL.contains(config.styleEl)) {
        config.headEl?.removeChild(config.styleEl);
    }
    const method = config.DOC[config.TYPE_EXIT_FULL_SCREEN];
    if (method) {
        return method.call(config.DOC);
    }
    return Promise.reject(new Error('Exit fullscreen API not supported'));
}

/**
 * 元素是否全屏
 * @param 目标元素
 */
export function isFull(el?: HTMLElement): boolean {
    if (!isInitialized) {
        initFullscreenConfig();
    }

    const currentElement = el instanceof HTMLElement ? el : fullscreenConfig!.DOC_EL;
    const fullscreenElement = fullscreenConfig!.DOC[fullscreenConfig!.TYPE_FULL_SCREEN_ELEMENT];
    return currentElement === fullscreenElement;
}

/**
 * 切换全屏/关闭
 * @param  目标元素
 * @returns Promise
 */
export function toggleFull(el?: HTMLElement, backgroundColor?: string): boolean {
    if (isFull(el)) {
        exitFull();
        return false;
    } else {
        beFull(el, backgroundColor);
        return true;
    }
}
