import { describe, it, expect } from 'vitest';

/**
 * 以下是从 utils 包复制的测试目标函数，但不从 index.ts 导入
 * 这样可以避免加载整个模块时可能出现的问题
 */

// 数字补0
function addZero(val: number): string {
    if (Number.isNaN(val)) {
        return `${val}`;
    }
    return val > 9 ? `${val}` : `0${val}`;
}

// 日期格式化
function formatDate(val: any, formatStr = 'yyyy-MM-dd') {
    if (!val) {
        return '';
    }
    let date = val;
    if (typeof val === 'string') {
        // 字符类型
        date = new Date(val.replace(/-/g, '/'));
    } else if (typeof val === 'number') {
        // 时间戳
        date = new Date(val);
    }

    let str = formatStr;
    str = str.replace(/yyyy|YYYY/, `${date.getFullYear()}`);
    str = str.replace(/MM/, addZero(date.getMonth() + 1));
    str = str.replace(/M/g, `${date.getMonth() + 1}`);
    str = str.replace(/dd|DD/, addZero(date.getDate()));
    str = str.replace(/d|D/g, `${date.getDate()}`);
    str = str.replace(/hh|HH/, addZero(date.getHours()));
    str = str.replace(/h|H/g, `${date.getHours()}`);
    str = str.replace(/mm/, addZero(date.getMinutes()));
    str = str.replace(/m/g, `${date.getMinutes()}`);
    str = str.replace(/ss|SS/, addZero(date.getSeconds()));
    str = str.replace(/s|S/g, `${date.getSeconds()}`);
    return str;
}

// 获取UUID (简化版，不使用任何浏览器 API)
function getUUID() {
    const pattern = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx';
    return pattern.replace(/[xy]/g, (c) => {
        const r = (Math.random() * 16) | 0;
        const v = c === 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
    });
}

describe('工具函数测试', () => {
    // 测试数字补零函数
    describe('addZero', () => {
        it('应该为个位数添加前导零', () => {
            expect(addZero(5)).toBe('05');
        });

        it('不应该为十位数以上添加前导零', () => {
            expect(addZero(10)).toBe('10');
        });

        it('应该处理非数字输入', () => {
            expect(addZero(NaN)).toBe('NaN');
        });
    });

    // 测试日期格式化函数
    describe('formatDate', () => {
        it('应该正确格式化日期对象', () => {
            const date = new Date(2023, 0, 15); // 2023-01-15
            expect(formatDate(date, 'yyyy-MM-dd')).toBe('2023-01-15');
        });

        it('应该正确格式化日期字符串', () => {
            expect(formatDate('2023/01/15', 'yyyy/MM/dd')).toBe('2023/01/15');
        });

        it('应该正确格式化时间戳', () => {
            const timestamp = new Date(2023, 0, 15).getTime();
            expect(formatDate(timestamp, 'yyyy-MM-dd')).toBe('2023-01-15');
        });

        it('应该对空值返回空字符串', () => {
            expect(formatDate(null)).toBe('');
        });
    });

    // 测试UUID生成函数
    describe('getUUID', () => {
        it('应该生成有效的UUID格式', () => {
            const uuid = getUUID();
            expect(uuid).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/);
        });

        it('应该每次生成不同的UUID', () => {
            const uuid1 = getUUID();
            const uuid2 = getUUID();
            expect(uuid1).not.toBe(uuid2);
        });
    });
});
