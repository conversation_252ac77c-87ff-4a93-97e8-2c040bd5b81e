import { beFull, exitFull, isFull } from './fullscreen';

/**
 * 增强的全屏管理工具，专门处理屏幕共享场景下的全屏功能
 */

interface FullscreenOptions {
    /** 是否在屏幕共享模式下 */
    isScreenSharing?: boolean;
    /** 屏幕共享类型 */
    shareType?: 'tab' | 'window' | 'screen';
    /** 延迟时间（毫秒） */
    delay?: number;
    /** 最大重试次数 */
    maxRetries?: number;
}

/**
 * 屏幕共享感知的全屏请求
 * @param el 目标元素
 * @param options 选项
 */
export async function requestFullscreenWithScreenShare(
    el?: HTMLElement,
    options: FullscreenOptions = {}
): Promise<boolean> {
    const {
        isScreenSharing = false,
        shareType = 'screen',
        delay = 0,
        maxRetries = 3
    } = options;

    // 如果已经全屏，直接返回成功
    if (isFull(el)) {
        return true;
    }

    // 如果有延迟，先等待
    if (delay > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
    }

    let retries = 0;
    while (retries < maxRetries) {
        try {
            if (isScreenSharing && shareType === 'tab') {
                // 标签页共享模式下的特殊处理
                return await requestFullscreenForTabShare(el);
            } else {
                // 正常全屏请求
                await beFull(el);
                return true;
            }
        } catch (error) {
            console.warn(`Fullscreen request failed (attempt ${retries + 1}):`, error);
            retries++;
            
            if (retries < maxRetries) {
                // 等待一段时间后重试
                await new Promise(resolve => setTimeout(resolve, 1000 * retries));
            }
        }
    }

    return false;
}

/**
 * 标签页共享模式下的全屏请求
 * @param el 目标元素
 */
async function requestFullscreenForTabShare(el?: HTMLElement): Promise<boolean> {
    return new Promise((resolve) => {
        let resolved = false;
        
        const resolveOnce = (success: boolean) => {
            if (!resolved) {
                resolved = true;
                resolve(success);
            }
        };

        // 监听全屏状态变化
        const handleFullscreenChange = () => {
            if (isFull(el)) {
                resolveOnce(true);
            }
        };

        // 添加全屏状态监听
        document.addEventListener('fullscreenchange', handleFullscreenChange);
        document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
        document.addEventListener('mozfullscreenchange', handleFullscreenChange);

        // 创建用户交互提示
        const createInteractionPrompt = () => {
            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                font-family: Arial, sans-serif;
            `;

            const message = document.createElement('div');
            message.style.cssText = `
                font-size: 18px;
                margin-bottom: 20px;
                text-align: center;
                max-width: 600px;
                line-height: 1.5;
            `;
            message.textContent = '为了更好的考试体验，请点击此处进入全屏模式';

            const button = document.createElement('button');
            button.style.cssText = `
                padding: 12px 24px;
                font-size: 16px;
                background: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                transition: background 0.3s;
            `;
            button.textContent = '进入全屏';

            button.addEventListener('mouseenter', () => {
                button.style.background = '#0056b3';
            });

            button.addEventListener('mouseleave', () => {
                button.style.background = '#007bff';
            });

            const handleClick = async () => {
                try {
                    await beFull(el);
                    document.body.removeChild(overlay);
                } catch (error) {
                    console.warn('Failed to enter fullscreen:', error);
                    resolveOnce(false);
                }
            };

            button.addEventListener('click', handleClick);
            overlay.addEventListener('click', handleClick);

            overlay.appendChild(message);
            overlay.appendChild(button);
            document.body.appendChild(overlay);

            // 10秒后自动移除提示
            setTimeout(() => {
                if (document.body.contains(overlay)) {
                    document.body.removeChild(overlay);
                    resolveOnce(false);
                }
            }, 10000);
        };

        // 尝试直接请求全屏
        beFull(el).then(() => {
            // 成功则不需要显示提示
        }).catch(() => {
            // 失败则显示用户交互提示
            createInteractionPrompt();
        });

        // 清理函数
        const cleanup = () => {
            document.removeEventListener('fullscreenchange', handleFullscreenChange);
            document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
            document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
        };

        // 30秒超时
        setTimeout(() => {
            cleanup();
            resolveOnce(false);
        }, 30000);
    });
}

/**
 * 安全的退出全屏
 */
export async function safeExitFullscreen(): Promise<boolean> {
    try {
        if (isFull()) {
            await exitFull();
            return true;
        }
        return true;
    } catch (error) {
        console.warn('Failed to exit fullscreen:', error);
        return false;
    }
}

/**
 * 检测屏幕共享类型
 * @param stream 媒体流
 */
export function detectScreenShareType(stream: MediaStream): 'tab' | 'window' | 'screen' | 'unknown' {
    try {
        const videoTrack = stream.getVideoTracks()[0];
        if (!videoTrack) return 'unknown';

        const settings = videoTrack.getSettings();
        
        // 根据displaySurface判断
        if (settings.displaySurface) {
            switch (settings.displaySurface) {
                case 'browser':
                    return 'tab';
                case 'window':
                    return 'window';
                case 'monitor':
                    return 'screen';
                default:
                    return 'unknown';
            }
        }

        // 备用检测方法：根据尺寸判断
        if (settings.width && settings.height) {
            const screenRatio = settings.width / settings.height;
            const isSmallSize = settings.width < screen.width * 0.8 || settings.height < screen.height * 0.8;
            
            if (isSmallSize) {
                return screenRatio > 1.5 ? 'tab' : 'window';
            } else {
                return 'screen';
            }
        }

        return 'unknown';
    } catch (error) {
        console.warn('Failed to detect screen share type:', error);
        return 'unknown';
    }
}
