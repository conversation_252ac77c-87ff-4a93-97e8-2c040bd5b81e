export function formatNumber2Time(value: any) {
    let secondTime: any = Number.parseInt(value); // 秒
    let minuteTime: any = 0; // 分
    let hourTime: any = 0; // 小时
    if (secondTime >= 60) {
        minuteTime = Number.parseInt(String(secondTime / 60));
        secondTime = Number.parseInt(String(secondTime % 60));
        if (minuteTime >= 60) {
            hourTime = Number.parseInt(String(minuteTime / 60));
            minuteTime = Number.parseInt(String(minuteTime % 60));
        }
    }
    let time = `${hourTime >= 10 ? hourTime : `0${hourTime}`}:${minuteTime >= 10 ? minuteTime : `0${minuteTime}`}:${secondTime >= 10 ? secondTime : `0${secondTime}`}`;

    // if (minuteTime > 0) {
    //     time = '' + parseInt(minuteTime) + ':' + time;
    // }
    // if (hourTime > 0) {
    //     time = '' + parseInt(hourTime) + ':' + time;
    // }
    return time;
}
