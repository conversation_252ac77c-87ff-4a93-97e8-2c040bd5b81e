/* eslint-disable */
import * as $protobuf from 'protobufjs';
import Long = require('long');
/** Properties of an ExamUser. */
export interface IExamUser {
    /** ExamUser userId */
    userId: string;

    /** ExamUser avatar */
    avatar?: string | null;

    /** ExamUser roleType */
    roleType: ExamUser.RoleType;

    /** ExamUser userName */
    userName?: string | null;
}

/** Represents an ExamUser. */
export class ExamUser implements IExamUser {
    /**
     * Constructs a new ExamUser.
     * @param [properties] Properties to set
     */
    constructor(properties?: IExamUser);

    /** ExamUser userId. */
    public userId: string;

    /** ExamUser avatar. */
    public avatar: string;

    /** ExamUser roleType. */
    public roleType: ExamUser.RoleType;

    /** ExamUser userName. */
    public userName: string;

    /**
     * Creates a new ExamUser instance using the specified properties.
     * @param [properties] Properties to set
     * @returns ExamUser instance
     */
    public static create(properties?: IExamUser): ExamUser;

    /**
     * Encodes the specified ExamUser message. Does not implicitly {@link ExamUser.verify|verify} messages.
     * @param message ExamUser message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IExamUser, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified ExamUser message, length delimited. Does not implicitly {@link ExamUser.verify|verify} messages.
     * @param message ExamUser message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IExamUser, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes an ExamUser message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns ExamUser
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: $protobuf.Reader | Uint8Array, length?: number): ExamUser;

    /**
     * Decodes an ExamUser message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns ExamUser
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: $protobuf.Reader | Uint8Array): ExamUser;

    /**
     * Verifies an ExamUser message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): string | null;

    /**
     * Creates an ExamUser message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns ExamUser
     */
    public static fromObject(object: { [k: string]: any }): ExamUser;

    /**
     * Creates a plain object from an ExamUser message. Also converts values to other types if specified.
     * @param message ExamUser
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: ExamUser, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this ExamUser to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for ExamUser
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

 
export namespace ExamUser {
    /** RoleType enum. */
    enum RoleType {
        EXAMINEE = 1,
        EXAMINER = 2,
        SYSTEM = 0,
        CORP_ADMIN_USER = 3
    }
}

/** Properties of an ExamMessage. */
export interface IExamMessage {
    /** ExamMessage messageType */
    messageType: ExamMessage.MessageType;

    /** ExamMessage from */
    from: IExamUser;

    /** ExamMessage to */
    to: IExamUser;

    /** ExamMessage text */
    text?: string | null;

    /** ExamMessage examId */
    examId: string;

    /** ExamMessage talkTime */
    talkTime?: string | null;

    /** ExamMessage unreadCount */
    unreadCount?: number | null;

    /** ExamMessage groupId */
    groupId?: string | null;

    /** ExamMessage messageId */
    messageId?: string | null;
}

/** Represents an ExamMessage. */
export class ExamMessage implements IExamMessage {
    /**
     * Constructs a new ExamMessage.
     * @param [properties] Properties to set
     */
    constructor(properties?: IExamMessage);

    /** ExamMessage messageType. */
    public messageType: ExamMessage.MessageType;

    /** ExamMessage from. */
    public from: IExamUser;

    /** ExamMessage to. */
    public to: IExamUser;

    /** ExamMessage text. */
    public text: string;

    /** ExamMessage examId. */
    public examId: string;

    /** ExamMessage talkTime. */
    public talkTime: string;

    /** ExamMessage unreadCount. */
    public unreadCount: number;

    /** ExamMessage groupId. */
    public groupId: string;

    /** ExamMessage messageId. */
    public messageId: string;

    /**
     * Creates a new ExamMessage instance using the specified properties.
     * @param [properties] Properties to set
     * @returns ExamMessage instance
     */
    public static create(properties?: IExamMessage): ExamMessage;

    /**
     * Encodes the specified ExamMessage message. Does not implicitly {@link ExamMessage.verify|verify} messages.
     * @param message ExamMessage message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IExamMessage, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified ExamMessage message, length delimited. Does not implicitly {@link ExamMessage.verify|verify} messages.
     * @param message ExamMessage message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IExamMessage, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes an ExamMessage message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns ExamMessage
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: $protobuf.Reader | Uint8Array, length?: number): ExamMessage;

    /**
     * Decodes an ExamMessage message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns ExamMessage
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: $protobuf.Reader | Uint8Array): ExamMessage;

    /**
     * Verifies an ExamMessage message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): string | null;

    /**
     * Creates an ExamMessage message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns ExamMessage
     */
    public static fromObject(object: { [k: string]: any }): ExamMessage;

    /**
     * Creates a plain object from an ExamMessage message. Also converts values to other types if specified.
     * @param message ExamMessage
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: ExamMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this ExamMessage to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for ExamMessage
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

export namespace ExamMessage {
    /** MessageType enum. */
    enum MessageType {
        TEXT = 1,
        PICTURE = 2,
        VOICE = 3
    }
}

/** Properties of an ExamMessageRead. */
export interface IExamMessageRead {
    /** ExamMessageRead from */
    from: IExamUser;

    /** ExamMessageRead messageId */
    messageId: string;
}

/** Represents an ExamMessageRead. */
export class ExamMessageRead implements IExamMessageRead {
    /**
     * Constructs a new ExamMessageRead.
     * @param [properties] Properties to set
     */
    constructor(properties?: IExamMessageRead);

    /** ExamMessageRead from. */
    public from: IExamUser;

    /** ExamMessageRead messageId. */
    public messageId: string;

    /**
     * Creates a new ExamMessageRead instance using the specified properties.
     * @param [properties] Properties to set
     * @returns ExamMessageRead instance
     */
    public static create(properties?: IExamMessageRead): ExamMessageRead;

    /**
     * Encodes the specified ExamMessageRead message. Does not implicitly {@link ExamMessageRead.verify|verify} messages.
     * @param message ExamMessageRead message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IExamMessageRead, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified ExamMessageRead message, length delimited. Does not implicitly {@link ExamMessageRead.verify|verify} messages.
     * @param message ExamMessageRead message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IExamMessageRead, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes an ExamMessageRead message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns ExamMessageRead
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: $protobuf.Reader | Uint8Array, length?: number): ExamMessageRead;

    /**
     * Decodes an ExamMessageRead message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns ExamMessageRead
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: $protobuf.Reader | Uint8Array): ExamMessageRead;

    /**
     * Verifies an ExamMessageRead message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): string | null;

    /**
     * Creates an ExamMessageRead message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns ExamMessageRead
     */
    public static fromObject(object: { [k: string]: any }): ExamMessageRead;

    /**
     * Creates a plain object from an ExamMessageRead message. Also converts values to other types if specified.
     * @param message ExamMessageRead
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: ExamMessageRead, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this ExamMessageRead to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for ExamMessageRead
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a ZpExamProtocol. */
export interface IZpExamProtocol {
    /** ZpExamProtocol proType */
    proType: ZpExamProtocol.ProType;

    /** ZpExamProtocol messages */
    messages?: IExamMessage[] | null;

    /** ZpExamProtocol messageRead */
    messageRead?: IExamMessageRead[] | null;
}

/** Represents a ZpExamProtocol. */
export class ZpExamProtocol implements IZpExamProtocol {
    /**
     * Constructs a new ZpExamProtocol.
     * @param [properties] Properties to set
     */
    constructor(properties?: IZpExamProtocol);

    /** ZpExamProtocol proType. */
    public proType: ZpExamProtocol.ProType;

    /** ZpExamProtocol messages. */
    public messages: IExamMessage[];

    /** ZpExamProtocol messageRead. */
    public messageRead: IExamMessageRead[];

    /**
     * Creates a new ZpExamProtocol instance using the specified properties.
     * @param [properties] Properties to set
     * @returns ZpExamProtocol instance
     */
    public static create(properties?: IZpExamProtocol): ZpExamProtocol;

    /**
     * Encodes the specified ZpExamProtocol message. Does not implicitly {@link ZpExamProtocol.verify|verify} messages.
     * @param message ZpExamProtocol message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IZpExamProtocol, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified ZpExamProtocol message, length delimited. Does not implicitly {@link ZpExamProtocol.verify|verify} messages.
     * @param message ZpExamProtocol message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IZpExamProtocol, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a ZpExamProtocol message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns ZpExamProtocol
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: $protobuf.Reader | Uint8Array, length?: number): ZpExamProtocol;

    /**
     * Decodes a ZpExamProtocol message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns ZpExamProtocol
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: $protobuf.Reader | Uint8Array): ZpExamProtocol;

    /**
     * Verifies a ZpExamProtocol message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): string | null;

    /**
     * Creates a ZpExamProtocol message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns ZpExamProtocol
     */
    public static fromObject(object: { [k: string]: any }): ZpExamProtocol;

    /**
     * Creates a plain object from a ZpExamProtocol message. Also converts values to other types if specified.
     * @param message ZpExamProtocol
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: ZpExamProtocol, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this ZpExamProtocol to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for ZpExamProtocol
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

export namespace ZpExamProtocol {
    /** ProType enum. */
    enum ProType {
        IM = 1,
        EXAM_COMMIT = 2,
        LOGIN_CONFLICT = 3,
        NOTICE = 4,
        READ_MESSAGE = 5,
        EXAMINEE_COMMIT = 6,
        SEND_EXAMINEE_WARNING = 7,
        SEND_IDENTITY_AUDIT_RESULT = 8,
        SEND_TO_AUDIT_IDENTITY_AUDIT_INFO = 9,
        HUMMER_AUTH_NOTICE = 10,
        EXAMINEE_LEAVE_SEAT = 11,
        EXAMINEE_LOWER_HEAD = 12,
        EXAMINEE_LOOK_AROUND = 13,
        EXAMINEE_MULTIPLE_FACES = 14,
        EXAMINEE_SUBSTITUTE_EXAM = 15,
        V_STREAM_CALLBACK_NOTICE = 16
    }
}

