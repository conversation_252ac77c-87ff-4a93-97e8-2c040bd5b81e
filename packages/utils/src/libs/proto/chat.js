/* eslint-disable */

// var $protobuf = require("protobufjs/minimal");
import $protobuf from 'protobufjs/minimal';
// Common aliases
var $Reader = $protobuf.Reader, $Writer = $protobuf.Writer, $util = $protobuf.util;

// Exported root namespace
var $root = $protobuf.roots["default"] || ($protobuf.roots["default"] = {});

$root.ExamUser = (function () {

    /**
     * Properties of an ExamUser.
     * @exports IExamUser
     * @interface IExamUser
     * @property {string} userId ExamUser userId
     * @property {string|null} [avatar] ExamUser avatar
     * @property {ExamUser.RoleType} roleType ExamUser roleType
     * @property {string|null} [userName] ExamUser userName
     */

    /**
     * Constructs a new ExamUser.
     * @exports ExamUser
     * @classdesc Represents an ExamUser.
     * @implements IExamUser
     * @constructor
     * @param {IExamUser=} [properties] Properties to set
     */
    function ExamUser(properties) {
        if (properties)
            for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * ExamUser userId.
     * @member {string} userId
     * @memberof ExamUser
     * @instance
     */
    ExamUser.prototype.userId = "";

    /**
     * ExamUser avatar.
     * @member {string} avatar
     * @memberof ExamUser
     * @instance
     */
    ExamUser.prototype.avatar = "";

    /**
     * ExamUser roleType.
     * @member {ExamUser.RoleType} roleType
     * @memberof ExamUser
     * @instance
     */
    ExamUser.prototype.roleType = 1;

    /**
     * ExamUser userName.
     * @member {string} userName
     * @memberof ExamUser
     * @instance
     */
    ExamUser.prototype.userName = "";

    /**
     * Creates a new ExamUser instance using the specified properties.
     * @function create
     * @memberof ExamUser
     * @static
     * @param {IExamUser=} [properties] Properties to set
     * @returns {ExamUser} ExamUser instance
     */
    ExamUser.create = function create(properties) {
        return new ExamUser(properties);
    };

    /**
     * Encodes the specified ExamUser message. Does not implicitly {@link ExamUser.verify|verify} messages.
     * @function encode
     * @memberof ExamUser
     * @static
     * @param {IExamUser} message ExamUser message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ExamUser.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        writer.uint32(/* id 1, wireType 2 =*/10).string(message.userId);
        if (message.avatar != null && Object.hasOwnProperty.call(message, "avatar"))
            writer.uint32(/* id 2, wireType 2 =*/18).string(message.avatar);
        writer.uint32(/* id 3, wireType 0 =*/24).int32(message.roleType);
        if (message.userName != null && Object.hasOwnProperty.call(message, "userName"))
            writer.uint32(/* id 4, wireType 2 =*/34).string(message.userName);
        return writer;
    };

    /**
     * Encodes the specified ExamUser message, length delimited. Does not implicitly {@link ExamUser.verify|verify} messages.
     * @function encodeDelimited
     * @memberof ExamUser
     * @static
     * @param {IExamUser} message ExamUser message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ExamUser.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes an ExamUser message from the specified reader or buffer.
     * @function decode
     * @memberof ExamUser
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {ExamUser} ExamUser
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ExamUser.decode = function decode(reader, length) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        var end = length === undefined ? reader.len : reader.pos + length, message = new $root.ExamUser();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    message.userId = reader.string();
                    break;
                }
                case 2: {
                    message.avatar = reader.string();
                    break;
                }
                case 3: {
                    message.roleType = reader.int32();
                    break;
                }
                case 4: {
                    message.userName = reader.string();
                    break;
                }
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        if (!message.hasOwnProperty("userId"))
            throw $util.ProtocolError("missing required 'userId'", { instance: message });
        if (!message.hasOwnProperty("roleType"))
            throw $util.ProtocolError("missing required 'roleType'", { instance: message });
        return message;
    };

    /**
     * Decodes an ExamUser message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof ExamUser
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {ExamUser} ExamUser
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ExamUser.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies an ExamUser message.
     * @function verify
     * @memberof ExamUser
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    ExamUser.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (!$util.isString(message.userId))
            return "userId: string expected";
        if (message.avatar != null && message.hasOwnProperty("avatar"))
            if (!$util.isString(message.avatar))
                return "avatar: string expected";
        switch (message.roleType) {
            default:
                return "roleType: enum value expected";
            case 1:
            case 2:
            case 0:
            case 3:
                break;
        }
        if (message.userName != null && message.hasOwnProperty("userName"))
            if (!$util.isString(message.userName))
                return "userName: string expected";
        return null;
    };

    /**
     * Creates an ExamUser message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof ExamUser
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {ExamUser} ExamUser
     */
    ExamUser.fromObject = function fromObject(object) {
        if (object instanceof $root.ExamUser)
            return object;
        var message = new $root.ExamUser();
        if (object.userId != null)
            message.userId = String(object.userId);
        if (object.avatar != null)
            message.avatar = String(object.avatar);
        switch (object.roleType) {
            default:
                if (typeof object.roleType === "number") {
                    message.roleType = object.roleType;
                    break;
                }
                break;
            case "EXAMINEE":
            case 1:
                message.roleType = 1;
                break;
            case "EXAMINER":
            case 2:
                message.roleType = 2;
                break;
            case "SYSTEM":
            case 0:
                message.roleType = 0;
                break;
            case "CORP_ADMIN_USER":
            case 3:
                message.roleType = 3;
                break;
        }
        if (object.userName != null)
            message.userName = String(object.userName);
        return message;
    };

    /**
     * Creates a plain object from an ExamUser message. Also converts values to other types if specified.
     * @function toObject
     * @memberof ExamUser
     * @static
     * @param {ExamUser} message ExamUser
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    ExamUser.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        var object = {};
        if (options.defaults) {
            object.userId = "";
            object.avatar = "";
            object.roleType = options.enums === String ? "EXAMINEE" : 1;
            object.userName = "";
        }
        if (message.userId != null && message.hasOwnProperty("userId"))
            object.userId = message.userId;
        if (message.avatar != null && message.hasOwnProperty("avatar"))
            object.avatar = message.avatar;
        if (message.roleType != null && message.hasOwnProperty("roleType"))
            object.roleType = options.enums === String ? $root.ExamUser.RoleType[message.roleType] === undefined ? message.roleType : $root.ExamUser.RoleType[message.roleType] : message.roleType;
        if (message.userName != null && message.hasOwnProperty("userName"))
            object.userName = message.userName;
        return object;
    };

    /**
     * Converts this ExamUser to JSON.
     * @function toJSON
     * @memberof ExamUser
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    ExamUser.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for ExamUser
     * @function getTypeUrl
     * @memberof ExamUser
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    ExamUser.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/ExamUser";
    };

    /**
     * RoleType enum.
     * @name ExamUser.RoleType
     * @enum {number}
     * @property {number} EXAMINEE=1 EXAMINEE value
     * @property {number} EXAMINER=2 EXAMINER value
     * @property {number} SYSTEM=0 SYSTEM value
     * @property {number} CORP_ADMIN_USER=3 CORP_ADMIN_USER value
     */
    ExamUser.RoleType = (function () {
        var valuesById = {}, values = Object.create(valuesById);
        values[valuesById[1] = "EXAMINEE"] = 1;
        values[valuesById[2] = "EXAMINER"] = 2;
        values[valuesById[0] = "SYSTEM"] = 0;
        values[valuesById[3] = "CORP_ADMIN_USER"] = 3;
        return values;
    })();

    return ExamUser;
})();

$root.ExamMessage = (function () {

    /**
     * Properties of an ExamMessage.
     * @exports IExamMessage
     * @interface IExamMessage
     * @property {ExamMessage.MessageType} messageType ExamMessage messageType
     * @property {IExamUser} from ExamMessage from
     * @property {IExamUser} to ExamMessage to
     * @property {string|null} [text] ExamMessage text
     * @property {string} examId ExamMessage examId
     * @property {string|null} [talkTime] ExamMessage talkTime
     * @property {number|null} [unreadCount] ExamMessage unreadCount
     * @property {string|null} [groupId] ExamMessage groupId
     * @property {string|null} [messageId] ExamMessage messageId
     */

    /**
     * Constructs a new ExamMessage.
     * @exports ExamMessage
     * @classdesc Represents an ExamMessage.
     * @implements IExamMessage
     * @constructor
     * @param {IExamMessage=} [properties] Properties to set
     */
    function ExamMessage(properties) {
        if (properties)
            for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * ExamMessage messageType.
     * @member {ExamMessage.MessageType} messageType
     * @memberof ExamMessage
     * @instance
     */
    ExamMessage.prototype.messageType = 1;

    /**
     * ExamMessage from.
     * @member {IExamUser} from
     * @memberof ExamMessage
     * @instance
     */
    ExamMessage.prototype.from = null;

    /**
     * ExamMessage to.
     * @member {IExamUser} to
     * @memberof ExamMessage
     * @instance
     */
    ExamMessage.prototype.to = null;

    /**
     * ExamMessage text.
     * @member {string} text
     * @memberof ExamMessage
     * @instance
     */
    ExamMessage.prototype.text = "";

    /**
     * ExamMessage examId.
     * @member {string} examId
     * @memberof ExamMessage
     * @instance
     */
    ExamMessage.prototype.examId = "";

    /**
     * ExamMessage talkTime.
     * @member {string} talkTime
     * @memberof ExamMessage
     * @instance
     */
    ExamMessage.prototype.talkTime = "";

    /**
     * ExamMessage unreadCount.
     * @member {number} unreadCount
     * @memberof ExamMessage
     * @instance
     */
    ExamMessage.prototype.unreadCount = 0;

    /**
     * ExamMessage groupId.
     * @member {string} groupId
     * @memberof ExamMessage
     * @instance
     */
    ExamMessage.prototype.groupId = "";

    /**
     * ExamMessage messageId.
     * @member {string} messageId
     * @memberof ExamMessage
     * @instance
     */
    ExamMessage.prototype.messageId = "";

    /**
     * Creates a new ExamMessage instance using the specified properties.
     * @function create
     * @memberof ExamMessage
     * @static
     * @param {IExamMessage=} [properties] Properties to set
     * @returns {ExamMessage} ExamMessage instance
     */
    ExamMessage.create = function create(properties) {
        return new ExamMessage(properties);
    };

    /**
     * Encodes the specified ExamMessage message. Does not implicitly {@link ExamMessage.verify|verify} messages.
     * @function encode
     * @memberof ExamMessage
     * @static
     * @param {IExamMessage} message ExamMessage message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ExamMessage.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        writer.uint32(/* id 1, wireType 0 =*/8).int32(message.messageType);
        $root.ExamUser.encode(message.from, writer.uint32(/* id 2, wireType 2 =*/18).fork()).ldelim();
        $root.ExamUser.encode(message.to, writer.uint32(/* id 3, wireType 2 =*/26).fork()).ldelim();
        if (message.text != null && Object.hasOwnProperty.call(message, "text"))
            writer.uint32(/* id 4, wireType 2 =*/34).string(message.text);
        writer.uint32(/* id 5, wireType 2 =*/42).string(message.examId);
        if (message.talkTime != null && Object.hasOwnProperty.call(message, "talkTime"))
            writer.uint32(/* id 6, wireType 2 =*/50).string(message.talkTime);
        if (message.unreadCount != null && Object.hasOwnProperty.call(message, "unreadCount"))
            writer.uint32(/* id 7, wireType 0 =*/56).int32(message.unreadCount);
        if (message.groupId != null && Object.hasOwnProperty.call(message, "groupId"))
            writer.uint32(/* id 8, wireType 2 =*/66).string(message.groupId);
        if (message.messageId != null && Object.hasOwnProperty.call(message, "messageId"))
            writer.uint32(/* id 9, wireType 2 =*/74).string(message.messageId);
        return writer;
    };

    /**
     * Encodes the specified ExamMessage message, length delimited. Does not implicitly {@link ExamMessage.verify|verify} messages.
     * @function encodeDelimited
     * @memberof ExamMessage
     * @static
     * @param {IExamMessage} message ExamMessage message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ExamMessage.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes an ExamMessage message from the specified reader or buffer.
     * @function decode
     * @memberof ExamMessage
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {ExamMessage} ExamMessage
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ExamMessage.decode = function decode(reader, length) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        var end = length === undefined ? reader.len : reader.pos + length, message = new $root.ExamMessage();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    message.messageType = reader.int32();
                    break;
                }
                case 2: {
                    message.from = $root.ExamUser.decode(reader, reader.uint32());
                    break;
                }
                case 3: {
                    message.to = $root.ExamUser.decode(reader, reader.uint32());
                    break;
                }
                case 4: {
                    message.text = reader.string();
                    break;
                }
                case 5: {
                    message.examId = reader.string();
                    break;
                }
                case 6: {
                    message.talkTime = reader.string();
                    break;
                }
                case 7: {
                    message.unreadCount = reader.int32();
                    break;
                }
                case 8: {
                    message.groupId = reader.string();
                    break;
                }
                case 9: {
                    message.messageId = reader.string();
                    break;
                }
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        if (!message.hasOwnProperty("messageType"))
            throw $util.ProtocolError("missing required 'messageType'", { instance: message });
        if (!message.hasOwnProperty("from"))
            throw $util.ProtocolError("missing required 'from'", { instance: message });
        if (!message.hasOwnProperty("to"))
            throw $util.ProtocolError("missing required 'to'", { instance: message });
        if (!message.hasOwnProperty("examId"))
            throw $util.ProtocolError("missing required 'examId'", { instance: message });
        return message;
    };

    /**
     * Decodes an ExamMessage message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof ExamMessage
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {ExamMessage} ExamMessage
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ExamMessage.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies an ExamMessage message.
     * @function verify
     * @memberof ExamMessage
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    ExamMessage.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        switch (message.messageType) {
            default:
                return "messageType: enum value expected";
            case 1:
            case 2:
            case 3:
                break;
        }
        {
            var error = $root.ExamUser.verify(message.from);
            if (error)
                return "from." + error;
        }
        {
            var error = $root.ExamUser.verify(message.to);
            if (error)
                return "to." + error;
        }
        if (message.text != null && message.hasOwnProperty("text"))
            if (!$util.isString(message.text))
                return "text: string expected";
        if (!$util.isString(message.examId))
            return "examId: string expected";
        if (message.talkTime != null && message.hasOwnProperty("talkTime"))
            if (!$util.isString(message.talkTime))
                return "talkTime: string expected";
        if (message.unreadCount != null && message.hasOwnProperty("unreadCount"))
            if (!$util.isInteger(message.unreadCount))
                return "unreadCount: integer expected";
        if (message.groupId != null && message.hasOwnProperty("groupId"))
            if (!$util.isString(message.groupId))
                return "groupId: string expected";
        if (message.messageId != null && message.hasOwnProperty("messageId"))
            if (!$util.isString(message.messageId))
                return "messageId: string expected";
        return null;
    };

    /**
     * Creates an ExamMessage message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof ExamMessage
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {ExamMessage} ExamMessage
     */
    ExamMessage.fromObject = function fromObject(object) {
        if (object instanceof $root.ExamMessage)
            return object;
        var message = new $root.ExamMessage();
        switch (object.messageType) {
            default:
                if (typeof object.messageType === "number") {
                    message.messageType = object.messageType;
                    break;
                }
                break;
            case "TEXT":
            case 1:
                message.messageType = 1;
                break;
            case "PICTURE":
            case 2:
                message.messageType = 2;
                break;
            case "VOICE":
            case 3:
                message.messageType = 3;
                break;
        }
        if (object.from != null) {
            if (typeof object.from !== "object")
                throw TypeError(".ExamMessage.from: object expected");
            message.from = $root.ExamUser.fromObject(object.from);
        }
        if (object.to != null) {
            if (typeof object.to !== "object")
                throw TypeError(".ExamMessage.to: object expected");
            message.to = $root.ExamUser.fromObject(object.to);
        }
        if (object.text != null)
            message.text = String(object.text);
        if (object.examId != null)
            message.examId = String(object.examId);
        if (object.talkTime != null)
            message.talkTime = String(object.talkTime);
        if (object.unreadCount != null)
            message.unreadCount = object.unreadCount | 0;
        if (object.groupId != null)
            message.groupId = String(object.groupId);
        if (object.messageId != null)
            message.messageId = String(object.messageId);
        return message;
    };

    /**
     * Creates a plain object from an ExamMessage message. Also converts values to other types if specified.
     * @function toObject
     * @memberof ExamMessage
     * @static
     * @param {ExamMessage} message ExamMessage
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    ExamMessage.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        var object = {};
        if (options.defaults) {
            object.messageType = options.enums === String ? "TEXT" : 1;
            object.from = null;
            object.to = null;
            object.text = "";
            object.examId = "";
            object.talkTime = "";
            object.unreadCount = 0;
            object.groupId = "";
            object.messageId = "";
        }
        if (message.messageType != null && message.hasOwnProperty("messageType"))
            object.messageType = options.enums === String ? $root.ExamMessage.MessageType[message.messageType] === undefined ? message.messageType : $root.ExamMessage.MessageType[message.messageType] : message.messageType;
        if (message.from != null && message.hasOwnProperty("from"))
            object.from = $root.ExamUser.toObject(message.from, options);
        if (message.to != null && message.hasOwnProperty("to"))
            object.to = $root.ExamUser.toObject(message.to, options);
        if (message.text != null && message.hasOwnProperty("text"))
            object.text = message.text;
        if (message.examId != null && message.hasOwnProperty("examId"))
            object.examId = message.examId;
        if (message.talkTime != null && message.hasOwnProperty("talkTime"))
            object.talkTime = message.talkTime;
        if (message.unreadCount != null && message.hasOwnProperty("unreadCount"))
            object.unreadCount = message.unreadCount;
        if (message.groupId != null && message.hasOwnProperty("groupId"))
            object.groupId = message.groupId;
        if (message.messageId != null && message.hasOwnProperty("messageId"))
            object.messageId = message.messageId;
        return object;
    };

    /**
     * Converts this ExamMessage to JSON.
     * @function toJSON
     * @memberof ExamMessage
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    ExamMessage.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for ExamMessage
     * @function getTypeUrl
     * @memberof ExamMessage
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    ExamMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/ExamMessage";
    };

    /**
     * MessageType enum.
     * @name ExamMessage.MessageType
     * @enum {number}
     * @property {number} TEXT=1 TEXT value
     * @property {number} PICTURE=2 PICTURE value
     * @property {number} VOICE=3 VOICE value
     */
    ExamMessage.MessageType = (function () {
        var valuesById = {}, values = Object.create(valuesById);
        values[valuesById[1] = "TEXT"] = 1;
        values[valuesById[2] = "PICTURE"] = 2;
        values[valuesById[3] = "VOICE"] = 3;
        return values;
    })();

    return ExamMessage;
})();

$root.ExamMessageRead = (function () {

    /**
     * Properties of an ExamMessageRead.
     * @exports IExamMessageRead
     * @interface IExamMessageRead
     * @property {IExamUser} from ExamMessageRead from
     * @property {string} messageId ExamMessageRead messageId
     */

    /**
     * Constructs a new ExamMessageRead.
     * @exports ExamMessageRead
     * @classdesc Represents an ExamMessageRead.
     * @implements IExamMessageRead
     * @constructor
     * @param {IExamMessageRead=} [properties] Properties to set
     */
    function ExamMessageRead(properties) {
        if (properties)
            for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * ExamMessageRead from.
     * @member {IExamUser} from
     * @memberof ExamMessageRead
     * @instance
     */
    ExamMessageRead.prototype.from = null;

    /**
     * ExamMessageRead messageId.
     * @member {string} messageId
     * @memberof ExamMessageRead
     * @instance
     */
    ExamMessageRead.prototype.messageId = "";

    /**
     * Creates a new ExamMessageRead instance using the specified properties.
     * @function create
     * @memberof ExamMessageRead
     * @static
     * @param {IExamMessageRead=} [properties] Properties to set
     * @returns {ExamMessageRead} ExamMessageRead instance
     */
    ExamMessageRead.create = function create(properties) {
        return new ExamMessageRead(properties);
    };

    /**
     * Encodes the specified ExamMessageRead message. Does not implicitly {@link ExamMessageRead.verify|verify} messages.
     * @function encode
     * @memberof ExamMessageRead
     * @static
     * @param {IExamMessageRead} message ExamMessageRead message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ExamMessageRead.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        $root.ExamUser.encode(message.from, writer.uint32(/* id 1, wireType 2 =*/10).fork()).ldelim();
        writer.uint32(/* id 2, wireType 2 =*/18).string(message.messageId);
        return writer;
    };

    /**
     * Encodes the specified ExamMessageRead message, length delimited. Does not implicitly {@link ExamMessageRead.verify|verify} messages.
     * @function encodeDelimited
     * @memberof ExamMessageRead
     * @static
     * @param {IExamMessageRead} message ExamMessageRead message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ExamMessageRead.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes an ExamMessageRead message from the specified reader or buffer.
     * @function decode
     * @memberof ExamMessageRead
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {ExamMessageRead} ExamMessageRead
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ExamMessageRead.decode = function decode(reader, length) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        var end = length === undefined ? reader.len : reader.pos + length, message = new $root.ExamMessageRead();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    message.from = $root.ExamUser.decode(reader, reader.uint32());
                    break;
                }
                case 2: {
                    message.messageId = reader.string();
                    break;
                }
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        if (!message.hasOwnProperty("from"))
            throw $util.ProtocolError("missing required 'from'", { instance: message });
        if (!message.hasOwnProperty("messageId"))
            throw $util.ProtocolError("missing required 'messageId'", { instance: message });
        return message;
    };

    /**
     * Decodes an ExamMessageRead message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof ExamMessageRead
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {ExamMessageRead} ExamMessageRead
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ExamMessageRead.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies an ExamMessageRead message.
     * @function verify
     * @memberof ExamMessageRead
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    ExamMessageRead.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        {
            var error = $root.ExamUser.verify(message.from);
            if (error)
                return "from." + error;
        }
        if (!$util.isString(message.messageId))
            return "messageId: string expected";
        return null;
    };

    /**
     * Creates an ExamMessageRead message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof ExamMessageRead
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {ExamMessageRead} ExamMessageRead
     */
    ExamMessageRead.fromObject = function fromObject(object) {
        if (object instanceof $root.ExamMessageRead)
            return object;
        var message = new $root.ExamMessageRead();
        if (object.from != null) {
            if (typeof object.from !== "object")
                throw TypeError(".ExamMessageRead.from: object expected");
            message.from = $root.ExamUser.fromObject(object.from);
        }
        if (object.messageId != null)
            message.messageId = String(object.messageId);
        return message;
    };

    /**
     * Creates a plain object from an ExamMessageRead message. Also converts values to other types if specified.
     * @function toObject
     * @memberof ExamMessageRead
     * @static
     * @param {ExamMessageRead} message ExamMessageRead
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    ExamMessageRead.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        var object = {};
        if (options.defaults) {
            object.from = null;
            object.messageId = "";
        }
        if (message.from != null && message.hasOwnProperty("from"))
            object.from = $root.ExamUser.toObject(message.from, options);
        if (message.messageId != null && message.hasOwnProperty("messageId"))
            object.messageId = message.messageId;
        return object;
    };

    /**
     * Converts this ExamMessageRead to JSON.
     * @function toJSON
     * @memberof ExamMessageRead
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    ExamMessageRead.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for ExamMessageRead
     * @function getTypeUrl
     * @memberof ExamMessageRead
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    ExamMessageRead.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/ExamMessageRead";
    };

    return ExamMessageRead;
})();

$root.ZpExamProtocol = (function () {

    /**
     * Properties of a ZpExamProtocol.
     * @exports IZpExamProtocol
     * @interface IZpExamProtocol
     * @property {ZpExamProtocol.ProType} proType ZpExamProtocol proType
     * @property {Array.<IExamMessage>|null} [messages] ZpExamProtocol messages
     * @property {Array.<IExamMessageRead>|null} [messageRead] ZpExamProtocol messageRead
     */

    /**
     * Constructs a new ZpExamProtocol.
     * @exports ZpExamProtocol
     * @classdesc Represents a ZpExamProtocol.
     * @implements IZpExamProtocol
     * @constructor
     * @param {IZpExamProtocol=} [properties] Properties to set
     */
    function ZpExamProtocol(properties) {
        this.messages = [];
        this.messageRead = [];
        if (properties)
            for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * ZpExamProtocol proType.
     * @member {ZpExamProtocol.ProType} proType
     * @memberof ZpExamProtocol
     * @instance
     */
    ZpExamProtocol.prototype.proType = 1;

    /**
     * ZpExamProtocol messages.
     * @member {Array.<IExamMessage>} messages
     * @memberof ZpExamProtocol
     * @instance
     */
    ZpExamProtocol.prototype.messages = $util.emptyArray;

    /**
     * ZpExamProtocol messageRead.
     * @member {Array.<IExamMessageRead>} messageRead
     * @memberof ZpExamProtocol
     * @instance
     */
    ZpExamProtocol.prototype.messageRead = $util.emptyArray;

    /**
     * Creates a new ZpExamProtocol instance using the specified properties.
     * @function create
     * @memberof ZpExamProtocol
     * @static
     * @param {IZpExamProtocol=} [properties] Properties to set
     * @returns {ZpExamProtocol} ZpExamProtocol instance
     */
    ZpExamProtocol.create = function create(properties) {
        return new ZpExamProtocol(properties);
    };

    /**
     * Encodes the specified ZpExamProtocol message. Does not implicitly {@link ZpExamProtocol.verify|verify} messages.
     * @function encode
     * @memberof ZpExamProtocol
     * @static
     * @param {IZpExamProtocol} message ZpExamProtocol message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ZpExamProtocol.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        writer.uint32(/* id 1, wireType 0 =*/8).int32(message.proType);
        if (message.messages != null && message.messages.length)
            for (var i = 0; i < message.messages.length; ++i)
                $root.ExamMessage.encode(message.messages[i], writer.uint32(/* id 2, wireType 2 =*/18).fork()).ldelim();
        if (message.messageRead != null && message.messageRead.length)
            for (var i = 0; i < message.messageRead.length; ++i)
                $root.ExamMessageRead.encode(message.messageRead[i], writer.uint32(/* id 3, wireType 2 =*/26).fork()).ldelim();
        return writer;
    };

    /**
     * Encodes the specified ZpExamProtocol message, length delimited. Does not implicitly {@link ZpExamProtocol.verify|verify} messages.
     * @function encodeDelimited
     * @memberof ZpExamProtocol
     * @static
     * @param {IZpExamProtocol} message ZpExamProtocol message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ZpExamProtocol.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a ZpExamProtocol message from the specified reader or buffer.
     * @function decode
     * @memberof ZpExamProtocol
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {ZpExamProtocol} ZpExamProtocol
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ZpExamProtocol.decode = function decode(reader, length) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        var end = length === undefined ? reader.len : reader.pos + length, message = new $root.ZpExamProtocol();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    message.proType = reader.int32();
                    break;
                }
                case 2: {
                    if (!(message.messages && message.messages.length))
                        message.messages = [];
                    message.messages.push($root.ExamMessage.decode(reader, reader.uint32()));
                    break;
                }
                case 3: {
                    if (!(message.messageRead && message.messageRead.length))
                        message.messageRead = [];
                    message.messageRead.push($root.ExamMessageRead.decode(reader, reader.uint32()));
                    break;
                }
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        if (!message.hasOwnProperty("proType"))
            throw $util.ProtocolError("missing required 'proType'", { instance: message });
        return message;
    };

    /**
     * Decodes a ZpExamProtocol message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof ZpExamProtocol
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {ZpExamProtocol} ZpExamProtocol
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ZpExamProtocol.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a ZpExamProtocol message.
     * @function verify
     * @memberof ZpExamProtocol
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    ZpExamProtocol.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        switch (message.proType) {
            default:
                return "proType: enum value expected";
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
            case 7:
            case 8:
            case 9:
            case 10:
            case 11:
            case 12:
            case 13:
            case 14:
            case 15:
            case 16:
                break;
        }
        if (message.messages != null && message.hasOwnProperty("messages")) {
            if (!Array.isArray(message.messages))
                return "messages: array expected";
            for (var i = 0; i < message.messages.length; ++i) {
                var error = $root.ExamMessage.verify(message.messages[i]);
                if (error)
                    return "messages." + error;
            }
        }
        if (message.messageRead != null && message.hasOwnProperty("messageRead")) {
            if (!Array.isArray(message.messageRead))
                return "messageRead: array expected";
            for (var i = 0; i < message.messageRead.length; ++i) {
                var error = $root.ExamMessageRead.verify(message.messageRead[i]);
                if (error)
                    return "messageRead." + error;
            }
        }
        return null;
    };

    /**
     * Creates a ZpExamProtocol message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof ZpExamProtocol
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {ZpExamProtocol} ZpExamProtocol
     */
    ZpExamProtocol.fromObject = function fromObject(object) {
        if (object instanceof $root.ZpExamProtocol)
            return object;
        var message = new $root.ZpExamProtocol();
        switch (object.proType) {
            default:
                if (typeof object.proType === "number") {
                    message.proType = object.proType;
                    break;
                }
                break;
            case "IM":
            case 1:
                message.proType = 1;
                break;
            case "EXAM_COMMIT":
            case 2:
                message.proType = 2;
                break;
            case "LOGIN_CONFLICT":
            case 3:
                message.proType = 3;
                break;
            case "NOTICE":
            case 4:
                message.proType = 4;
                break;
            case "READ_MESSAGE":
            case 5:
                message.proType = 5;
                break;
            case "EXAMINEE_COMMIT":
            case 6:
                message.proType = 6;
                break;
            case "SEND_EXAMINEE_WARNING":
            case 7:
                message.proType = 7;
                break;
            case "SEND_IDENTITY_AUDIT_RESULT":
            case 8:
                message.proType = 8;
                break;
            case "SEND_TO_AUDIT_IDENTITY_AUDIT_INFO":
            case 9:
                message.proType = 9;
                break;
            case "HUMMER_AUTH_NOTICE":
            case 10:
                message.proType = 10;
                break;
            case "EXAMINEE_LEAVE_SEAT":
            case 11:
                message.proType = 11;
                break;
            case "EXAMINEE_LOWER_HEAD":
            case 12:
                message.proType = 12;
                break;
            case "EXAMINEE_LOOK_AROUND":
            case 13:
                message.proType = 13;
                break;
            case "EXAMINEE_MULTIPLE_FACES":
            case 14:
                message.proType = 14;
                break;
            case "EXAMINEE_SUBSTITUTE_EXAM":
            case 15:
                message.proType = 15;
                break;
            case "V_STREAM_CALLBACK_NOTICE":
            case 16:
                message.proType = 16;
                break;
        }
        if (object.messages) {
            if (!Array.isArray(object.messages))
                throw TypeError(".ZpExamProtocol.messages: array expected");
            message.messages = [];
            for (var i = 0; i < object.messages.length; ++i) {
                if (typeof object.messages[i] !== "object")
                    throw TypeError(".ZpExamProtocol.messages: object expected");
                message.messages[i] = $root.ExamMessage.fromObject(object.messages[i]);
            }
        }
        if (object.messageRead) {
            if (!Array.isArray(object.messageRead))
                throw TypeError(".ZpExamProtocol.messageRead: array expected");
            message.messageRead = [];
            for (var i = 0; i < object.messageRead.length; ++i) {
                if (typeof object.messageRead[i] !== "object")
                    throw TypeError(".ZpExamProtocol.messageRead: object expected");
                message.messageRead[i] = $root.ExamMessageRead.fromObject(object.messageRead[i]);
            }
        }
        return message;
    };

    /**
     * Creates a plain object from a ZpExamProtocol message. Also converts values to other types if specified.
     * @function toObject
     * @memberof ZpExamProtocol
     * @static
     * @param {ZpExamProtocol} message ZpExamProtocol
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    ZpExamProtocol.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        var object = {};
        if (options.arrays || options.defaults) {
            object.messages = [];
            object.messageRead = [];
        }
        if (options.defaults)
            object.proType = options.enums === String ? "IM" : 1;
        if (message.proType != null && message.hasOwnProperty("proType"))
            object.proType = options.enums === String ? $root.ZpExamProtocol.ProType[message.proType] === undefined ? message.proType : $root.ZpExamProtocol.ProType[message.proType] : message.proType;
        if (message.messages && message.messages.length) {
            object.messages = [];
            for (var j = 0; j < message.messages.length; ++j)
                object.messages[j] = $root.ExamMessage.toObject(message.messages[j], options);
        }
        if (message.messageRead && message.messageRead.length) {
            object.messageRead = [];
            for (var j = 0; j < message.messageRead.length; ++j)
                object.messageRead[j] = $root.ExamMessageRead.toObject(message.messageRead[j], options);
        }
        return object;
    };

    /**
     * Converts this ZpExamProtocol to JSON.
     * @function toJSON
     * @memberof ZpExamProtocol
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    ZpExamProtocol.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for ZpExamProtocol
     * @function getTypeUrl
     * @memberof ZpExamProtocol
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    ZpExamProtocol.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/ZpExamProtocol";
    };

    /**
     * ProType enum.
     * @name ZpExamProtocol.ProType
     * @enum {number}
     * @property {number} IM=1 IM value
     * @property {number} EXAM_COMMIT=2 EXAM_COMMIT value
     * @property {number} LOGIN_CONFLICT=3 LOGIN_CONFLICT value
     * @property {number} NOTICE=4 NOTICE value
     * @property {number} READ_MESSAGE=5 READ_MESSAGE value
     * @property {number} EXAMINEE_COMMIT=6 EXAMINEE_COMMIT value
     * @property {number} SEND_EXAMINEE_WARNING=7 SEND_EXAMINEE_WARNING value
     * @property {number} SEND_IDENTITY_AUDIT_RESULT=8 SEND_IDENTITY_AUDIT_RESULT value
     * @property {number} SEND_TO_AUDIT_IDENTITY_AUDIT_INFO=9 SEND_TO_AUDIT_IDENTITY_AUDIT_INFO value
     * @property {number} HUMMER_AUTH_NOTICE=10 HUMMER_AUTH_NOTICE value
     * @property {number} EXAMINEE_LEAVE_SEAT=11 EXAMINEE_LEAVE_SEAT value
     * @property {number} EXAMINEE_LOWER_HEAD=12 EXAMINEE_LOWER_HEAD value
     * @property {number} EXAMINEE_LOOK_AROUND=13 EXAMINEE_LOOK_AROUND value
     * @property {number} EXAMINEE_MULTIPLE_FACES=14 EXAMINEE_MULTIPLE_FACES value
     * @property {number} EXAMINEE_SUBSTITUTE_EXAM=15 EXAMINEE_SUBSTITUTE_EXAM value
     * @property {number} V_STREAM_CALLBACK_NOTICE=16 V_STREAM_CALLBACK_NOTICE value
     */
    ZpExamProtocol.ProType = (function () {
        var valuesById = {}, values = Object.create(valuesById);
        values[valuesById[1] = "IM"] = 1;
        values[valuesById[2] = "EXAM_COMMIT"] = 2;
        values[valuesById[3] = "LOGIN_CONFLICT"] = 3;
        values[valuesById[4] = "NOTICE"] = 4;
        values[valuesById[5] = "READ_MESSAGE"] = 5;
        values[valuesById[6] = "EXAMINEE_COMMIT"] = 6;
        values[valuesById[7] = "SEND_EXAMINEE_WARNING"] = 7;
        values[valuesById[8] = "SEND_IDENTITY_AUDIT_RESULT"] = 8;
        values[valuesById[9] = "SEND_TO_AUDIT_IDENTITY_AUDIT_INFO"] = 9;
        values[valuesById[10] = "HUMMER_AUTH_NOTICE"] = 10;
        values[valuesById[11] = "EXAMINEE_LEAVE_SEAT"] = 11;
        values[valuesById[12] = "EXAMINEE_LOWER_HEAD"] = 12;
        values[valuesById[13] = "EXAMINEE_LOOK_AROUND"] = 13;
        values[valuesById[14] = "EXAMINEE_MULTIPLE_FACES"] = 14;
        values[valuesById[15] = "EXAMINEE_SUBSTITUTE_EXAM"] = 15;
        values[valuesById[16] = "V_STREAM_CALLBACK_NOTICE"] = 16;
        return values;
    })();

    return ZpExamProtocol;
})();

// module.exports = $root;
export default $root;
/*eslint-disable */
