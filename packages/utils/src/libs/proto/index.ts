import proto from './chat';

export function protobufEncode(payload: any) {
    // 必要时验证要传入的对象是否符合要求
    const errMsg = proto.ZpExamProtocol.verify(payload);
    if (errMsg) {
        throw new Error(errMsg);
    }
    // 创建一个新消息
    const message = proto.ZpExamProtocol.create(payload);

    // 将消息编码到Uint8Array（浏览器）或Buffer（节点）
    const buffer = proto.ZpExamProtocol.encode(message).finish();

    return buffer;
}
export function protobufDecode(buffer: any) {
    if (!buffer) {
        return;
    }
    // 将Uint8Array（浏览器）或Buffer（节点）解码为消息
    const decodeMessage = proto.ZpExamProtocol.decode(buffer);

    // 将消息转换回普通对象
    const obj = proto.ZpExamProtocol.toObject(decodeMessage);
    return obj;
}
