syntax = "proto2";
option java_package = "com.kanjian.exam.websocket.model";
option java_outer_classname = "ExamProtocol";

//用户信息
message ExamUser {
  //用户uid
  required string userId = 1;
  //用户头像
  optional string avatar = 2;
  //用户角色 1考生 2监考管 0 系统 3 corp_admin 用户
  enum RoleType {
    EXAMINEE = 1;
    EXAMINER = 2;
    SYSTEM = 0;
    CORP_ADMIN_USER = 3;
  }
  required RoleType roleType = 3;
  //用户姓名
  optional string userName = 4;
}

//消息内容
message ExamMessage {
  // message type 1文本 2图片 3语音
  enum MessageType {
    TEXT = 1;
    PICTURE = 2;
    VOICE = 3;
  }
  required MessageType messageType = 1;
  required ExamUser from = 2;
  required ExamUser to = 3;
  //文本内容 当type为1是,必须设置
  optional string text = 4;
  //考试id
  required string examId = 5;
  //聊天时间
  optional string talkTime = 6;
  //未读数量
  optional int32 unreadCount = 7;
  //群聊ID
  optional string groupId = 8;
  //消息ID
  optional string messageId = 9;
}

message ExamMessageRead{
  required ExamUser from = 1;
  //消息ID
  required string messageId = 2;
}

//协议
message ZpExamProtocol {
  //协议type  1:IM消息 2:监考官强制交卷 3:考生重复登录互踢 4:公告 5:消息已读回复 6:考生交卷 7:监考官向考生发送消息提醒 8:考生身份人工审核结果 9:向考官发送人工审核-待审核信息 10:异步人脸认证通知
  //         11 考生离开 12 考生低头 13 考生左右张望 14 考生多人脸 15 考生替考 16 v组流事件回调通知 17 考前准备结束 18 电脑屏幕监控消息
  enum ProType{
    IM = 1;
    EXAM_COMMIT = 2;
    LOGIN_CONFLICT = 3;
    NOTICE = 4;
    READ_MESSAGE = 5;
    EXAMINEE_COMMIT = 6;
    SEND_EXAMINEE_WARNING = 7;
    SEND_IDENTITY_AUDIT_RESULT = 8;
    SEND_TO_AUDIT_IDENTITY_AUDIT_INFO = 9;
    HUMMER_AUTH_NOTICE = 10;
    EXAMINEE_LEAVE_SEAT = 11;
    EXAMINEE_LOWER_HEAD = 12;
    EXAMINEE_LOOK_AROUND = 13;
    EXAMINEE_MULTIPLE_FACES = 14;
    EXAMINEE_SUBSTITUTE_EXAM = 15;
    V_STREAM_CALLBACK_NOTICE = 16;
    EXAM_PREPARE_OVER = 17;
    COMPUTER_SCREEN_MONITORING = 18;

  }
  required ProType proType = 1;
  //type为1,2,3,4 时，必须设置
  repeated ExamMessage messages = 2;
  //消息已读状态回复
  repeated ExamMessageRead messageRead = 3;
}
