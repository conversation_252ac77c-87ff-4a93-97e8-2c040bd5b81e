{"options": {"java_package": "com.kanjian.exam.websocket.model", "java_outer_classname": "ExamProtocol"}, "nested": {"ExamUser": {"fields": {"userId": {"rule": "required", "type": "string", "id": 1}, "avatar": {"type": "string", "id": 2}, "roleType": {"rule": "required", "type": "RoleType", "id": 3}, "userName": {"type": "string", "id": 4}}, "nested": {"RoleType": {"values": {"EXAMINEE": 1, "EXAMINER": 2, "SYSTEM": 0, "CORP_ADMIN_USER": 3}}}}, "ExamMessage": {"fields": {"messageType": {"rule": "required", "type": "MessageType", "id": 1}, "from": {"rule": "required", "type": "ExamUser", "id": 2}, "to": {"rule": "required", "type": "ExamUser", "id": 3}, "text": {"type": "string", "id": 4}, "examId": {"rule": "required", "type": "string", "id": 5}, "talkTime": {"type": "string", "id": 6}, "unreadCount": {"type": "int32", "id": 7}, "groupId": {"type": "string", "id": 8}, "messageId": {"type": "string", "id": 9}}, "nested": {"MessageType": {"values": {"TEXT": 1, "PICTURE": 2, "VOICE": 3}}}}, "ExamMessageRead": {"fields": {"from": {"rule": "required", "type": "ExamUser", "id": 1}, "messageId": {"rule": "required", "type": "string", "id": 2}}}, "ZpExamProtocol": {"fields": {"proType": {"rule": "required", "type": "ProType", "id": 1}, "messages": {"rule": "repeated", "type": "ExamMessage", "id": 2}, "messageRead": {"rule": "repeated", "type": "ExamMessageRead", "id": 3}}, "nested": {"ProType": {"values": {"IM": 1, "EXAM_COMMIT": 2, "LOGIN_CONFLICT": 3, "NOTICE": 4, "READ_MESSAGE": 5, "EXAMINEE_COMMIT": 6, "SEND_EXAMINEE_WARNING": 7, "SEND_IDENTITY_AUDIT_RESULT": 8, "SEND_TO_AUDIT_IDENTITY_AUDIT_INFO": 9, "HUMMER_AUTH_NOTICE": 10, "EXAMINEE_LEAVE_SEAT": 11, "EXAMINEE_LOWER_HEAD": 12, "EXAMINEE_LOOK_AROUND": 13, "EXAMINEE_MULTIPLE_FACES": 14, "EXAMINEE_SUBSTITUTE_EXAM": 15, "V_STREAM_CALLBACK_NOTICE": 16}}}}}}