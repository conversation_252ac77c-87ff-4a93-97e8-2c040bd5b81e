/**
 * ex: v-customTxt="{reg:'\d'}" 只允许输入数字
 */
import type { ObjectDirective } from 'vue';

export default {
    mounted(el, binding) {
        const input = el.querySelector('input') || el.querySelector('textarea');

        const reg = (binding.value || {}).reg || 'd\u4E00-\u9FA5A-Za-z';
        // let replaceReg = new RegExp(`[^\\${reg}]`, 'g');
        // let testReg = new RegExp(`[\\${reg}]`, 'g');

        // 监听keypress，阻止用户输入不合法的字符
        let oldValue = '';
        let c = true;
        input?.addEventListener('keypress', (e: any) => {
            const m = e.key;
            c = new RegExp(`[\\${reg}]`, 'g').test(m);
            // 用存起来的正则第 2n 次触发keypress校验不通过
            //  c = testReg.test(''+m);
            if (!c) {
                // debugger;
                e.preventDefault();
                e.returnValue = false;
                return false;
            }
            return true;
        });

        // if (!(binding.value || {}).reg) {
        // ToDO 中文^  没有触发 onkeypress
        input?.addEventListener('input', (e: InputEvent) => {
            const _this = e.target;
            if ((binding.value || {}).reg && _this) {
                setTimeout(() => {
                    const val = input?.value;
                    if (oldValue === val) {
                        return;
                    }
                    oldValue = val;
                    (<HTMLInputElement>_this).value = val.replace(new RegExp(`[^\\${reg}]`, 'g'), '');
                    _this.dispatchEvent(new Event('input')); // 触发input通知v-model更新
                });
            }
        });
        input?.addEventListener('paste', (e: any) => {
            const _this = e.target;
            // 立即更新会没有效果，通过setTimeout()加入任务队列末尾，做一个延迟
            const tid = window.setTimeout(() => {
                const val = input?.value;
                _this.value = val.replace(new RegExp(`[^\\${reg}]`, 'g'), '');
                _this.dispatchEvent(new Event('input')); // 触发input通知v-model更新
                window.clearTimeout(tid);
            }, 0);
        });
    },
    beforeUnmount(el) {
        const input = el.querySelector('input') || el.querySelector('textarea');
        input.onpaste = null;
        input.onkeyup = null;
    },
} as ObjectDirective;
