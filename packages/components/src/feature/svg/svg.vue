<template>
    <svg :class="svgClass" aria-hidden="true">
        <use :xlink:href="iconName" />
    </svg>
</template>

<script lang="ts">
import { computed, defineComponent } from 'vue';

export default defineComponent({
    name: 'SvgIcon',
    props: {
        name: {
            type: String,
            required: true,
        },
        className: {
            type: String,
            default: '',
        },
        height: {
            type: [String, Number],
            default: '14',
        },
        width: {
            type: [String, Number],
            default: '14',
        },
    },
    setup(props) {
        const iconName = computed(() => {
            return `#icon-${props.name}`;
        });

        const svgClass = computed(() => {
            if (props.className) {
                return `svg-icon ${props.className}`;
            }
            return 'svg-icon';
        });

        return {
            iconName,
            svgClass,
        };
    },
});
</script>

<style scoped>
.svg-icon {
    width: v-bind(`${width}px`);
    height: v-bind(`${height}px`);
    vertical-align: middle;
    fill: currentColor;
    overflow: hidden;
    outline: none;
}
</style>
