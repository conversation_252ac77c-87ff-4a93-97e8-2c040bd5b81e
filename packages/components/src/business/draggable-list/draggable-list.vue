<template>
    <div ref="el" class="c-draggable-container">
        <div v-for="(item, index) in list" :key="item.id" class="c-draggable-item">
            <slot name="item" :item="item" :index />
        </div>
        <slot />
    </div>
</template>

<script setup lang="ts" generic="T extends { id: string | number }">
import type { MaybeRef } from 'vue';
import type { UseDraggableOptions } from 'vue-draggable-plus';
import { computed, ref, unref } from 'vue';
import { useDraggable } from 'vue-draggable-plus';

const list = defineModel<T[]>();
const props = defineProps<{ options?: MaybeRef<UseDraggableOptions<T>> }>();

defineSlots<{
    default: (props: any) => any;
    item: (props: { item: T; index: number }) => any;
}>();

const el = ref<HTMLDivElement>();

const options = computed(() => {
    const options = unref(props.options);
    return {
        ...options,
        animation: options?.animation ?? 150,
        ghostClass: options?.ghostClass ?? 'ghost',
    };
});

useDraggable(el, list, options);
</script>

<style scoped>
.ghost {
    opacity: 0.5;
}
</style>
