<template>
    <div class="mono-status-container" :class="{ 'mobile-status-view': isMobileView }">
        <div class="mono-status-img" :style="{ backgroundImage: `url(${imgSrc})` }"></div>
        <div v-if="text" class="text">
            {{ text }}
        </div>
        <button class="mono-status-button" @click="handleClick">返回考试列表（{{ seconds }}s）</button>
    </div>
</template>

<script setup lang="ts">
import { useIsMobile } from '@crm/exam-hooks';
import { ExamStatusEnum } from '@crm/exam-types';
import { computed, onMounted, ref } from 'vue';

defineOptions({
    name: 'MonoStatus',
});
interface IProps {
    status: ExamStatusEnum;
    text: string;
    isH5?: boolean;
}
const props = withDefaults(defineProps<IProps>(), {});
const emit = defineEmits<{
    onButtonClick: [];
    onTimeout: [];
}>();
const { isMobileView } = useIsMobile();

const imgSrc = computed(() => {
    if (
        [
            ExamStatusEnum.调试已结束,
            ExamStatusEnum.未开始,
            ExamStatusEnum.已结束,
            ExamStatusEnum.提前入场,
            ExamStatusEnum.禁止入场,
            ExamStatusEnum.即时模式下前一场未交卷且未结束,
        ].includes(props.status)
    ) {
        return 'https://img.bosszhipin.com/static/zhipin/kanjian/zhice/kaoshi/mono-status.svg';
    } else if (props.status === ExamStatusEnum.已交卷) {
        return 'https://img.bosszhipin.com/static/zhipin/kanjian/zhice/kaoshi/end-status.svg';
    } else if (props.status === ExamStatusEnum.兼容拦截) {
        return 'https://img.bosszhipin.com/static/zhipin/kanjian/zhice/kaoshi/compatibility-status.svg';
    }
    return '';
});
const seconds = ref(3);
function handleClick() {
    emit('onButtonClick');
}
function countDown() {
    if (seconds.value <= 0) {
        emit('onTimeout');
        return;
    }
    setTimeout(() => {
        seconds.value--;
        countDown();
    }, 1000);
}
onMounted(() => {
    countDown();
});
</script>

<style lang="less" scoped>
.mono-status-container {
    display: flex;
    flex-direction: column;
    align-items: center;

    .mono-status-img {
        width: 140px;
        height: 140px;
        background-size: auto 100%;
        background-repeat: no-repeat;
        background-position: center;
    }

    .text {
        margin-top: 20px;
        margin-bottom: 24px;
        text-align: center;
    }

    .mono-status-button {
        color: #fff;
        border-color: #00a6a7;
        background-color: #00a6a7;
        display: inline-flex;
        position: relative;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        outline: none;
        border: 1px #f0f1f2 solid;
        border-radius: 4px;
        height: 32px;
        padding: 0 15px;
        user-select: none;
        font-family: inherit;
        transition: all 0.1s cubic-bezier(0, 0, 1, 1);
        will-change: box-shadow;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
        white-space: nowrap;
        line-height: normal;
        vertical-align: middle;
        cursor: pointer;
    }

    &.mobile-status-view {
        .text {
            color: #1f1f1f;
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: 22px;
        }

        .mono-status-button {
            margin-top: 40px;
            width: 335px;
            height: 44px;
            border-radius: 8px;
        }
    }
}
</style>
