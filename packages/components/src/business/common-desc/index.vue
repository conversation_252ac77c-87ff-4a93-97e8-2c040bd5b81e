<template>
    <div class="common-desc-container">
        <div v-if="label" class="common-desc-label">{{ label }}：</div>
        <div v-if="!loading" class="common-desc-content">
            <div class="common-desc-content">
                <slot v-if="!blank" />
                <template v-else> -- </template>
            </div>
        </div>
        <b-skeleton v-else :rows="0" animated />
    </div>
</template>

<script setup lang="ts">
interface PropsType {
    label?: string;
    align?: 'start' | 'center';
    fullWidth?: boolean;
    loading?: boolean;
    wrap?: boolean;
    blank?: boolean;
}

defineOptions({
    name: 'CommonDesc',
});
withDefaults(defineProps<PropsType>(), {
    align: 'start',
});

defineSlots<{
    default: (props: any) => any;
}>();
</script>

<style scoped>
.common-desc-container {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    gap: 4px;
    width: 100%;
}
.common-desc-label {
    color: #808080;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    flex-shrink: 0;
    white-space: pre-wrap;
}
.common-desc-content {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}
:deep(.b-skeleton-item) {
    margin: 4px 0;
}
:deep(.b-tag) {
    margin: 0;
}
</style>
