import { onMounted, onUnmounted } from 'vue';

export function useExamInputGuard() {
    // Renamed from useTabNavigationControl
    const handleKeydown = (event: KeyboardEvent) => {
        // 禁用 Tab 键切换焦点
        if (event.key === 'Tab') {
            event.preventDefault();
            return; // Processed, no need to check further
        }

        // 阻止 Enter 键在特定 input 字段中触发表单提交
        if (event.key === 'Enter') {
            const targetElement = event.target as HTMLElement;
            if (targetElement) {
                const tagName = targetElement.tagName.toLowerCase();
                const inputType = (targetElement as HTMLInputElement).type?.toLowerCase();

                // 仅当焦点在特定类型的 input 元素上时阻止 Enter
                // 不阻止 textarea (换行) 或各种 button 类型
                if (
                    tagName === 'input' &&
                    inputType && // ensure inputType is defined
                    !['button', 'submit', 'reset', 'image', 'checkbox', 'radio', 'file', 'color', 'range'].includes(inputType)
                    // 排除那些 Enter 有合理默认行为或通常不用于提交的类型
                ) {
                    event.preventDefault();
                    return; // Processed
                }
                // 允许 Enter 在 textarea, button, 和其他默认行为合理的元素上
            }
        }

        // 关于其他快捷键 (例如 空格键):
        // 全局阻止空格键会破坏基本的文本输入。
        // "Tab + 空格" 的原始顾虑，由于 Tab 本身已被阻止，其作为修饰键的场景不再直接适用。
        // 如果空格键在特定控件（如自定义按钮或选择组件）上触发了不期望的行为，
        // 建议在这些组件内部或通过更具体的事件处理来解决。
        // 此 Hook 专注于 Tab 和 Enter 这两个最常见的误操作源。
    };

    onMounted(() => {
        window.addEventListener('keydown', handleKeydown, true); // Use capture phase if needed, but usually not
    });

    onUnmounted(() => {
        window.removeEventListener('keydown', handleKeydown, true); // Match capture phase if used
    });
}
