import { getCurrentInstance, getCurrentScope, nextTick, onMounted, onScopeDispose, ref } from 'vue';

type Fn = () => void;

function tryOnScopeDispose(fn: Fn) {
    if (getCurrentScope()) {
        onScopeDispose(fn);
        return true;
    }
    return false;
}

function tryOnMounted(fn: Fn, sync = true) {
    if (getCurrentInstance()) {
        onMounted(fn);
    } else if (sync) {
        fn();
    } else {
        nextTick(fn);
    }
}

export function useBroadcastChannel(name: string) {
    const isClosed = ref(false);
    const channel = ref<BroadcastChannel | undefined>();
    const data = ref();
    const error = ref<Event | null>(null);

    const post = (data: unknown) => {
        if (channel.value) {
            channel.value.postMessage(data);
        }
    };

    const close = () => {
        if (channel.value) {
            channel.value.close();
        }
        isClosed.value = true;
    };

    tryOnMounted(() => {
        error.value = null;
        channel.value = new BroadcastChannel(name);

        channel.value.addEventListener(
            'message',
            (e: MessageEvent) => {
                data.value = e.data;
            },
            { passive: true },
        );

        channel.value.addEventListener(
            'messageerror',
            (e: MessageEvent) => {
                error.value = e;
            },
            { passive: true },
        );

        channel.value.addEventListener('close', () => {
            isClosed.value = true;
        });
    });

    tryOnScopeDispose(() => {
        close();
    });

    return {
        channel,
        data,
        post,
        close,
        error,
        isClosed,
    };
}
