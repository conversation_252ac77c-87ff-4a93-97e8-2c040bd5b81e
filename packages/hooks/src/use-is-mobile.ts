import { onMounted, onUnmounted, ref } from 'vue';

export function useIsMobile() {
    const isMobileView = ref(false);

    function checkMobileView() {
        // 检测屏幕宽度小于等于800px或者是移动设备
        const isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        isMobileView.value = isMobileDevice || window.innerWidth <= 800;
    }
    checkMobileView();

    onMounted(() => {
        checkMobileView();
        window.addEventListener('resize', checkMobileView);
    });

    onUnmounted(() => {
        window.removeEventListener('resize', checkMobileView);
    });

    // 返回计数器状态和增加方法
    return {
        isMobileView,
    };
}
