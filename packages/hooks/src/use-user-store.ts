import { defineStore } from 'pinia';

export const useUserStore = defineStore('user', {
    state: () => ({
        // 如何添加类型
        userInfo: {
            name: '',
            avatar: '',
            encryptId: '',
            id: '',
            roleNames: [],
            hasCorpAuth: 0,
            menus: [],
        },
    }),
    getters: {
        hasInitUserInfo(): boolean {
            return !!this.userInfo.name;
        },
    },
    actions: {
        setUserInfo(userInfo: any) {
            this.userInfo = userInfo;
        },
    },
});
