import { TimeOptions, Timer, TimeManager } from './types';
import { useTimeCenter } from './use-time-center';
import { useTimer } from './use-timer';

const defaultTimeOptions: TimeOptions = {
    syncTimeFrequency: 15 * 1000,
};

/**
 * 创建并管理一个独立的时间中心和相关的计时器。
 *
 * @param {TimeOptions} [customOptions] - 自定义时间中心配置，将与默认配置合并。
 * @returns {{
 *   timeCenter: TimeCenter,
 *   createTimer: () => Timer,
 *   reconfigure: (newOptions?: TimeOptions) => void
 * }} 返回一个包含 timeCenter 实例、createTimer 工厂函数和 reconfigure 重新配置函数的对象。
 *
 * @example
 * // 在组件 `setup` 中使用
 * const { timeCenter, createTimer, reconfigure } = createTimeManager();
 *
 * const timer1 = createTimer();
 * timer1.start({ ... });
 *
 * // 重新配置时间管理器
 * reconfigure({
 *   syncTimeFrequency: 30 * 1000,
 *   syncTime: () => fetch('/api/time').then(res => res.json()).then(data => data.timestamp)
 * });
 *
 * const timer2 = createTimer(); // 使用新配置
 * timer2.start({ ... });
 *
 * // timeCenter 和所有创建的 timer 的生命周期
 * // 会自动与当前组件绑定。
 */
export function createTimeManager(customOptions?: TimeOptions): TimeManager {
    let options = { ...defaultTimeOptions, ...customOptions };
    let currentTimeCenter = useTimeCenter(options);

    /**
     * 创建一个与当前 timeCenter 实例关联的计时器。
     * @returns {Timer}
     */
    function createTimer(): Timer {
        return useTimer({
            currentTime: currentTimeCenter.currentTime,
        });
    }

    /**
     * 重新配置时间管理器。
     * 这将创建一个新的时间中心，但不会停止旧的时间中心。
     * 已存在的计时器会继续使用旧的时间中心，新创建的计时器会使用新的配置。
     * 旧的时间中心会在没有计时器引用时自动被垃圾回收机制清理。
     *
     * @param {TimeOptions} [newOptions] - 新的时间中心配置，将与默认配置合并。
     */
    function reconfigure(newOptions?: TimeOptions): void {
        // 合并新配置
        options = { ...defaultTimeOptions, ...newOptions };

        // 创建新的时间中心，不停止旧的时间中心
        // 旧的时间中心会继续为已存在的计时器提供服务
        // 当没有计时器引用时，旧的时间中心会被垃圾回收
        currentTimeCenter = useTimeCenter(options);
    }

    return {
        get timeCenter() {
            return currentTimeCenter;
        },
        createTimer,
        reconfigure,
    };
}
