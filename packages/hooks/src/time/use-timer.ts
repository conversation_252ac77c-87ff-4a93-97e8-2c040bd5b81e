import { FormatterTime, RegisterTimeOptions, Timer, TimerStatus } from './types';
import { reactive, watch, type Ref } from 'vue';

/** @constant {number} SECOND - 一秒对应的毫秒数。 */
const SECOND = 1000;
/** @constant {number} MINUTE - 一分钟对应的毫秒数。 */
const MINUTE = 60 * SECOND;
/** @constant {number} HOUR - 一小时对应的毫秒数。 */
const HOUR = 60 * MINUTE;

/**
 * @function formatTime
 * @description 将给定的毫秒数格式化为包含时、分、秒、毫秒的对象。
 * @param {number} time - 需要格式化的时间（毫秒）。如果为 `Infinity`，则返回所有字段均为 'Infinity' 的对象。
 * @returns {FormatterTime} 格式化后的时间对象。
 * @see FormatterTime
 */
function formatTime(time: number): FormatterTime {
    // 处理特殊值
    if (time === Infinity) {
        return {
            total: time,
            hours: 'Infinity',
            minutes: 'Infinity',
            seconds: 'Infinity',
            milliseconds: 'Infinity',
        };
    }

    // 处理 NaN 和无效值
    if (isNaN(time) || !isFinite(time)) {
        return {
            total: NaN,
            hours: '--',
            minutes: '--',
            seconds: '--',
            milliseconds: '---',
        };
    }

    // 处理负数时间 - 取绝对值并标记为负数
    const absTime = Math.abs(time);
    const isNegative = time < 0;

    const hours = Math.floor(absTime / HOUR);
    const minutes = Math.floor((absTime % HOUR) / MINUTE);
    const seconds = Math.floor((absTime % MINUTE) / SECOND);
    const milliseconds = Math.floor(absTime % SECOND);

    const formatNumber = (num: number, padLength: number) => {
        const formatted = String(num).padStart(padLength, '0');
        return isNegative ? `-${formatted}` : formatted;
    };

    return {
        total: time,
        hours: formatNumber(hours, 2),
        minutes: formatNumber(minutes, 2),
        seconds: formatNumber(seconds, 2),
        milliseconds: formatNumber(milliseconds, 3),
    };
}

/**
 * @function useTimer
 * @description 创建并管理一个与 `useTimeCenter` 实例联动的计时器。
 *              它利用 `timeCenter` 提供的 `currentTime` 和 `accumulatedTime` 来驱动自身的计时逻辑（启动、暂停、恢复、停止）。
 *              提供了已过时间 (`elapsedTime`) 和剩余时间 (`remainingTime`) 的响应式数据。
 * @param {object} options - 计时器配置选项。
 * @param {Readonly<Ref<number>>} options.currentTime - 从 `useTimeCenter` 获取的当前时间引用。
 * @param {Readonly<Ref<number>>} options.accumulatedTime - 从 `useTimeCenter` 获取的累积时间引用，用于触发计时器状态更新。
 * @see useTimeCenter
 * @see Timer
 * @see RegisterTimeOptions
 * @example
 * const timeCenter = useTimeCenter();
 * const timer = useTimer({
 *   currentTime: timeCenter.currentTime,
 *   accumulatedTime: timeCenter.accumulatedTime
 * });
 *
 * // 启动一个 30 秒的倒计时
 * timer.start({
 *   finishTime: (now) => now + 30000,
 *   onFinished: () => {
 *     console.log('Timer finished!');
 *   }
 * });
 *
 * // 在 Vue 模板中使用
 * // <p>Elapsed: {{ timer.elapsedTime.seconds }}s</p>
 * // <p>Remaining: {{ timer.remainingTime.seconds }}s</p>
 */
export function useTimer(options: { currentTime: Readonly<Ref<number>> }): Timer {
    const { currentTime } = options;
    const initFormatterTime: FormatterTime = {
        total: 0,
        hours: '00',
        minutes: '00',
        seconds: '00',
        milliseconds: '000',
    };

    let lastTrackedTime = currentTime.value;
    let stopWatch: () => void;
    let isProcessingCallback = false; // 防止回调函数重入
    let currentTimeAtCallbackStart = currentTime.value;

    // 安全地执行回调函数
    const safeCallback = (callback: ((...arg: any) => void | Promise<void>) | undefined, ...args: any[]) => {
        if (!callback || isProcessingCallback) return;

        isProcessingCallback = true;
        currentTimeAtCallbackStart = currentTime.value;

        try {
            // 直接调用回调函数，无论它是同步还是异步的
            const result = callback(...args);

            // 如果返回 Promise，则异步处理
            if (result && typeof result.then === 'function') {
                result
                    .catch((error: any) => {
                        console.error('useTimer: 异步回调函数执行错误:', error);
                    })
                    .finally(() => {
                        isProcessingCallback = false;
                    });
            } else {
                // 同步回调立即重置标记
                isProcessingCallback = false;
            }
        } catch (error) {
            console.error('useTimer: 回调函数执行错误:', error);
            isProcessingCallback = false;
        }
    };

    const formatTimes = (timer: Timer) => {
        const relateBeginTime = timer.beginTime - timer.idleTime;
        timer.elapsedTime = formatTime(currentTime.value - relateBeginTime);

        // 如果结束时间是无限大，剩余时间也是无限大
        if (timer.finishTime === Infinity) {
            timer.remainingTime = formatTime(Infinity);
        } else {
            timer.remainingTime = formatTime(Math.max(timer.finishTime - currentTime.value, 0));
        }
    };

    const timer = reactive<Timer>({
        status: TimerStatus.idled,
        beginTime: currentTime.value,
        finishTime: Infinity,
        idleTime: 0,
        currentIdleTime: 0,
        elapsedTime: { ...initFormatterTime },
        remainingTime: { ...initFormatterTime },
        start: (o: RegisterTimeOptions) => {
            if (timer.status === TimerStatus.destroyed) {
                if (typeof console !== 'undefined' && console.warn) {
                    console.warn('useTimer: Cannot start a destroyed timer.');
                }
                return;
            }
            timer.stop();
            timer.elapsedTime = { ...initFormatterTime };
            timer.remainingTime = { ...initFormatterTime };

            // 安全地计算开始和结束时间，并验证有效性
            try {
                const calculatedBeginTime = o.beginTime?.(currentTime.value) ?? currentTime.value;
                const calculatedFinishTime = o.finishTime?.(currentTime.value) ?? Infinity;

                // 验证时间值的有效性
                if (!isFinite(calculatedBeginTime) || isNaN(calculatedBeginTime)) {
                    console.warn('useTimer: Invalid beginTime, using currentTime as fallback');
                    timer.beginTime = currentTime.value;
                } else {
                    timer.beginTime = calculatedBeginTime;
                }

                if (calculatedFinishTime !== Infinity && (!isFinite(calculatedFinishTime) || isNaN(calculatedFinishTime))) {
                    console.warn('useTimer: Invalid finishTime, using Infinity as fallback');
                    timer.finishTime = Infinity;
                } else {
                    timer.finishTime = calculatedFinishTime;
                }

                // 验证时间逻辑合理性
                if (timer.finishTime !== Infinity && timer.finishTime < timer.beginTime) {
                    console.warn('useTimer: finishTime should be greater than beginTime');
                }
            } catch (error) {
                console.error('useTimer: Error calculating timer times:', error);
                timer.beginTime = currentTime.value;
                timer.finishTime = Infinity;
            }

            timer.key = o.key || '';
            timer.actionWhenTimeFinish = o.actionWhenTimeFinish || 'stop';
            timer.onFinished = o.onFinished || (() => {});
            timer.onTick = o.onTick || (() => {});

            formatTimes(timer);

            timer.status = TimerStatus.running;
        },
        stop: () => {
            if (timer.status === TimerStatus.stopped || timer.status === TimerStatus.destroyed || timer.status === TimerStatus.idled) {
                return;
            }

            timer.status = TimerStatus.stopped;
            timer.currentIdleTime = 0;
            if (timer.remainingTime.total !== 0) {
                timer.remainingTime = { ...initFormatterTime, total: 0 };
            }
        },
        destroy: () => {
            if (timer.status === TimerStatus.destroyed) {
                return;
            }
            // 停止监听器以释放资源
            stopWatch?.();

            // 设置状态为销毁
            timer.status = TimerStatus.destroyed;

            // 清理回调函数，防止内存泄漏
            // @ts-ignore
            timer.onFinished = undefined;
            // @ts-ignore
            timer.onTick = undefined;
            // @ts-ignore
            timer.actionWhenTimeFinish = undefined;
            // @ts-ignore
            timer.key = undefined;

            // 重置所有时间值到一个无效或初始状态
            const destroyedTime: FormatterTime = {
                total: NaN,
                hours: '--',
                minutes: '--',
                seconds: '--',
                milliseconds: '---',
            };
            timer.elapsedTime = destroyedTime;
            timer.remainingTime = destroyedTime;
            timer.beginTime = NaN;
            timer.finishTime = NaN;
            timer.idleTime = NaN;
            timer.currentIdleTime = NaN;
        },
        pause: () => {
            if (timer.status === TimerStatus.running) {
                timer.status = TimerStatus.suspended;
            }
        },
        resume: () => {
            if (timer.status === TimerStatus.suspended) {
                // 恢复时重置当前暂停时间（已经在暂停期间累积到 idleTime 了）
                timer.currentIdleTime = 0;
                timer.status = TimerStatus.running;
            }
        },
    });

    stopWatch = watch(
        () => currentTime.value,
        (newTime, oldTime) => {
            const now = newTime;

            // 防止在回调执行期间由于时间变化触发新的 watch
            if (isProcessingCallback && now > currentTimeAtCallbackStart) {
                return;
            }

            const deltaTime = oldTime ? Math.max(0, now - lastTrackedTime) : 0;
            lastTrackedTime = now;

            if (timer.status === TimerStatus.stopped || timer.status === TimerStatus.destroyed) {
                return;
            }
            if (timer.status === TimerStatus.suspended) {
                timer.currentIdleTime += deltaTime;
                timer.idleTime += deltaTime;
                if (timer.finishTime !== Infinity) {
                    timer.finishTime += deltaTime;
                }
                return;
            }

            // 只有在计时器运行且有时间变化时才执行回调
            if (timer.status === TimerStatus.running) {
                formatTimes(timer);

                // 安全地调用 onTick 回调（但在初始化时跳过）
                if (oldTime !== undefined) {
                    safeCallback(timer.onTick, timer);
                }

                if (timer.finishTime !== Infinity && timer.finishTime <= currentTime.value) {
                    // 安全地调用 onFinished 回调
                    safeCallback(timer.onFinished, now);

                    // 最终将时间格式化为结束时间
                    const relateBeginTime = timer.beginTime - timer.idleTime;
                    timer.elapsedTime = formatTime(timer.finishTime - relateBeginTime);
                    timer.remainingTime = formatTime(0);

                    if (timer.actionWhenTimeFinish === 'destroy') {
                        timer.destroy();
                    } else {
                        timer.stop();
                    }
                    return;
                }
            }
        },
        {
            immediate: true,
        },
    );

    return timer;
}
