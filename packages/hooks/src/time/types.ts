import type { Ref } from 'vue';

/**
 * @interface FormatterTime
 * @description 格式化后的时间对象，用于展示计时器经过的时间或剩余时间。
 * @property {number} total - 经过的总时间（毫秒）。注意：如果计算基准时间大于当前时间，此值可能为负数。
 * @property {string} hours - 格式化后的小时数（例如 '01', '12'）。
 * @property {string} minutes - 格式化后的分钟数（例如 '05', '59'）。
 * @property {string} seconds - 格式化后的秒数（例如 '08', '45'）。
 * @property {string} milliseconds - 格式化后的毫秒数（例如 '001', '123'）。
 */
export interface FormatterTime {
    /**
     * 经过的总时间（毫秒）。
     * @description 如果计算基准时间（如 beginTime）大于当前时间，该值会是一个负数，且绝对值随时间递增。
     */
    total: number;
    /**
     * 经过的小时数（两位格式化，不足两位补零）。
     */
    hours: string;
    /**
     * 经过的分钟数（两位格式化，不足两位补零）。
     */
    minutes: string;
    /**
     * 经过的秒数（两位格式化，不足两位补零）。
     */
    seconds: string;
    /**
     * 经过的毫秒数（三位格式化，不足三位补零）。
     */
    milliseconds: string;
}

/**
 * @enum {number} TimerStatus
 * @description 计时器的运行状态枚举。
 */
export enum TimerStatus {
    /**
     * @description 计时器默认状态，未启动。
     */
    idled = -1,
    /**
     * @description 计时器正在运行。
     */
    running = 0,
    /**
     * @description 计时器已暂停。
     */
    suspended = 1,
    /**
     * @description 计时器已停止，可被回收或重新启动。
     */
    stopped = 2,
    /**
     * @description 计时器已被销毁，无法再使用。
     */
    destroyed = 3,
}

/**
 * @interface RegisterTimeOptions
 * @description 注册或启动计时器时使用的配置选项。
 */
export interface RegisterTimeOptions {
    /**
     * @description 计时器的唯一标识符，可选。用于在多个计时器场景下区分和获取特定计时器实例。
     */
    key?: string;
    /**
     * @description 计时器的起始时间戳（毫秒）。可以是一个返回时间戳的函数，该函数在计时器启动时调用，接收当前时间中心的时间作为参数。
     * @default currentTime.value (计时器启动时的当前时间)
     * @param {number} currentTime - 计时器启动时的时间中心当前时间。
     * @returns {number} 计时器的起始时间戳。
     * @example
     * // 从当前时间 10 秒后开始
     * beginTime: (currentTime) => currentTime + 10000
     * // 使用固定的时间戳
     * beginTime: () => 1678886400000
     */
    beginTime?: (currentTime: number) => number;
    /**
     * @description 计时器的结束时间戳（毫秒），用于触发回调和计算剩余时间。
     *              可以是一个返回时间戳的函数，该函数在计时器启动时调用，接收当前时间中心的时间作为参数。
     * @default Infinity (永不结束)
     * @param {number} currentTime - 计时器启动时的时间中心当前时间。
     * @returns {number} 计时器的结束时间戳。
     * @example
     * // 1 分钟后结束
     * finishTime: (currentTime) => currentTime + 60000
     * // 在某个固定时间点结束
     * finishTime: () => 1678886460000
     */
    finishTime?: (currentTime: number) => number;
    /**
     * @description 当计时器到达 `finishTime` 时执行的操作。
     *              - 'stop': 停止计时器 (默认)。
     *              - 'restart': 重新启动计时器 (未实现)。
     *              - 'destroy': 清理计时器，相当于调用 `timer.destroy()`。
     * @default 'stop'
     */
    actionWhenTimeFinish?: 'stop' | 'restart' | 'destroy'; // 'restart' 功能当前未实现，保留以备将来扩展
    /**
     * @description 当计时器到达 `finishTime` 时触发的回调函数。
     */
    onFinished?: (currentTime: number) => void;
    /**
     * @description 计时器每次更新时触发的回调函数。
     */
    onTick?: (timer: Timer) => void;
}

/**
 * @interface Timer
 * @description 计时器实例接口，包含了计时器的状态、时间信息以及控制方法。
 * @extends Omit<RegisterTimeOptions, 'beginTime' | 'finishTime'>
 */
export interface Timer extends Omit<RegisterTimeOptions, 'beginTime' | 'finishTime'> {
    /**
     * @description 计时器的当前状态，只读。
     * @see TimerStatus
     */
    status: TimerStatus;
    /**
     * @description 计时器从 `beginTime` 开始到当前时间所经过的时间（已排除暂停时间 `idleTime`），响应式，只读。
     * @see FormatterTime
     */
    elapsedTime: FormatterTime;
    /**
     * @description 计时器的实际开始时间戳（毫秒）。
     */
    beginTime: number;
    /**
     * @description 计时器的实际结束时间戳（毫秒）。
     */
    finishTime: number;
    /**
     * @description 从当前时间到 `finishTime` 的剩余时间，响应式，只读。如果 `finishTime` 已过，则为 0。
     * @see FormatterTime
     */
    remainingTime: FormatterTime;
    /**
     * @description 计时器自启动以来累计的总暂停时间（毫秒）。
     */
    idleTime: number;
    /**
     * @description 当前暂停周期内累计的暂停时间（毫秒）。当计时器恢复 (`resume`) 时，此值会累加到 `idleTime` 并重置为 0。
     */
    currentIdleTime: number;
    /**
     * @description 启动或重新启动计时器。会先调用 `stop()` 清理当前状态，然后根据提供的选项 `o` 设置新的计时参数，并将状态设置为 `running`。
     * @param {RegisterTimeOptions} o - 启动计时器的配置选项。
     */
    start: (o: RegisterTimeOptions) => void;
    /**
     * @description 停止计时器。将状态设置为 `stopped`，重置 `currentIdleTime`、`elapsedTime` 和 `remainingTime`。
     *              停止后的计时器可以被重新 `start`。
     *              如果计时器已经是 `stopped` 状态，则此操作无效。
     */
    stop: () => void;
    /**
     * @description 销毁计时器。会停止计时器、清理所有回调和内部状态，并释放所有资源。
     *              销毁后，计时器将无法再使用，状态会变为 `destroyed`。
     */
    destroy: () => void;
    /**
     * @description 暂停计时器。将状态设置为 `suspended`。
     *              如果计时器不在 `running` 状态，则此操作无效。
     */
    pause: () => void;
    /**
     * @description 恢复计时器。将状态设置为 `running`。
     *              如果计时器不在 `suspended` 状态，则此操作无效。
     */
    resume: () => void;
}

/**
 * @interface TimeOptions
 * @description `useTimeCenter` Hook 的配置选项。
 */
export interface TimeOptions {
    /**
     * @description 与服务器同步时间的频率（毫秒）。仅在提供了 `syncTime` 函数时生效。
     * @default 5000
     */
    syncTimeFrequency?: number;
    /**
     * @description 用于从外部（例如服务器）获取当前标准时间的函数。返回值应为时间戳（毫秒）。
     *              如果提供此函数，`useTimeCenter` 会定期调用它来校准内部时间。
     * @returns {number | Promise<number>} 当前标准时间的时间戳或解析为时间戳的 Promise。
     */
    syncTime?: () => number | Promise<number>;
    /**
     * @description 外部传入的 requestAnimationFrame 实现，用于测试或特殊环境。
     * @default globalThis.requestAnimationFrame
     */
    requestAnimationFrame?: (callback: FrameRequestCallback) => number;
    /**
     * @description 外部传入的 cancelAnimationFrame 实现，用于测试或特殊环境。
     * @default globalThis.cancelAnimationFrame
     */
    cancelAnimationFrame?: (handle: number) => void;
}

/**
 * @interface TimeCenter
 * @description 时间中心实例接口，提供全局统一的时间源和管理功能。
 */
export interface TimeCenter {
    /**
     * @description 时间中心维护的当前时间戳（毫秒），响应式且只读。
     */
    readonly currentTime: Readonly<Ref<number>>;
    /**
     * @description 手动停止时间中心的内部更新循环 (`requestAnimationFrame`)。
     *              通常在 Vue 组件的 `onScopeDispose` 中自动调用，但在非组件环境或需要提前停止时手动调用。
     */
    stopUpdateTime: () => void;
    /**
     * @description 自上次计时器更新以来累积的时间（毫秒），响应式且只读。
     *              主要用于驱动 `useTimer` 的更新逻辑，避免过于频繁的计算。
     */
    readonly accumulatedTime: Readonly<Ref<number>>;
}

/**
 * @interface TimeManager
 * @description `createTimeManager` 函数的返回类型，包含时间中心实例、计时器工厂函数和重新配置函数。
 */
export interface TimeManager {
    /**
     * @description 时间中心实例，提供统一的时间源。注意：此属性在调用 `reconfigure` 后会返回新的实例。
     */
    readonly timeCenter: TimeCenter;
    /**
     * @description 创建计时器的工厂函数。返回的计时器会使用当前的时间中心实例。
     * @returns {Timer} 新创建的计时器实例。
     */
    createTimer: () => Timer;
    /**
     * @description 重新配置时间管理器。
     *              这将停止当前的时间中心，创建一个新的时间中心。
     *              已存在的计时器会继续使用旧的时间中心，新创建的计时器会使用新的配置。
     * @param {TimeOptions} [newOptions] - 新的时间中心配置，将与默认配置合并。
     */
    reconfigure: (newOptions?: TimeOptions) => void;
}
