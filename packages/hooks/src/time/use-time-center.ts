/* eslint-disable no-console */
import { readonly, ref, onScopeDispose, getCurrentScope, computed } from 'vue';
import { TimeCenter, TimeOptions } from './types';

/**
 * @function useTimeCenter
 * @description 创建并管理一个全局时间中心实例。
 *              提供统一的、可校准的当前时间 (`currentTime`) 和时间累积 (`accumulatedTime`)。
 *              时间更新通过 `requestAnimationFrame` 实现，以优化性能。
 *              支持通过 `syncTime` 选项与外部时间源同步。
 * @param {TimeOptions} [options] - 时间中心的配置选项。
 * @see TimeOptions
 * @see TimeCenter
 * @example
 * // 基本用法
 * const timeCenter = useTimeCenter();
 * watch(timeCenter.currentTime, (newTime) => {
 *   console.log('Current time:', newTime);
 * });
 *
 * // 使用服务器时间同步
 * const timeCenterWithSync = useTimeCenter({
 *   syncTime: async () => {
 *     const response = await fetch('/api/server-time');
 *     const data = await response.json();
 *     return data.timestamp;
 *   },
 *   syncTimeFrequency: 10000 // 每 10 秒同步一次
 * });
 */
export function useTimeCenter(options?: TimeOptions): TimeCenter {
    const {
        syncTimeFrequency = 5000,
        syncTime,
        requestAnimationFrame: raf = globalThis.requestAnimationFrame,
        cancelAnimationFrame: caf = globalThis.cancelAnimationFrame,
    } = options || {};

    const isSyncEnabled = typeof syncTime === 'function';

    let frameId: number | null = null;
    let lastPerformanceTime = 0;
    let lastSyncPerformanceTime = 0;
    let isInitialSyncDone = false;
    let isStopped = false;
    let isSyncing = false;

    const accumulatedTime = ref<number>(0);

    // 记录初始时间，用于计算基于真实时间的偏移
    const initialRealTime = Date.now();

    const serverTime = ref(isSyncEnabled ? 0 : initialRealTime);
    const currentTime = computed(() => {
        // `Date.now()` is not a reactive source, causing `currentTime` to fail to update
        // when `syncTime` is not provided. The logic is now unified to rely on `accumulatedTime`.
        return serverTime.value + accumulatedTime.value;
    });

    const performSync = async (timestamp: number) => {
        if (!isSyncEnabled || isSyncing) return;

        isSyncing = true;
        lastSyncPerformanceTime = timestamp;

        try {
            const serverTimestamp = await syncTime();

            // 验证服务器返回的时间戳有效性
            if (typeof serverTimestamp !== 'number' || !isFinite(serverTimestamp) || isNaN(serverTimestamp)) {
                console.warn('服务器返回无效时间戳:', serverTimestamp);
                return;
            }

            serverTime.value = serverTimestamp;
            accumulatedTime.value = 0;
            lastPerformanceTime = timestamp;
            isInitialSyncDone = true; // 标记初始同步完成
        } catch (error) {
            console.warn('服务器时间同步失败:', error);
            isInitialSyncDone = true; // 即使失败也标记初始同步尝试完成
        } finally {
            isSyncing = false;
        }
    };

    const updateTime = async () => {
        if (isStopped) return;

        const now = performance.now();
        const frameInterval = lastPerformanceTime > 0 ? now - lastPerformanceTime : 0;
        const timeSinceLastSync = now - lastSyncPerformanceTime;

        // 判断是否需要与服务器同步时间
        const shouldSync = isSyncEnabled && !isSyncing && timeSinceLastSync >= syncTimeFrequency;

        // 如果还没完成初始同步且有 syncTime，则不进行后续同步判断
        if (!isInitialSyncDone && isSyncEnabled) {
            // 初始同步可能正在进行中，不做额外处理
        } else if (shouldSync) {
            await performSync(now);
            if (isStopped) return; // 检查是否在同步期间被停止
        }

        // 累积时间更新逻辑
        if (!shouldSync) {
            // 使用真实的帧间隔累积时间
            accumulatedTime.value += frameInterval;
            lastPerformanceTime = now;
        }

        // 首先安排下一次更新，确保frameId被设置
        if (typeof raf === 'function') {
            frameId = raf(updateTime);
        } else {
            // 如果 raf 不可用，则停止更新循环
            stopUpdateTime();
            return;
        }
    };

    // 手动停止更新
    const stopUpdateTime = () => {
        isStopped = true;
        if (frameId !== null && typeof caf === 'function') {
            caf(frameId);
            frameId = null;
        }
    };

    // 初始化时设置 lastPerformanceTime
    lastPerformanceTime = performance.now();

    // 如果提供了 syncTime，立即触发初始同步（不等待结果）
    if (isSyncEnabled) {
        performSync(lastPerformanceTime);
    } else {
        // 如果没有 syncTime，立即标记初始同步已完成
        isInitialSyncDone = true;
    }

    // 总是启动 updateTime 循环
    if (typeof raf === 'function') {
        updateTime();
    }

    // 如果在 effect scope (例如组件 setup) 中，自动清理
    if (getCurrentScope()) {
        onScopeDispose(stopUpdateTime);
    }

    return { accumulatedTime: readonly(accumulatedTime), currentTime: readonly(currentTime), stopUpdateTime };
}
