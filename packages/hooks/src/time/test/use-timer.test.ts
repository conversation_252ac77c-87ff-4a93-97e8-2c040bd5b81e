import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { ref, nextTick } from 'vue';
import { useTimer } from '../use-timer';
import { TimerStatus, type RegisterTimeOptions } from '../types';

describe('useTimer', () => {
    let mockCurrentTime: any;

    beforeEach(() => {
        vi.useFakeTimers();
        mockCurrentTime = ref(1000000); // 使用固定的起始时间
        vi.clearAllMocks();

        // Mock performance.now to be consistent with tests
        if (!global.performance) {
            Object.defineProperty(global, 'performance', {
                value: { now: vi.fn(() => 1000000) },
                configurable: true,
                writable: true,
            });
        } else {
            global.performance.now = vi.fn(() => 1000000);
        }
    });

    afterEach(() => {
        vi.useRealTimers();
    });

    describe('基本功能', () => {
        it('应该创建计时器实例', () => {
            const timer = useTimer({ currentTime: mockCurrentTime });

            expect(timer).toBeDefined();
            expect(timer.status).toBe(TimerStatus.idled);
            expect(timer.elapsedTime.total).toBe(0);
            expect(timer.remainingTime.total).toBe(0);
            expect(timer.idleTime).toBe(0);
            expect(timer.currentIdleTime).toBe(0);
        });

        it('应该正确启动计时器', async () => {
            const timer = useTimer({ currentTime: mockCurrentTime });
            const onTick = vi.fn();

            timer.start({
                beginTime: () => mockCurrentTime.value,
                finishTime: () => mockCurrentTime.value + 5000,
                onTick,
            });

            expect(timer.status).toBe(TimerStatus.running);
            expect(timer.beginTime).toBe(mockCurrentTime.value);
            expect(timer.finishTime).toBe(mockCurrentTime.value + 5000);
        });

        it('应该正确停止计时器', () => {
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({});
            timer.stop();

            expect(timer.status).toBe(TimerStatus.stopped);
            expect(timer.currentIdleTime).toBe(0);
            expect(timer.elapsedTime.total).toBe(0);
            expect(timer.remainingTime.total).toBe(0);
        });

        it('应该正确暂停和恢复计时器', async () => {
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => mockCurrentTime.value,
                finishTime: () => mockCurrentTime.value + 5000,
            });

            // 暂停
            timer.pause();
            expect(timer.status).toBe(TimerStatus.suspended);

            // 模拟时间流逝
            mockCurrentTime.value += 1000;
            await nextTick();

            // 恢复
            timer.resume();
            expect(timer.status).toBe(TimerStatus.running);
        });

        it('应该正确销毁计时器', () => {
            const timer = useTimer({ currentTime: mockCurrentTime });
            const onTick = vi.fn();
            const onFinished = vi.fn();

            timer.start({
                onTick,
                onFinished,
            });

            timer.destroy();

            expect(timer.status).toBe(TimerStatus.destroyed);
            expect(timer.onTick).toBeUndefined();
            expect(timer.onFinished).toBeUndefined();
            expect(timer.elapsedTime.total).toBeNaN();
            expect(timer.remainingTime.total).toBeNaN();
        });
    });

    describe('时间计算', () => {
        it('应该正确计算已过时间', async () => {
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => mockCurrentTime.value,
            });

            // 模拟时间流逝 2 秒
            mockCurrentTime.value += 2000;
            await nextTick();

            expect(timer.elapsedTime.total).toBe(2000);
            expect(timer.elapsedTime.seconds).toBe('02');
        });

        it('应该正确计算剩余时间', async () => {
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => mockCurrentTime.value,
                finishTime: () => mockCurrentTime.value + 5000,
            });

            // 模拟时间流逝 2 秒
            mockCurrentTime.value += 2000;
            await nextTick();

            expect(timer.remainingTime.total).toBe(3000);
            expect(timer.remainingTime.seconds).toBe('03');
        });

        it('应该在暂停时停止时间计算', async () => {
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => mockCurrentTime.value,
                finishTime: () => mockCurrentTime.value + 5000,
            });

            // 运行 1 秒
            mockCurrentTime.value += 1000;
            await nextTick();
            const elapsedBeforePause = timer.elapsedTime.total;

            // 暂停
            timer.pause();

            // 再流逝 2 秒
            mockCurrentTime.value += 2000;
            await nextTick();

            // 已过时间应该没有增加，但结束时间会延后
            expect(timer.elapsedTime.total).toBe(elapsedBeforePause);
            expect(timer.idleTime).toBe(2000);
            // 原始结束时间 + 暂停时间 = 1005000 + 2000 = 1007000
            expect(timer.finishTime).toBe(1007000);
        });
    });

    describe('回调函数', () => {
        it('应该在每次tick时调用onTick', async () => {
            const timer = useTimer({ currentTime: mockCurrentTime });
            const onTick = vi.fn();

            timer.start({
                onTick,
            });

            mockCurrentTime.value += 100;
            await nextTick();

            expect(onTick).toHaveBeenCalledWith(timer);
        });

        it('应该在完成时调用onFinished', async () => {
            const timer = useTimer({ currentTime: mockCurrentTime });
            const onFinished = vi.fn();

            timer.start({
                beginTime: () => mockCurrentTime.value,
                finishTime: () => mockCurrentTime.value + 1000,
                onFinished,
            });

            // 超过结束时间
            mockCurrentTime.value += 1500;
            await nextTick();

            expect(onFinished).toHaveBeenCalledWith(mockCurrentTime.value);
            expect(timer.status).toBe(TimerStatus.stopped);
        });

        it('应该处理回调函数中的错误', async () => {
            const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
            const timer = useTimer({ currentTime: mockCurrentTime });
            const onTick = vi.fn().mockImplementation(() => {
                throw new Error('Callback error');
            });

            timer.start({
                onTick,
            });

            mockCurrentTime.value += 100;
            await nextTick();

            expect(consoleSpy).toHaveBeenCalledWith('useTimer: 回调函数执行错误:', expect.any(Error));

            consoleSpy.mockRestore();
        });

        it('应该防止回调函数重入', async () => {
            const timer = useTimer({ currentTime: mockCurrentTime });
            const onTick = vi.fn();

            timer.start({
                onTick,
            });

            // 正常调用一次
            mockCurrentTime.value += 100;
            await nextTick();

            // 验证回调被调用了一次
            expect(onTick).toHaveBeenCalledTimes(1);

            // 重入保护主要是防止在回调执行期间同步触发的额外调用
            // 在正常的异步更新中，不会出现真正的重入问题
        });
    });

    describe('结束动作', () => {
        it('应该在结束时执行stop动作', async () => {
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => mockCurrentTime.value,
                finishTime: () => mockCurrentTime.value + 1000,
                actionWhenTimeFinish: 'stop',
            });

            mockCurrentTime.value += 1500;
            await nextTick();

            expect(timer.status).toBe(TimerStatus.stopped);
        });

        it('应该在结束时执行destroy动作', async () => {
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => mockCurrentTime.value,
                finishTime: () => mockCurrentTime.value + 1000,
                actionWhenTimeFinish: 'destroy',
            });

            mockCurrentTime.value += 1500;
            await nextTick();

            expect(timer.status).toBe(TimerStatus.destroyed);
        });
    });

    describe('边界情况和错误处理', () => {
        it('应该处理无效的beginTime', () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => NaN,
            });

            expect(timer.beginTime).toBe(mockCurrentTime.value);
            expect(consoleSpy).toHaveBeenCalledWith('useTimer: Invalid beginTime, using currentTime as fallback');

            consoleSpy.mockRestore();
        });

        it('应该处理无效的finishTime', () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                finishTime: () => NaN,
            });

            expect(timer.finishTime).toBe(Infinity);
            expect(consoleSpy).toHaveBeenCalledWith('useTimer: Invalid finishTime, using Infinity as fallback');

            consoleSpy.mockRestore();
        });

        it('应该处理finishTime小于beginTime的情况', () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => mockCurrentTime.value + 1000,
                finishTime: () => mockCurrentTime.value,
            });

            expect(consoleSpy).toHaveBeenCalledWith('useTimer: finishTime should be greater than beginTime');

            consoleSpy.mockRestore();
        });

        it('应该处理时间计算函数抛出错误', () => {
            const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => {
                    throw new Error('Calculation error');
                },
            });

            expect(timer.beginTime).toBe(mockCurrentTime.value);
            expect(timer.finishTime).toBe(Infinity);
            expect(consoleSpy).toHaveBeenCalledWith('useTimer: Error calculating timer times:', expect.any(Error));

            consoleSpy.mockRestore();
        });

        it('不应该启动已销毁的计时器', () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.destroy();
            timer.start({});

            expect(timer.status).toBe(TimerStatus.destroyed);
            expect(consoleSpy).toHaveBeenCalledWith('useTimer: Cannot start a destroyed timer.');

            consoleSpy.mockRestore();
        });

        it('应该忽略对已停止计时器的重复停止操作', () => {
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({});
            timer.stop();
            const firstStopStatus = timer.status;

            timer.stop();

            expect(timer.status).toBe(firstStopStatus);
        });

        it('应该忽略对已销毁计时器的重复销毁操作', () => {
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.destroy();
            timer.destroy();

            expect(timer.status).toBe(TimerStatus.destroyed);
        });

        it('应该忽略对非运行状态计时器的暂停操作', () => {
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.pause();

            expect(timer.status).toBe(TimerStatus.idled);
        });

        it('应该忽略对非暂停状态计时器的恢复操作', () => {
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.resume();

            expect(timer.status).toBe(TimerStatus.idled);
        });
    });

    describe('时间格式化', () => {
        it('应该正确格式化正常时间', async () => {
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => mockCurrentTime.value,
            });

            // 流逝 1小时2分3秒456毫秒
            const elapsed = 1 * 60 * 60 * 1000 + 2 * 60 * 1000 + 3 * 1000 + 456;
            mockCurrentTime.value += elapsed;
            await nextTick();

            expect(timer.elapsedTime.hours).toBe('01');
            expect(timer.elapsedTime.minutes).toBe('02');
            expect(timer.elapsedTime.seconds).toBe('03');
            expect(timer.elapsedTime.milliseconds).toBe('456');
        });

        it('应该正确处理负数时间', async () => {
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => mockCurrentTime.value + 1000, // 开始时间在未来
            });

            expect(timer.elapsedTime.total).toBeLessThan(0);
            expect(timer.elapsedTime.hours).toMatch(/^-/);
        });

        it('应该正确处理无限时间', async () => {
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => mockCurrentTime.value,
                finishTime: () => Infinity,
            });

            expect(timer.remainingTime.total).toBe(Infinity);
            expect(timer.remainingTime.hours).toBe('Infinity');
            expect(timer.remainingTime.minutes).toBe('Infinity');
            expect(timer.remainingTime.seconds).toBe('Infinity');
            expect(timer.remainingTime.milliseconds).toBe('Infinity');
        });
    });

    describe('状态管理', () => {
        it('应该正确处理状态转换', () => {
            const timer = useTimer({ currentTime: mockCurrentTime });

            // 初始状态
            expect(timer.status).toBe(TimerStatus.idled);

            // 启动
            timer.start({});
            expect(timer.status).toBe(TimerStatus.running);

            // 暂停
            timer.pause();
            expect(timer.status).toBe(TimerStatus.suspended);

            // 恢复
            timer.resume();
            expect(timer.status).toBe(TimerStatus.running);

            // 停止
            timer.stop();
            expect(timer.status).toBe(TimerStatus.stopped);

            // 重新启动
            timer.start({});
            expect(timer.status).toBe(TimerStatus.running);

            // 销毁
            timer.destroy();
            expect(timer.status).toBe(TimerStatus.destroyed);
        });
    });

    describe('配置选项', () => {
        it('应该保存和使用所有配置选项', () => {
            const timer = useTimer({ currentTime: mockCurrentTime });
            const options: RegisterTimeOptions = {
                key: 'test-timer',
                actionWhenTimeFinish: 'destroy',
                onFinished: vi.fn(),
                onTick: vi.fn(),
            };

            timer.start(options);

            expect(timer.key).toBe(options.key);
            expect(timer.actionWhenTimeFinish).toBe(options.actionWhenTimeFinish);
            expect(timer.onFinished).toBe(options.onFinished);
            expect(timer.onTick).toBe(options.onTick);
        });
    });
});
