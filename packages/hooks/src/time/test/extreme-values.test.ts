import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { ref, nextTick } from 'vue';
import { useTimer } from '../use-timer';
import { TimerStatus } from '../types';

describe('极端值和边界条件测试', () => {
    beforeEach(() => {
        vi.useFakeTimers();
        vi.clearAllMocks();

        global.performance = global.performance || {};
        global.performance.now = vi.fn(() => Date.now());
    });

    afterEach(() => {
        vi.useRealTimers();
        vi.restoreAllMocks();
    });

    describe('极端时间戳测试', () => {
        it('应该处理接近JavaScript最大安全整数的时间戳', async () => {
            const largeTimestamp = Number.MAX_SAFE_INTEGER - 1000;
            const mockCurrentTime = ref(largeTimestamp);
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => largeTimestamp,
                finishTime: () => largeTimestamp + 5000,
            });

            expect(timer.status).toBe(TimerStatus.running);
            expect(timer.beginTime).toBe(largeTimestamp);
            expect(timer.finishTime).toBe(largeTimestamp + 5000);
        });

        it('应该处理非常小的时间戳', async () => {
            const smallTimestamp = 1;
            const mockCurrentTime = ref(smallTimestamp);
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => smallTimestamp,
                finishTime: () => smallTimestamp + 1000,
            });

            expect(timer.status).toBe(TimerStatus.running);
            expect(timer.beginTime).toBe(smallTimestamp);
        });

        it('应该处理零时间戳', async () => {
            const mockCurrentTime = ref(0);
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => 0,
                finishTime: () => 1000,
            });

            expect(timer.status).toBe(TimerStatus.running);
            expect(timer.beginTime).toBe(0);
        });
    });

    describe('微小时间间隔测试', () => {
        it('应该处理毫秒级的精确计时', async () => {
            const mockCurrentTime = ref(1000000);
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => mockCurrentTime.value,
                finishTime: () => mockCurrentTime.value + 1, // 1毫秒
            });

            // 推进1毫秒
            mockCurrentTime.value += 1;
            await nextTick();

            expect(timer.status).toBe(TimerStatus.stopped);
        });

        it('应该处理亚毫秒级的时间差', async () => {
            const mockCurrentTime = ref(1000000.5);
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => mockCurrentTime.value,
            });

            mockCurrentTime.value += 0.1; // 0.1毫秒
            await nextTick();

            expect(timer.elapsedTime.total).toBeCloseTo(0.1, 1);
        });
    });

    describe('浮点数精度测试', () => {
        it('应该正确处理浮点数累积误差', async () => {
            const mockCurrentTime = ref(1000000);
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => mockCurrentTime.value,
            });

            // 累积多次小的浮点数增量
            for (let i = 0; i < 1000; i++) {
                mockCurrentTime.value += 0.1;
            }
            await nextTick();

            // 应该接近100毫秒，允许一定的浮点数误差
            expect(timer.elapsedTime.total).toBeCloseTo(100, 0);
        });

        it('应该处理非整数的时间间隔', async () => {
            const mockCurrentTime = ref(1000000.123);
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => mockCurrentTime.value,
                finishTime: () => mockCurrentTime.value + 1500.456,
            });

            mockCurrentTime.value += 500.789;
            await nextTick();

            expect(timer.elapsedTime.total).toBeCloseTo(500.789, 2);
            expect(timer.remainingTime.total).toBeCloseTo(999.667, 2);
        });
    });

    describe('特殊数值测试', () => {
        it('应该处理负零时间戳', async () => {
            const mockCurrentTime = ref(-0);
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => -0,
                finishTime: () => 1000,
            });

            expect(timer.status).toBe(TimerStatus.running);
            expect(Object.is(timer.beginTime, -0)).toBe(true);
        });

        it('应该处理非常接近的开始和结束时间', async () => {
            const mockCurrentTime = ref(1000000);
            const timer = useTimer({ currentTime: mockCurrentTime });

            const beginTime = mockCurrentTime.value;
            const finishTime = beginTime + Number.EPSILON;

            timer.start({
                beginTime: () => beginTime,
                finishTime: () => finishTime,
            });

            expect(timer.status).toBe(TimerStatus.running);
            expect(timer.finishTime - timer.beginTime).toBeCloseTo(Number.EPSILON);
        });

        it('应该处理时间戳溢出情况', () => {
            const mockCurrentTime = ref(Number.MAX_SAFE_INTEGER);
            const timer = useTimer({ currentTime: mockCurrentTime });

            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

            timer.start({
                beginTime: () => Number.MAX_SAFE_INTEGER,
                finishTime: () => Number.MAX_SAFE_INTEGER + 1000, // 可能溢出
            });

            // 应该能正常工作，或至少不崩溃
            expect(timer.status).toBe(TimerStatus.running);

            consoleSpy.mockRestore();
        });
    });

    describe('格式化极端值测试', () => {
        it('应该正确格式化超长时间', async () => {
            const mockCurrentTime = ref(1000000);
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => mockCurrentTime.value,
            });

            // 10000小时 = 10000 * 60 * 60 * 1000 = 36,000,000,000毫秒
            const hugeDelta = 10000 * 60 * 60 * 1000;
            mockCurrentTime.value += hugeDelta;
            await nextTick();

            expect(timer.elapsedTime.hours).toBe('10000');
            expect(timer.elapsedTime.minutes).toBe('00');
            expect(timer.elapsedTime.seconds).toBe('00');
        });

        it('应该处理非常小的毫秒值', async () => {
            const mockCurrentTime = ref(1000000);
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => mockCurrentTime.value,
            });

            mockCurrentTime.value += 0.001; // 非常小的增量
            await nextTick();

            expect(timer.elapsedTime.total).toBeCloseTo(0.001, 3);
            expect(timer.elapsedTime.milliseconds).toBe('000');
        });
    });
});
