import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { nextTick } from 'vue';
import { createTimeManager } from '../create-time-manager';
import { TimerStatus } from '../types';

const raf = vi.fn((callback: FrameRequestCallback) => {
    const id = setTimeout(() => callback(performance.now()), 16);
    return id as unknown as number;
});

const caf = vi.fn((id: number) => {
    clearTimeout(id);
});

describe('时间模块并发测试', () => {
    beforeEach(() => {
        vi.useFakeTimers();
        vi.clearAllMocks();
        raf.mockClear();
        caf.mockClear();

        global.performance = global.performance || {};
        global.performance.now = vi.fn(() => Date.now());
    });

    afterEach(() => {
        vi.useRealTimers();
        vi.restoreAllMocks();
    });

    describe('竞态条件测试', () => {
        it('应该处理多个计时器同时启动时的竞态条件', async () => {
            const { createTimer } = createTimeManager();
            const timers = Array.from({ length: 10 }, () => createTimer());
            const now = Date.now();
            // 同时启动所有计时器
            const startPromises = timers.map((timer, index) =>
                Promise.resolve().then(() => {
                    timer.start({
                        beginTime: () => now + index * 100,
                        finishTime: () => now + 5000 + index * 100,
                    });
                }),
            );

            await Promise.all(startPromises);
            await nextTick();

            // 验证所有计时器都正确启动
            timers.forEach((timer) => {
                expect(timer.status).toBe(TimerStatus.running);
            });
        });

        it('应该处理在回调函数中修改计时器状态的场景', async () => {
            const { timeCenter, createTimer } = createTimeManager({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });
            const timer1 = createTimer();
            const timer2 = createTimer();

            let callbackExecuted = false;
            const now = Date.now();
            timer1.start({
                beginTime: () => now,
                onTick: () => {
                    if (!callbackExecuted) {
                        callbackExecuted = true;
                        // 在回调中启动另一个计时器
                        timer2.start({
                            beginTime: () => Date.now(),
                        });
                        // 尝试修改当前计时器状态
                        timer1.pause();
                    }
                },
            });

            await vi.advanceTimersByTimeAsync(100);
            await nextTick();
            await vi.runOnlyPendingTimersAsync();
            await nextTick();

            expect(timer1.status).toBe(TimerStatus.suspended);
            expect(timer2.status).toBe(TimerStatus.running);
            timeCenter.stopUpdateTime();
        });

        it('应该处理快速连续的状态变更', () => {
            const { createTimer } = createTimeManager();
            const timer = createTimer();

            // 快速连续的状态操作
            timer.start({});
            timer.pause();
            timer.resume();
            timer.pause();
            timer.resume();
            timer.stop();
            timer.start({});

            expect(timer.status).toBe(TimerStatus.running);
        });
    });

    describe('重入保护测试', () => {
        it('应该防止异步 onTick 回调的重入', async () => {
            const { timeCenter, createTimer } = createTimeManager({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });
            const timer = createTimer();
            let resolveTick: (() => void) | null = null;

            const onTick = vi.fn(async () => {
                await new Promise<void>((resolve) => {
                    resolveTick = resolve;
                });
            });

            timer.start({ onTick });

            // 第一次 tick, 触发 onTick
            await vi.advanceTimersByTimeAsync(20);
            await nextTick();

            expect(onTick).toHaveBeenCalledTimes(1);

            // 在第一次 onTick 完成前, 再次推进时间
            await vi.advanceTimersByTimeAsync(20);
            await nextTick();

            // onTick 不应该被重入
            expect(onTick).toHaveBeenCalledTimes(1);

            // 完成第一次 onTick
            (resolveTick as unknown as () => void)?.();
            await nextTick(); // 等待异步 onTick 的 promise 在微任务队列中完成

            // 再次推进时间, 应该可以触发下一次 onTick
            await vi.advanceTimersByTimeAsync(20);
            await nextTick();

            expect(onTick).toHaveBeenCalledTimes(2);

            timeCenter.stopUpdateTime();
        });

        it('应该防止onFinished回调的重入', async () => {
            const { timeCenter, createTimer } = createTimeManager({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });
            const timer = createTimer();
            const now = Date.now();
            let isInsideOnFinished = false;
            let reEntryDetected = false;

            const onFinished = vi.fn(() => {
                if (isInsideOnFinished) {
                    reEntryDetected = true;
                    return;
                }
                isInsideOnFinished = true;
                // 尝试重新启动计时器，在重入保护下，这不应导致问题
                timer.start({
                    finishTime: () => Date.now() + 1000,
                });
                isInsideOnFinished = false;
            });

            timer.start({
                beginTime: () => now,
                finishTime: () => now + 100,
                onFinished,
            });

            // 超过结束时间
            await vi.advanceTimersByTimeAsync(500);
            await nextTick();
            await vi.runOnlyPendingTimersAsync();
            await nextTick();

            expect(reEntryDetected).toBe(false);
            expect(onFinished).toHaveBeenCalledTimes(1);
            timeCenter.stopUpdateTime();
        });
    });

    describe('资源竞争测试', () => {
        it('应该处理多个计时器同时销毁的场景', () => {
            const { createTimer } = createTimeManager();
            const timers = Array.from({ length: 100 }, () => createTimer());

            // 启动所有计时器
            timers.forEach((timer) => timer.start({}));

            // 同时销毁所有计时器
            timers.forEach((timer) => timer.destroy());

            // 验证所有计时器都被正确销毁
            timers.forEach((timer) => {
                expect(timer.status).toBe(TimerStatus.destroyed);
            });
        });

        it('应该处理时间中心停止时计时器的状态', () => {
            const { timeCenter, createTimer } = createTimeManager();
            const timers = Array.from({ length: 5 }, () => createTimer());

            timers.forEach((timer) => timer.start({}));

            // 停止时间中心
            timeCenter.stopUpdateTime();

            // 计时器应该继续保持运行状态
            timers.forEach((timer) => {
                expect(timer.status).toBe(TimerStatus.running);
            });
        });
    });
});
