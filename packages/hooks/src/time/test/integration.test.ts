import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { nextTick } from 'vue';
import { createTimeManager } from '../create-time-manager';
import { useTimeCenter } from '../use-time-center';
import { TimerStatus } from '../types';

const raf = (callback: FrameRequestCallback) => {
    const id = setTimeout(() => callback(performance.now()), 16);
    return id as unknown as number;
};

const caf = vi.fn((id: number) => {
    clearTimeout(id);
});

describe('时间模块集成测试', () => {
    beforeEach(() => {
        vi.useFakeTimers();
        vi.clearAllMocks();
        caf.mockClear();

        // Mock performance.now
        global.performance = global.performance || {};
        global.performance.now = vi.fn(() => Date.now());
    });

    afterEach(() => {
        vi.useRealTimers();
        vi.restoreAllMocks();
    });

    describe('完整使用场景', () => {
        it('应该支持创建多个计时器完成复杂的时间管理', async () => {
            const { createTimer } = createTimeManager();

            // 创建三个不同目的的计时器
            const examTimer = createTimer();
            const questionTimer = createTimer();
            const reminderTimer = createTimer();

            // 启动考试计时器 (30 分钟)
            examTimer.start({
                finishTime: () => Date.now() + 30 * 60 * 1000,
            });

            // 启动题目计时器 (15 秒)
            questionTimer.start({
                finishTime: () => Date.now() + 15 * 1000,
            });

            // 启动提醒计时器 (25 分钟)
            reminderTimer.start({
                finishTime: () => Date.now() + 25 * 60 * 1000,
            });

            // 验证所有计时器都在运行
            expect(examTimer.status).toBe(TimerStatus.running);
            expect(questionTimer.status).toBe(TimerStatus.running);
            expect(reminderTimer.status).toBe(TimerStatus.running);

            // 验证计时器具有不同的结束时间
            expect(examTimer.finishTime).toBeGreaterThan(questionTimer.finishTime);
            expect(reminderTimer.finishTime).toBeGreaterThan(questionTimer.finishTime);
        });

        it('应该支持暂停和恢复整个时间系统', async () => {
            const { createTimer } = createTimeManager();

            const timer1 = createTimer();
            const timer2 = createTimer();

            timer1.start({});
            timer2.start({});

            expect(timer1.status).toBe(TimerStatus.running);
            expect(timer2.status).toBe(TimerStatus.running);

            // 暂停所有计时器
            timer1.pause();
            timer2.pause();

            expect(timer1.status).toBe(TimerStatus.suspended);
            expect(timer2.status).toBe(TimerStatus.suspended);

            // 恢复所有计时器
            timer1.resume();
            timer2.resume();

            expect(timer1.status).toBe(TimerStatus.running);
            expect(timer2.status).toBe(TimerStatus.running);
        });
    });

    describe('服务器时间同步场景', () => {
        it('应该在时间同步后正确调整所有计时器', async () => {
            let serverTime = 2000000;
            const syncTime = vi.fn().mockImplementation(() => Promise.resolve(serverTime));

            const { timeCenter, createTimer } = createTimeManager({
                syncTime,
                syncTimeFrequency: 1000,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            const timer = createTimer();

            // 触发初始同步
            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            timer.start({});

            expect(syncTime).toHaveBeenCalled();
            expect(timer.beginTime).toBeGreaterThan(0);

            // 清理
            timeCenter.stopUpdateTime();
        });

        it('应该在网络错误时继续正常工作', async () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

            let callCount = 0;
            const syncTime = vi.fn().mockImplementation(() => {
                callCount++;
                if (callCount === 2) {
                    return Promise.reject(new Error('Network error'));
                }
                return Promise.resolve(2000000 + callCount * 1000);
            });

            const { timeCenter, createTimer } = createTimeManager({
                syncTime,
                syncTimeFrequency: 1000,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            const timer = createTimer();

            // 触发多次同步
            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            timer.start({});

            expect(timer.status).toBe(TimerStatus.running);

            timeCenter.stopUpdateTime();
            consoleSpy.mockRestore();
        });
    });

    describe('性能和内存管理', () => {
        it('应该能够处理大量计时器的创建和销毁', () => {
            const { createTimer } = createTimeManager();
            const timers = [];

            // 创建大量计时器
            for (let i = 0; i < 100; i++) {
                timers.push(createTimer());
            }

            // 启动一半计时器
            for (let i = 0; i < 50; i++) {
                timers[i]?.start({});
            }

            // 销毁另一半计时器
            for (let i = 50; i < 100; i++) {
                timers[i]?.destroy();
            }

            // 验证状态
            for (let i = 0; i < 50; i++) {
                expect(timers[i]?.status).toBe(TimerStatus.running);
            }
            for (let i = 50; i < 100; i++) {
                expect(timers[i]?.status).toBe(TimerStatus.destroyed);
            }
        });

        it('应该正确清理时间中心资源', () => {
            const timeCenter = useTimeCenter({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            timeCenter.stopUpdateTime();

            expect(caf).toHaveBeenCalled();
        });
    });

    describe('错误恢复和健壮性', () => {
        it('应该从回调函数错误中恢复并继续运行', async () => {
            const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
            const { timeCenter, createTimer } = createTimeManager({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            let callCount = 0;
            const onTick = vi.fn(() => {
                callCount++;
                if (callCount === 1) {
                    throw new Error('Callback error');
                }
            });

            const timer = createTimer();
            timer.start({ onTick });

            // 第一次 tick, 应该会抛出错误
            await vi.advanceTimersByTimeAsync(20);
            await nextTick();

            expect(onTick).toHaveBeenCalledTimes(1);
            expect(consoleSpy).toHaveBeenCalledWith('useTimer: 回调函数执行错误:', expect.any(Error));
            expect(timer.status).toBe(TimerStatus.running); // 确保计时器仍在运行

            // 第二次 tick, 应该能正常执行
            await vi.advanceTimersByTimeAsync(20);
            await nextTick();

            expect(onTick).toHaveBeenCalledTimes(2);
            expect(callCount).toBe(2);
            expect(timer.status).toBe(TimerStatus.running);

            consoleSpy.mockRestore();
            timeCenter.stopUpdateTime();
        });

        it('应该处理无效的计时器操作序列', () => {
            const { createTimer } = createTimeManager();
            const timer = createTimer();

            // 在未启动时暂停
            timer.pause();
            expect(timer.status).toBe(TimerStatus.idled);

            // 在未启动时恢复
            timer.resume();
            expect(timer.status).toBe(TimerStatus.idled);

            // 启动后销毁
            timer.start({});
            timer.destroy();
            expect(timer.status).toBe(TimerStatus.destroyed);

            // 在销毁后尝试启动
            timer.start({});
            expect(timer.status).toBe(TimerStatus.destroyed);
        });
    });

    describe('真实世界使用场景模拟', () => {
        it('模拟在线考试系统的时间管理', async () => {
            const { createTimer } = createTimeManager();

            const events = {
                questionFinished: vi.fn(),
                reminderTriggered: vi.fn(),
                examFinished: vi.fn(),
            };

            // 创建考试主计时器 (60 分钟)
            const examTimer = createTimer();
            examTimer.start({
                finishTime: () => Date.now() + 60 * 60 * 1000,
                onFinished: events.examFinished,
            });

            // 创建题目计时器 (5 分钟)
            const questionTimer = createTimer();
            questionTimer.start({
                finishTime: () => Date.now() + 5 * 60 * 1000,
                onFinished: events.questionFinished,
            });

            // 创建提醒计时器 (4.5 分钟后提醒)
            const reminderTimer = createTimer();
            reminderTimer.start({
                finishTime: () => Date.now() + 4.5 * 60 * 1000,
                onFinished: events.reminderTriggered,
            });

            // 验证初始状态
            expect(examTimer.status).toBe(TimerStatus.running);
            expect(questionTimer.status).toBe(TimerStatus.running);
            expect(reminderTimer.status).toBe(TimerStatus.running);

            // 模拟暂停考试 (网络问题)
            examTimer.pause();
            questionTimer.pause();
            reminderTimer.pause();

            expect(examTimer.status).toBe(TimerStatus.suspended);
            expect(questionTimer.status).toBe(TimerStatus.suspended);
            expect(reminderTimer.status).toBe(TimerStatus.suspended);

            // 恢复考试
            examTimer.resume();
            questionTimer.resume();
            reminderTimer.resume();

            expect(examTimer.status).toBe(TimerStatus.running);
            expect(questionTimer.status).toBe(TimerStatus.running);
            expect(reminderTimer.status).toBe(TimerStatus.running);
        });

        it('模拟体育比赛计时系统', async () => {
            const { createTimer } = createTimeManager();

            const events = {
                gameFinished: vi.fn(),
                shotClockExpired: vi.fn(),
                timeoutCalled: vi.fn(),
            };

            // 比赛主计时器 (48 分钟)
            const gameTimer = createTimer();
            gameTimer.start({
                finishTime: () => Date.now() + 48 * 60 * 1000,
                onFinished: events.gameFinished,
            });

            // 进攻时间计时器 (24 秒)
            let shotClockTimer = createTimer();
            const startShotClock = () => {
                shotClockTimer = createTimer();
                shotClockTimer.start({
                    finishTime: () => Date.now() + 24 * 1000,
                    onFinished: events.shotClockExpired,
                });
            };

            startShotClock();

            // 验证初始状态
            expect(gameTimer.status).toBe(TimerStatus.running);
            expect(shotClockTimer.status).toBe(TimerStatus.running);

            // 模拟暂停比赛 (教练叫暂停)
            gameTimer.pause();
            shotClockTimer.pause();

            expect(gameTimer.status).toBe(TimerStatus.suspended);
            expect(shotClockTimer.status).toBe(TimerStatus.suspended);

            // 恢复比赛
            gameTimer.resume();
            shotClockTimer.resume();

            expect(gameTimer.status).toBe(TimerStatus.running);
            expect(shotClockTimer.status).toBe(TimerStatus.running);
        });
    });

    describe('高级集成场景', () => {
        it('应该支持链式启动计时器', async () => {
            const { timeCenter, createTimer } = createTimeManager({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });
            const timerA = createTimer();
            const timerB = createTimer();
            const onFinishedA = vi.fn(() => {
                timerB.start({
                    finishTime: () => timeCenter.currentTime.value + 2000,
                });
            });

            timerA.start({
                finishTime: () => timeCenter.currentTime.value + 1000,
                onFinished: onFinishedA,
            });

            expect(timerA.status).toBe(TimerStatus.running);
            expect(timerB.status).toBe(TimerStatus.idled);

            // 推进时间，完成 timerA
            await vi.advanceTimersByTimeAsync(1500);
            await nextTick();
            await vi.runOnlyPendingTimersAsync();
            await nextTick();

            expect(onFinishedA).toHaveBeenCalledTimes(1);
            expect(timerA.status).toBe(TimerStatus.stopped);
            expect(timerB.status).toBe(TimerStatus.running);
            expect(timerB.remainingTime.total).toBeCloseTo(1500, -2);

            // 推进时间，完成 timerB
            await vi.advanceTimersByTimeAsync(2000);
            await nextTick();
            await vi.runOnlyPendingTimersAsync();
            await nextTick();

            expect(timerB.status).toBe(TimerStatus.stopped);

            timeCenter.stopUpdateTime();
        });

        it('暂停的计时器应该在时间同步后正确恢复', async () => {
            let serverTime = 2000000;
            const syncTime = vi.fn().mockImplementation(() => {
                return Promise.resolve(serverTime);
            });

            const { timeCenter, createTimer } = createTimeManager({
                syncTime,
                syncTimeFrequency: 5000,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            const timer = createTimer();

            // 初始同步
            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            timer.start({
                finishTime: () => timeCenter.currentTime.value + 10000, // 10秒后结束
            });

            // 运行2秒
            await vi.advanceTimersByTimeAsync(2000);
            await nextTick();
            expect(timer.remainingTime.total).toBeCloseTo(8000, -2);

            // 暂停计时器
            timer.pause();

            // 模拟一次服务器时间同步，时间向前跳了30秒
            serverTime += 30000;
            await vi.advanceTimersByTimeAsync(5000); // 触发同步
            await nextTick();
            await vi.runOnlyPendingTimersAsync();
            await nextTick();

            // 恢复计时器
            timer.resume();

            // 验证：即使服务器时间跳跃，计时器的暂停时间依然被正确计算和补偿
            // 恢复后，剩余时间仍然是约 8 秒。这是核心的业务逻辑，finishTime 的具体值是实现细节。
            expect(timer.remainingTime.total).toBeCloseTo(8000, -2);

            timeCenter.stopUpdateTime();
        });
    });
});
