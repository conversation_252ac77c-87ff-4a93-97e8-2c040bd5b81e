import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { ref, nextTick } from 'vue';
import { createTimeManager } from '../create-time-manager';
import { useTimeCenter } from '../use-time-center';
import { TimerStatus } from '../types';

// 用于性能监控的工具函数
const measureTime = async (fn: () => void | Promise<void>): Promise<number> => {
    const start = performance.now();
    await fn();
    return performance.now() - start;
};

const raf = vi.fn((callback: FrameRequestCallback) => {
    const id = setTimeout(() => callback(performance.now()), 16);
    return id as unknown as number;
});

const caf = vi.fn((id: number) => {
    clearTimeout(id);
});

describe('性能和内存测试', () => {
    beforeEach(() => {
        vi.useFakeTimers();
        vi.clearAllMocks();
        raf.mockClear();
        caf.mockClear();

        global.performance = global.performance || {};
        global.performance.now = vi.fn(() => Date.now());
    });

    afterEach(() => {
        vi.useRealTimers();
        vi.restoreAllMocks();
    });

    describe('计时器创建性能测试', () => {
        it('应该能快速创建大量计时器', async () => {
            const { createTimer } = createTimeManager();
            const timerCount = 1000;

            const executionTime = await measureTime(() => {
                const timers = [];
                for (let i = 0; i < timerCount; i++) {
                    timers.push(createTimer());
                }
            });

            // 创建1000个计时器应该在100ms内完成
            expect(executionTime).toBeLessThan(100);
        });

        it('应该能快速启动大量计时器', async () => {
            const { createTimer } = createTimeManager();
            const timerCount = 500;
            const timers = Array.from({ length: timerCount }, () => createTimer());

            const executionTime = await measureTime(() => {
                timers.forEach((timer, index) => {
                    timer.start({
                        beginTime: () => Date.now() + index,
                        finishTime: () => Date.now() + 5000 + index,
                    });
                });
            });

            // 启动500个计时器应该在50ms内完成
            expect(executionTime).toBeLessThan(50);

            // 验证所有计时器都正确启动
            timers.forEach(timer => {
                expect(timer.status).toBe(TimerStatus.running);
            });
        });
    });

    describe('时间更新性能测试', () => {
        it('应该能高效处理高频时间更新', async () => {
            const mockCurrentTime = ref(1000000);
            const { createTimer } = createTimeManager();
            const timer = createTimer();

            timer.start({
                beginTime: () => mockCurrentTime.value,
            });

            const updateCount = 1000;
            const executionTime = await measureTime(async () => {
                for (let i = 0; i < updateCount; i++) {
                    mockCurrentTime.value += 10;
                    await nextTick();
                }
            });

            // 1000次更新应该在合理时间内完成
            expect(executionTime).toBeLessThan(500);
        });

        it('应该能高效处理多计时器同时更新', async () => {
            const mockCurrentTime = ref(1000000);
            const { createTimer } = createTimeManager();
            const timerCount = 100;
            const timers = Array.from({ length: timerCount }, () => createTimer());

            timers.forEach(timer => {
                timer.start({
                    beginTime: () => mockCurrentTime.value,
                    onTick: () => {}, // 添加回调以测试回调处理性能
                });
            });

            const executionTime = await measureTime(async () => {
                mockCurrentTime.value += 1000;
                await nextTick();
            });

            // 100个计时器的单次更新应该在20ms内完成
            expect(executionTime).toBeLessThan(20);
        });
    });

    describe('内存使用测试', () => {
        it('应该在计时器销毁后释放内存', () => {
            const { createTimer } = createTimeManager();
            const timerCount = 100;

            // 创建大量计时器
            const timers = Array.from({ length: timerCount }, () => createTimer());
            timers.forEach(timer => timer.start({}));

            // 销毁所有计时器
            timers.forEach(timer => timer.destroy());

            // 验证所有计时器都被销毁
            timers.forEach(timer => {
                expect(timer.status).toBe(TimerStatus.destroyed);
                expect(timer.onTick).toBeUndefined();
                expect(timer.onFinished).toBeUndefined();
            });
        });

        it('应该正确清理时间中心资源', () => {
            const raf = vi.fn((callback: FrameRequestCallback) => {
                const id = setTimeout(() => callback(performance.now()), 16);
                return id as unknown as number;
            });
            const caf = vi.fn((id: number) => {
                clearTimeout(id);
            });

            const timeCenter = useTimeCenter({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            expect(raf).toHaveBeenCalled();

            timeCenter.stopUpdateTime();
            expect(caf).toHaveBeenCalled();
        });

        it('应该防止内存泄漏在长时间运行场景', async () => {
            const { timeCenter, createTimer } = createTimeManager({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            // 模拟长时间运行的场景
            for (let cycle = 0; cycle < 10; cycle++) {
                const timers = Array.from({ length: 10 }, () => createTimer());
                const now = Date.now();

                timers.forEach(timer => {
                    timer.start({
                        beginTime: () => now,
                        finishTime: () => now + 1000,
                    });
                });

                // 推进时间使计时器结束
                await vi.advanceTimersByTimeAsync(1500);
                await nextTick();
                await vi.runOnlyPendingTimersAsync();
                await nextTick();

                // 验证计时器正确结束
                timers.forEach(timer => {
                    expect(timer.status).toBe(TimerStatus.stopped);
                });

                // 手动销毁以模拟清理
                timers.forEach(timer => timer.destroy());
            }

            timeCenter.stopUpdateTime();
            // 如果存在内存泄漏，这个测试会显示异常的内存增长
            expect(true).toBe(true); // 主要是执行上述逻辑
        });
    });

    describe('大规模并发测试', () => {
        it('应该能处理大量并发计时器', async () => {
            const { createTimer } = createTimeManager();
            const timerCount = 1000;
            const mockCurrentTime = ref(1000000);

            // 创建大量计时器
            const timers = Array.from({ length: timerCount }, () => createTimer());

            // 同时启动所有计时器
            const startTime = performance.now();
            timers.forEach((timer, index) => {
                timer.start({
                    beginTime: () => mockCurrentTime.value + index,
                    finishTime: () => mockCurrentTime.value + 5000 + index,
                    onTick: () => {
                        // 空回调，模拟真实使用场景
                    },
                });
            });
            const createEndTime = performance.now();

            // 验证创建性能
            expect(createEndTime - startTime).toBeLessThan(200);

            // 推进时间并测试更新性能
            const updateStartTime = performance.now();
            mockCurrentTime.value += 1000;
            await nextTick();
            const updateEndTime = performance.now();

            // 验证更新性能
            expect(updateEndTime - updateStartTime).toBeLessThan(100);

            // 验证所有计时器正常工作
            const runningCount = timers.filter(timer => timer.status === TimerStatus.running).length;
            expect(runningCount).toBe(timerCount);
        });

        it('应该能处理频繁的创建和销毁', async () => {
            const { createTimer } = createTimeManager();
            const cycles = 100;

            const executionTime = await measureTime(() => {
                for (let i = 0; i < cycles; i++) {
                    const timer = createTimer();
                    timer.start({});
                    timer.destroy();
                }
            });

            // 100次创建-销毁循环应该在合理时间内完成
            expect(executionTime).toBeLessThan(100);
        });
    });

    describe('服务器同步性能测试', () => {
        it('应该能高效处理服务器时间同步', async () => {
            const syncCallCount = ref(0);
            const mockSyncTime = vi.fn().mockImplementation(() => {
                syncCallCount.value++;
                return Promise.resolve(Date.now() + syncCallCount.value * 1000);
            });

            const raf = vi.fn((callback: FrameRequestCallback) => {
                const id = setTimeout(() => callback(performance.now()), 16);
                return id as unknown as number;
            });
            const caf = vi.fn((id: number) => {
                clearTimeout(id);
            });

            const { timeCenter, createTimer } = createTimeManager({
                syncTime: mockSyncTime,
                syncTimeFrequency: 100, // 快速同步以测试性能
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            const timer = createTimer();
            timer.start({});

            // 快速推进时间以触发多次同步
            for (let i = 0; i < 5; i++) {
                await vi.advanceTimersToNextTimerAsync();
                await nextTick();
            }

            expect(mockSyncTime).toHaveBeenCalled();
            expect(timer.status).toBe(TimerStatus.running);

            timeCenter.stopUpdateTime();
        });

        it('应该能优雅处理同步失败而不影响性能', async () => {
            let failCount = 0;
            const mockSyncTime = vi.fn().mockImplementation(() => {
                failCount++;
                if (failCount % 2 === 0) {
                    return Promise.reject(new Error('Sync failed'));
                }
                return Promise.resolve(Date.now());
            });

            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

            const raf = vi.fn((callback: FrameRequestCallback) => {
                const id = setTimeout(() => callback(performance.now()), 16);
                return id as unknown as number;
            });
            const caf = vi.fn((id: number) => {
                clearTimeout(id);
            });

            const { timeCenter, createTimer } = createTimeManager({
                syncTime: mockSyncTime,
                syncTimeFrequency: 100,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            const timer = createTimer();
            timer.start({});

            const executionTime = await measureTime(async () => {
                for (let i = 0; i < 10; i++) {
                    await vi.advanceTimersToNextTimerAsync();
                    await nextTick();
                }
            });

            // 即使有同步失败，执行时间应该仍然合理
            expect(executionTime).toBeLessThan(200);
            expect(timer.status).toBe(TimerStatus.running);

            timeCenter.stopUpdateTime();
            consoleSpy.mockRestore();
        });
    });
});
