import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { ref, nextTick } from 'vue';
import { useTimer } from '../use-timer';
import type { FormatterTime } from '../types';

/**
 * 由于 formatTime 是 useTimer 内部的私有函数，
 * 我们通过创建计时器实例并直接设置时间来测试格式化逻辑
 */
describe('时间格式化功能', () => {
    let mockCurrentTime: any;

    beforeEach(() => {
        vi.useFakeTimers();
        mockCurrentTime = ref(1000000);
    });

    afterEach(() => {
        vi.useRealTimers();
        vi.restoreAllMocks();
    });

    /**
     * 通过设置特定的时间差来测试格式化功能
     */
    const testFormatTime = async (timeDelta: number): Promise<FormatterTime> => {
        const timer = useTimer({ currentTime: mockCurrentTime });
        const startTime = mockCurrentTime.value;

        timer.start({
            beginTime: () => startTime,
        });

        // 直接更新时间并等待响应式更新
        mockCurrentTime.value = startTime + timeDelta;
        await nextTick();

        return timer.elapsedTime;
    };

    // const testRemainingTime = async (totalTime: number, elapsedTime: number): Promise<FormatterTime> => {
    //     const timer = useTimer({ currentTime: mockCurrentTime });
    //     const startTime = mockCurrentTime.value;

    //     timer.start({
    //         beginTime: () => startTime,
    //         finishTime: () => startTime + totalTime,
    //     });

    //     // 推进时间
    //     mockCurrentTime.value = startTime + elapsedTime;
    //     await nextTick();

    //     return timer.remainingTime;
    // };

    describe('正常时间格式化', () => {
        it('应该正确格式化零时间', async () => {
            const result = await testFormatTime(0);

            expect(result.total).toBe(0);
            expect(result.hours).toBe('00');
            expect(result.minutes).toBe('00');
            expect(result.seconds).toBe('00');
            expect(result.milliseconds).toBe('000');
        });

        it('应该正确格式化毫秒', async () => {
            const result = await testFormatTime(123);

            expect(result.total).toBe(123);
            expect(result.hours).toBe('00');
            expect(result.minutes).toBe('00');
            expect(result.seconds).toBe('00');
            expect(result.milliseconds).toBe('123');
        });

        it('应该正确格式化秒数', async () => {
            const result = await testFormatTime(5678);

            expect(result.total).toBe(5678);
            expect(result.hours).toBe('00');
            expect(result.minutes).toBe('00');
            expect(result.seconds).toBe('05');
            expect(result.milliseconds).toBe('678');
        });

        it('应该正确格式化分钟', async () => {
            const timeDelta = 3 * 60 * 1000 + 45 * 1000 + 234;
            const result = await testFormatTime(timeDelta);

            expect(result.total).toBe(225234);
            expect(result.hours).toBe('00');
            expect(result.minutes).toBe('03');
            expect(result.seconds).toBe('45');
            expect(result.milliseconds).toBe('234');
        });

        it('应该正确格式化小时', async () => {
            const time = 2 * 60 * 60 * 1000 + 30 * 60 * 1000 + 15 * 1000 + 567;
            const result = await testFormatTime(time);

            expect(result.total).toBe(time);
            expect(result.hours).toBe('02');
            expect(result.minutes).toBe('30');
            expect(result.seconds).toBe('15');
            expect(result.milliseconds).toBe('567');
        });

        it('应该正确格式化大于 24 小时的时间', async () => {
            const time = 25 * 60 * 60 * 1000 + 10 * 60 * 1000 + 5 * 1000 + 100;
            const result = await testFormatTime(time);

            expect(result.total).toBe(time);
            expect(result.hours).toBe('25');
            expect(result.minutes).toBe('10');
            expect(result.seconds).toBe('05');
            expect(result.milliseconds).toBe('100');
        });
    });

    describe('边界值处理', () => {
        it('应该正确处理 59 秒 999 毫秒', async () => {
            const result = await testFormatTime(59 * 1000 + 999);

            expect(result.hours).toBe('00');
            expect(result.minutes).toBe('00');
            expect(result.seconds).toBe('59');
            expect(result.milliseconds).toBe('999');
        });

        it('应该正确处理 59 分 59 秒', async () => {
            const result = await testFormatTime(59 * 60 * 1000 + 59 * 1000);

            expect(result.hours).toBe('00');
            expect(result.minutes).toBe('59');
            expect(result.seconds).toBe('59');
        });

        it('应该正确处理刚好 1 小时', async () => {
            const result = await testFormatTime(60 * 60 * 1000);

            expect(result.hours).toBe('01');
            expect(result.minutes).toBe('00');
            expect(result.seconds).toBe('00');
            expect(result.milliseconds).toBe('000');
        });
    });

    describe('负数时间处理', () => {
        it('应该正确处理负数时间', async () => {
            const timer = useTimer({ currentTime: mockCurrentTime });
            const currentTime = mockCurrentTime.value;

            // 设置开始时间为未来时间
            timer.start({
                beginTime: () => currentTime + 5000,
            });

            await nextTick();
            const result = timer.elapsedTime;

            expect(result.total).toBeLessThan(0);
            expect(result.hours).toMatch(/^-/);
            expect(result.minutes).toMatch(/^-/);
            expect(result.seconds).toMatch(/^-/);
            expect(result.milliseconds).toMatch(/^-/);
        });

        it('应该正确格式化负数时间的绝对值', async () => {
            const timer = useTimer({ currentTime: mockCurrentTime });
            const currentTime = mockCurrentTime.value;

            // 设置开始时间为未来 1 小时 2 分 3 秒
            const futureDelta = 1 * 60 * 60 * 1000 + 2 * 60 * 1000 + 3 * 1000;
            timer.start({
                beginTime: () => currentTime + futureDelta,
            });

            await nextTick();
            const result = timer.elapsedTime;

            expect(result.total).toBe(-futureDelta);
            expect(result.hours).toBe('-01');
            expect(result.minutes).toBe('-02');
            expect(result.seconds).toBe('-03');
            expect(result.milliseconds).toBe('-000');
        });
    });

    describe('特殊值处理', () => {
        it('应该正确处理无限时间', async () => {
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => mockCurrentTime.value,
                finishTime: () => Infinity,
            });

            await nextTick();
            const result = timer.remainingTime;

            expect(result.total).toBe(Infinity);
            expect(result.hours).toBe('Infinity');
            expect(result.minutes).toBe('Infinity');
            expect(result.seconds).toBe('Infinity');
            expect(result.milliseconds).toBe('Infinity');
        });

        it('应该正确处理 NaN 时间', async () => {
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({});
            timer.destroy(); // 销毁后时间值变为 NaN

            await nextTick();
            const result = timer.elapsedTime;

            expect(result.total).toBeNaN();
            expect(result.hours).toBe('--');
            expect(result.minutes).toBe('--');
            expect(result.seconds).toBe('--');
            expect(result.milliseconds).toBe('---');
        });
    });

    describe('数字补零功能', () => {
        it('应该为单位数的小时补零', async () => {
            const result = await testFormatTime(5 * 60 * 60 * 1000);
            expect(result.hours).toBe('05');
        });

        it('应该为单位数的分钟补零', async () => {
            const result = await testFormatTime(7 * 60 * 1000);
            expect(result.minutes).toBe('07');
        });

        it('应该为单位数的秒数补零', async () => {
            const result = await testFormatTime(9 * 1000);
            expect(result.seconds).toBe('09');
        });

        it('应该为单位数和双位数的毫秒补零', async () => {
            expect((await testFormatTime(5)).milliseconds).toBe('005');
            expect((await testFormatTime(50)).milliseconds).toBe('050');
            expect((await testFormatTime(500)).milliseconds).toBe('500');
        });

        it('应该正确处理两位数', async () => {
            const result = await testFormatTime(12 * 60 * 60 * 1000 + 34 * 60 * 1000 + 56 * 1000);

            expect(result.hours).toBe('12');
            expect(result.minutes).toBe('34');
            expect(result.seconds).toBe('56');
        });
    });

    describe('精度和舍入', () => {
        it('应该正确处理毫秒的向下舍入', async () => {
            // 测试 Math.floor 的行为
            const result = await testFormatTime(1500.7); // 1.5007 秒

            expect(result.seconds).toBe('01');
            expect(result.milliseconds).toBe('500'); // 应该是 500，不是 501
        });

        it('应该正确处理分钟边界', async () => {
            const result = await testFormatTime(59999); // 59.999 秒

            expect(result.minutes).toBe('00');
            expect(result.seconds).toBe('59');
            expect(result.milliseconds).toBe('999');
        });

        it('应该正确处理小时边界', async () => {
            const result = await testFormatTime(3599999); // 59 分 59.999 秒

            expect(result.hours).toBe('00');
            expect(result.minutes).toBe('59');
            expect(result.seconds).toBe('59');
            expect(result.milliseconds).toBe('999');
        });
    });

    describe('大数值处理', () => {
        it('应该正确处理非常大的时间值', async () => {
            const largeTime = 999 * 60 * 60 * 1000; // 999 小时
            const result = await testFormatTime(largeTime);

            expect(result.hours).toBe('999');
            expect(result.minutes).toBe('00');
            expect(result.seconds).toBe('00');
            expect(result.milliseconds).toBe('000');
        });

        it('应该正确处理超过三位数的小时', async () => {
            const largeTime = 1234 * 60 * 60 * 1000 + 56 * 60 * 1000 + 18 * 1000 + 90;
            const result = await testFormatTime(largeTime);

            expect(result.hours).toBe('1234');
            expect(result.minutes).toBe('56');
            expect(result.seconds).toBe('18');
            expect(result.milliseconds).toBe('090');
        });
    });

    describe('剩余时间格式化', () => {
        it('应该正确计算和格式化剩余时间', async () => {
            const mockCurrentTime = ref(1000000);
            const timer = useTimer({ currentTime: mockCurrentTime });

            // 设置 5 秒的倒计时
            timer.start({
                beginTime: () => mockCurrentTime.value,
                finishTime: () => mockCurrentTime.value + 5000,
            });

            // 经过 2 秒
            mockCurrentTime.value += 2000;
            await nextTick();

            const remaining = timer.remainingTime;

            expect(remaining.total).toBe(3000);
            expect(remaining.seconds).toBe('03');
        });

        it('剩余时间为零时应该正确显示', async () => {
            const mockCurrentTime = ref(1000000);
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => mockCurrentTime.value,
                finishTime: () => mockCurrentTime.value + 1000,
            });

            // 超过结束时间
            mockCurrentTime.value += 2000;
            await nextTick();

            const remaining = timer.remainingTime;

            expect(remaining.total).toBe(0);
            expect(remaining.hours).toBe('00');
            expect(remaining.minutes).toBe('00');
            expect(remaining.seconds).toBe('00');
            expect(remaining.milliseconds).toBe('000');
        });
    });
});
