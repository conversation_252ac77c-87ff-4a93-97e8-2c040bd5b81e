import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { useTimeCenter } from '../use-time-center';
import { nextTick, defineComponent } from 'vue';
import { mount } from '@vue/test-utils';

const raf = vi.fn((callback: FrameRequestCallback) => {
    const id = setTimeout(() => callback(performance.now()), 16); // Use a realistic frame interval
    return id as unknown as number;
});
const caf = vi.fn((id: number) => {
    clearTimeout(id);
});

describe('useTimeCenter', () => {
    beforeEach(() => {
        vi.useFakeTimers();
        vi.clearAllMocks();
        raf.mockClear();
        caf.mockClear();

        // Mock performance.now
        global.performance = global.performance || {};
        global.performance.now = vi.fn(() => Date.now());
    });

    afterEach(() => {
        vi.useRealTimers();
        vi.restoreAllMocks();
    });

    describe('基本功能', () => {
        it('应该创建时间中心实例', () => {
            const timeCenter = useTimeCenter();

            expect(timeCenter).toBeDefined();
            expect(timeCenter.currentTime).toBeDefined();
            expect(timeCenter.accumulatedTime).toBeDefined();
            expect(timeCenter.stopUpdateTime).toBeTypeOf('function');
        });

        it('应该初始化时间值', async () => {
            const timeCenter = useTimeCenter();

            expect(timeCenter.currentTime.value).toBeTypeOf('number');
            expect(timeCenter.accumulatedTime.value).toBe(0);
        });

        it('应该能手动停止时间更新', () => {
            const timeCenter = useTimeCenter({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            timeCenter.stopUpdateTime();

            expect(caf).toHaveBeenCalled();
        });
    });

    describe('服务器时间同步', () => {
        it('应该在初始化时执行同步', async () => {
            const mockSyncTime = vi.fn().mockResolvedValue(2000000);

            const timeCenter = useTimeCenter({
                syncTime: mockSyncTime,
                syncTimeFrequency: 5000,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            expect(mockSyncTime).toHaveBeenCalled();
            expect(timeCenter.currentTime.value).toBeGreaterThanOrEqual(2000000);

            // 清理
            timeCenter.stopUpdateTime();
        });

        it('应该处理同步错误', async () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
            const mockSyncTime = vi.fn().mockRejectedValue(new Error('Network error'));

            const timeCenter = useTimeCenter({
                syncTime: mockSyncTime,
                syncTimeFrequency: 5000,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            expect(consoleSpy).toHaveBeenCalledWith('服务器时间同步失败:', expect.any(Error));

            timeCenter.stopUpdateTime();
            consoleSpy.mockRestore();
        });

        it('应该处理无效的服务器时间戳', async () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
            const mockSyncTime = vi.fn().mockResolvedValue(NaN);

            const timeCenter = useTimeCenter({
                syncTime: mockSyncTime,
                syncTimeFrequency: 5000,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            expect(consoleSpy).toHaveBeenCalledWith('服务器返回无效时间戳:', NaN);

            timeCenter.stopUpdateTime();
            consoleSpy.mockRestore();
        });

        it('应该处理各种无效时间戳类型', async () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
            const invalidValues = [undefined, null, 'invalid', Infinity];

            for (const invalidValue of invalidValues) {
                caf.mockClear();
                const mockSyncTime = vi.fn().mockResolvedValue(invalidValue);

                const timeCenter = useTimeCenter({
                    syncTime: mockSyncTime,
                    syncTimeFrequency: 5000,
                    requestAnimationFrame: raf,
                    cancelAnimationFrame: caf,
                });

                await vi.advanceTimersToNextTimerAsync();
                await nextTick();

                expect(consoleSpy).toHaveBeenCalledWith('服务器返回无效时间戳:', invalidValue);

                timeCenter.stopUpdateTime();
            }

            consoleSpy.mockRestore();
        });

        it('应该防止 syncTime 被多次并发执行', async () => {
            let syncInProgress = false;
            let syncCount = 0;

            const mockSyncTime = vi.fn().mockImplementation(async () => {
                // 如果已经有同步在进行，说明并发控制失效
                if (syncInProgress) {
                    throw new Error('Concurrent sync detected!');
                }

                syncInProgress = true;
                syncCount++;

                // 简单返回，不使用 setTimeout
                const result = Date.now() + 1000;

                syncInProgress = false;
                return result;
            });

            let updateCount = 0; // 使用普通变量代替ref
            const raf = vi.fn((callback: FrameRequestCallback) => {
                updateCount++;
                // 限制更新次数，防止无限循环
                if (updateCount < 10) {
                    const id = setTimeout(() => callback(performance.now()), 16);
                    return id as unknown as number;
                }
                return 0;
            });
            const caf = vi.fn((id: number) => {
                clearTimeout(id);
            });

            const timeCenter = useTimeCenter({
                syncTime: mockSyncTime,
                syncTimeFrequency: 50, // 短间隔
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            // 等待初始同步完成
            await nextTick();
            await nextTick();

            // 让几次更新发生
            await vi.advanceTimersToNextTimerAsync();
            await nextTick();
            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            // 手动停止以避免继续循环
            timeCenter.stopUpdateTime();

            // 验证 syncTime 被调用了，但没有发生并发错误
            expect(syncCount).toBeGreaterThan(0);
            // 如果有并发执行，mockSyncTime 会抛出错误，这里验证没有抛出错误
            expect(mockSyncTime).toHaveBeenCalled();
        });

        it('应该在初始化时立即执行同步而不依赖 requestAnimationFrame', async () => {
            let syncCalled = false;
            const mockSyncTime = vi.fn().mockImplementation(async () => {
                syncCalled = true;
                return Date.now() + 1000;
            });

            // 模拟 requestAnimationFrame 永远不被调用的情况
            const neverCallRaf = vi.fn().mockImplementation(() => 0);

            const timeCenter = useTimeCenter({
                syncTime: mockSyncTime,
                requestAnimationFrame: neverCallRaf,
            });

            // 等待初始同步 Promise 完成
            await nextTick();

            expect(syncCalled).toBe(true);
            expect(mockSyncTime).toHaveBeenCalledTimes(1);

            timeCenter.stopUpdateTime();
        });
    });

    describe('配置选项', () => {
        it('应该使用默认配置', () => {
            const timeCenter = useTimeCenter();

            expect(timeCenter.currentTime.value).toBeTypeOf('number');
            expect(timeCenter.accumulatedTime.value).toBe(0);
        });

        it('应该应用自定义配置', () => {
            const customOptions = {
                syncTimeFrequency: 10000,
            };

            const timeCenter = useTimeCenter(customOptions);

            expect(timeCenter).toBeDefined();
        });
    });

    describe('内存管理', () => {
        it('应该在组件卸载时自动清理', () => {
            const TestComponent = defineComponent({
                setup() {
                    useTimeCenter({
                        requestAnimationFrame: raf,
                        cancelAnimationFrame: caf,
                    });
                    return () => null; // Renderless component
                },
            });

            const wrapper = mount(TestComponent);
            expect(raf).toHaveBeenCalled();

            wrapper.unmount();
            expect(caf).toHaveBeenCalled();
        });

        it('当在组件外部使用时，不应该自动清理', () => {
            const timeCenter = useTimeCenter();
            // 在这种情况下，我们期望开发者手动管理清理
            expect(caf).not.toHaveBeenCalled();
            timeCenter.stopUpdateTime();
        });
    });
});
