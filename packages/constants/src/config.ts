// 广播 name
export const STUDENT_BROADCAST_CHANNEL = 'STUDENT_BROADCAST_CHANNEL';

// chrome下载地址
export const CHROME_DOWNLOAD_URL = 'https://www.google.cn/chrome/index.html';

// 隐私政策地址
export const PRIVACY_POLICY_URL = 'https://about.zhipin.com/agreement?id=9513747f3ec8432db9cc2905796c0b35';
// 用户协议
export const USER_AGREEMENT_URL = 'https://about.zhipin.com/agreement?id=5929a585718e4b1b8250390cfc2a8870';
// 人脸验证服务协议
export const FACE_VERIFY_SERVICE_AGREEMENT_URL = 'https://m.zhipin.com/mpa/html/mix/agreement-detail?agreementId=48c037a757d546cfa07b5aa8020bdad8';

// 帮助电话
export const HELP_PHONE = '010-59045796';

// 虚拟设备列表
export const VIRTUAL_DEVICE_LABLE_LIST = [
    'OBS Virtual Camera (m-de:vice)',
    'OBS Virtual Camera', // obs
    'JZ Virtual Camera', // 金舟
    'e2eSoft VCam', // vcam
    'ManyCam Virtual Webcam', // manycam 有虚拟麦克风
    'CamIn Virtual Camera', // CamIn
    'CyberLink YouCam 10', // YouCam
    'SplitCam Video Driver', // SplitCam 有虚拟麦克风
    'AlterCam Virtual Camera', // AlterCam
    'Camera (NVIDIA Broadcast)',
    'vMix Video',
    'vMix Video External 2',
    'DroidCam Source 2',
    'DroidCam Source 3',
];

// 测评登录local storage里存的登录token的key
export const EVALUATION_TOKEN = 'EXAM_TOKEN';

// 构建参数变量名
export const BUILD_ARG_VERSION = 'deployversion'; // npm run build --deployversion=1
export const BUILD_ARG_ENV = 'deployenv'; // npm run build --deployenv=prod

// 啄木鸟监控相关
export const MAGPIE_APP_KEY = '6c1dffc94fa7b472fbe21'; // 啄木鸟key
export const MAGPIE_APP_KEY_QA = 'ad61ed944e0bbe865fb11'; // 啄木鸟key(测试环境)

// 埋点新干线相关
export const DATASTAR_SERVER_URL_DEV = 'https://logapi-dev.weizhipin.com/dap/api/json';
export const DATASTAR_SERVER_URL_PROD = 'https://logapi.zhipin.com/dap/api/json';
export const DATASTAR_SERVER_URL_TOKEN = 'kaoping3CE17456A9D81D9CBC20A2436';
