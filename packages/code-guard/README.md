# Code-Guard

> fe-kaoshi-web 项目的本地代码门禁工具

## 简介

Code-Guard 是为 fe-kaoshi-web 项目设计的本地代码门禁工具，它帮助团队实现分层防御的代码质量保障系统。该工具能有效地检测代码问题，并在提交和推送前阻止不合规范的代码进入代码库。

## 功能

- 检查暂存区文件 (用于 pre-commit)
- 检查当前分支变更 (用于 pre-push)
- 全面代码检查 (用于发布前)
- 智能识别受影响的包，仅对变更的包执行检查
- 与 Turborepo 深度集成，利用缓存和增量计算提升性能
- 敏感信息检测，防止密钥泄露

## 安装

项目中已经安装本工具，位于 `packages/code-guard` 目录下。

如果要在新项目中使用，可以执行：

```bash
pnpm add -D @crm/exam-code-guard
```

## 使用方法

### 命令行使用

```bash
# 检查暂存区文件 (用于 pre-commit)
code-guard staged

# 检查当前分支变更 (用于 pre-push)
code-guard branch

# 全面检查 (用于发布前)
code-guard full

# 仅运行 lint 检查
code-guard lint

# 仅运行类型检查
code-guard typecheck

# 仅运行测试
code-guard test
```

### 与 Git Hooks 结合

在 `.husky/pre-commit` 中添加：

```bash
#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

# 运行暂存区检查
pnpm code-guard staged
```

在 `.husky/pre-push` 中添加：

```bash
#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

# 获取最新的远程分支信息
pnpm run prepush

# 运行分支检查
pnpm code-guard branch
```

## 配置

可通过命令行参数配置各项检查行为，例如：

```bash
# 自动修复问题
code-guard staged --fix

# 指定基础比较分支
code-guard branch --base=origin/main

# 仅检查受影响的包
code-guard typecheck --affected
```

## API 参考

如果需要在其他工具中集成 code-guard，可以使用其 API：

```js
const { stagedCommand, branchCommand } = require('@crm/exam-code-guard');

// 检查暂存区文件
await stagedCommand({ fix: true });

// 检查分支变更
await branchCommand({ base: 'HEAD^1' });
```

## 贡献指南

如果你发现了 bug 或有新功能建议，请在项目中提交 Issue 或 PR。

## 许可证

MIT
