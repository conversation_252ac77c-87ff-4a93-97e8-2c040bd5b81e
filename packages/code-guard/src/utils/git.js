const simpleGit = require('simple-git');
const path = require('path');
const { execSync } = require('child_process');

// 初始化 git 客户端
const git = simpleGit();

/**
 * 获取项目根目录
 * @returns {string} 项目根目录的绝对路径
 */
function getProjectRoot() {
    try {
        const rootPath = execSync('git rev-parse --show-toplevel', { encoding: 'utf-8' }).trim();
        return rootPath;
    } catch (error) {
        console.error('错误: 无法获取项目根目录。请确保在 Git 仓库中执行此命令。');
        process.exit(1);
    }
}

/**
 * 获取暂存区的文件列表
 * @returns {Promise<string[]>} 暂存区文件的相对路径数组
 */
async function getStagedFiles() {
    try {
        const status = await git.status();
        return [...status.staged, ...status.created, ...status.modified.filter((file) => status.staged.includes(file))];
    } catch (error) {
        console.error('获取暂存区文件失败:', error.message);
        return [];
    }
}

/**
 * 获取从指定 commit 到当前 HEAD 的变更文件
 * @param {string} base 基础 commit 或分支
 * @returns {Promise<string[]>} 变更文件的相对路径数组
 */
async function getChangedFiles(base = 'HEAD^1') {
    try {
        const diff = await git.diff([`${base}...HEAD`, '--name-only']);
        return diff
            .split('\n')
            .filter((file) => file.trim() !== '')
            .map((file) => file.trim());
    } catch (error) {
        console.error('获取变更文件失败:', error.message);
        return [];
    }
}

/**
 * 获取变更文件所属的包
 * @param {string[]} files 文件路径数组
 * @returns {string[]} 受影响的包名数组
 */
function getAffectedPackages(files) {
    // 用于存储受影响的包
    const affectedPackages = new Set();

    files.forEach((file) => {
        // apps 下的应用
        if (file.startsWith('apps/')) {
            const appMatch = file.match(/^apps\/([^/]+)/);
            if (appMatch && appMatch[1]) {
                affectedPackages.add(`@apps/${appMatch[1]}`);
            }
        }
        // packages 下的包
        else if (file.startsWith('packages/')) {
            const packageMatch = file.match(/^packages\/([^/]+)/);
            if (packageMatch && packageMatch[1]) {
                affectedPackages.add(`@crm/exam-${packageMatch[1]}`);
            }
        }
        // 处理其他目录下的文件
        else {
            // 将其他文件归类为根目录项目
            affectedPackages.add('@crm/root');
        }
    });

    return Array.from(affectedPackages);
}

/**
 * 根据包名生成 Turborepo 过滤器
 * @param {string[]} packages 包名数组
 * @returns {string} Turborepo 过滤器字符串
 */
function buildTurboFilter(packages) {
    if (packages.length === 0) {
        return '';
    }

    return `--filter={${packages.join(',')}}`;
}

module.exports = {
    getProjectRoot,
    getStagedFiles,
    getChangedFiles,
    getAffectedPackages,
    buildTurboFilter,
};
