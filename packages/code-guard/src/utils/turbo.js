const { execSync } = require('child_process');
const chalk = require('chalk');
const ora = require('ora');

/**
 * 运行 Turborepo 命令
 * @param {string} task 任务名称（如 'build', 'test'）
 * @param {string} filter 过滤器（如 '--filter=[HEAD^1]'）
 * @returns {Promise<boolean>} 是否成功
 */
async function runTurboTask(task, filter = '') {
    const spinner = ora(`执行 turbo run ${task} ${filter}`).start();

    try {
        const cmd = `pnpm turbo run ${task} ${filter}`;
        execSync(cmd, { stdio: 'inherit' });
        spinner.succeed(chalk.green(`✓ ${task} 成功完成`));
        return true;
    } catch (error) {
        spinner.fail(chalk.red(`✗ ${task} 失败: ${error.message}`));
        return false;
    }
}

/**
 * 基于变更的文件运行任务，仅针对受影响的包
 * @param {string} task 任务名称
 * @param {string[]} packages 包名数组
 * @returns {Promise<boolean>} 是否成功
 */
async function runForAffectedPackages(task, packages) {
    if (packages.length === 0) {
        console.log(chalk.yellow('⚠️ 没有检测到受影响的包'));
        return true;
    }

    const filter = `--filter={${packages.join(',')}}`;
    return runTurboTask(task, filter);
}

/**
 * 获取可用的 turbo 任务
 * @returns {string[]} 可用的任务名称数组
 */
function getAvailableTasks() {
    try {
        const output = execSync('pnpm turbo run --dry-run', { encoding: 'utf-8' });

        // 从输出中提取任务名称
        const tasksMatch = output.match(/Tasks:[\s\n]+([\w\s\n,:]+)/);
        if (tasksMatch && tasksMatch[1]) {
            return tasksMatch[1]
                .split(',')
                .map((task) => task.trim())
                .filter(Boolean);
        }

        return [];
    } catch (error) {
        console.error('获取 turbo 任务失败:', error.message);
        return [];
    }
}

module.exports = {
    runTurboTask,
    runForAffectedPackages,
    getAvailableTasks,
};
