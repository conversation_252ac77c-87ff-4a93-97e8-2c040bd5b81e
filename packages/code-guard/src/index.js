const { stagedCommand } = require('./commands/staged');
const { branchCommand } = require('./commands/branch');
const { fullCommand } = require('./commands/full');
const { lintCommand } = require('./commands/lint');
const { typeCheckCommand } = require('./commands/typecheck');
const { testCommand } = require('./commands/test');

module.exports = {
    // 命令
    stagedCommand,
    branchCommand,
    fullCommand,
    lintCommand,
    typeCheckCommand,
    testCommand,
};
