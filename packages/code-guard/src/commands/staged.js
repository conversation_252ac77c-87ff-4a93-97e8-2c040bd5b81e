const { execSync } = require('child_process');
const chalk = require('chalk');
const { getStagedFiles } = require('../utils/git');

/**
 * 使用gitleaks检查敏感信息
 * @returns {boolean} 是否检查成功
 */
function checkWithGitleaks() {
    try {
        execSync('npx gitleaks protect --staged --verbose', { stdio: 'inherit' });
        console.log(chalk.green('✓ 敏感信息检查成功完成'));
        return true;
    } catch (error) {
        return false;
    }
}

/**
 * 使用正则表达式检查敏感信息
 */
function checkWithRegex() {
    const sensitivePatterns = ['api[_]?key', 'auth[_]?token', 'password', 'secret', 'credential'];
    const grepCommand = `git diff --cached -G"(${sensitivePatterns.join('|')})" --name-only`;
    const potentiallyExposedFiles = execSync(grepCommand, { encoding: 'utf-8' }).trim();

    if (potentiallyExposedFiles) {
        console.log(chalk.yellow('⚠️ 可能包含敏感信息的文件:'));
        console.log(potentiallyExposedFiles);
        console.log(chalk.yellow('请检查这些文件是否包含敏感信息'));
    } else {
        console.log(chalk.green('✓ 未发现明显的敏感信息'));
    }
}

/**
 * 检查暂存区文件（用于 pre-commit）
 * @param {Object} options 命令选项
 * @param {boolean} options.fix 是否自动修复问题
 */
async function stagedCommand(options) {
    try {
        console.log(chalk.blue('🔍 检查暂存区文件...'));

        // 获取暂存区文件
        const stagedFiles = await getStagedFiles();

        if (stagedFiles.length === 0) {
            console.log(chalk.yellow('⚠️ 没有暂存的文件'));
            return;
        }

        console.log(`发现 ${stagedFiles.length} 个暂存文件`);

        // 运行 lint-staged
        try {
            console.log(chalk.blue('🧹 运行 lint-staged...'));
            execSync('pnpm lint-staged', { stdio: 'inherit' });
            console.log(chalk.green('✓ lint-staged 成功完成'));
        } catch (error) {
            console.error(chalk.red(`✗ lint-staged 失败: ${error.message}`));
            process.exit(1);
        }

        // 运行敏感信息检查
        console.log(chalk.blue('🔒 检查敏感信息...'));
        try {
            // 先尝试使用gitleaks，如果失败则使用正则表达式检查
            const gitleaksSuccess = checkWithGitleaks();
            if (!gitleaksSuccess) {
                checkWithRegex();
            }
        } catch (error) {
            console.error(chalk.red(`✗ 敏感信息检查失败: ${error.message}`));
            // 不终止进程，仅警告
        }

        console.log(chalk.green('✅ 暂存区检查完成'));
    } catch (error) {
        console.error(chalk.red(`✗ 暂存区检查失败: ${error.message}`));
        process.exit(1);
    }
}

module.exports = {
    stagedCommand,
};
