#!/usr/bin/env node

const { program } = require('commander');
const { version } = require('../package.json');
const { stagedCommand } = require('../src/commands/staged');
const { branchCommand } = require('../src/commands/branch');
const { fullCommand } = require('../src/commands/full');
const { lintCommand } = require('../src/commands/lint');
const { typeCheckCommand } = require('../src/commands/typecheck');
const { testCommand } = require('../src/commands/test');

// 设置版本号
program.version(version, '-v, --version');

// 注册命令
program.command('staged').description('检查暂存区文件 (用于 pre-commit)').option('-f, --fix', '自动修复问题', false).action(stagedCommand);

program.command('branch').description('检查当前分支变更 (用于 pre-push)').option('-b, --base <commit>', '对比的基础分支或提交', 'HEAD^1').action(branchCommand);

program.command('full').description('全面检查当前代码库 (用于发布前)').action(fullCommand);

program.command('lint').description('仅运行 lint 检查').option('-s, --staged', '仅检查暂存区', false).option('-f, --fix', '自动修复问题', false).action(lintCommand);

program.command('typecheck').description('仅运行类型检查').option('-a, --affected', '仅检查受影响的包', false).action(typeCheckCommand);

program.command('test').description('仅运行测试').option('-a, --affected', '仅测试受影响的包', false).option('-w, --watch', '监视模式', false).action(testCommand);

// 解析命令行参数
program.parse(process.argv);

// 如果没有提供命令，显示帮助信息
if (!process.argv.slice(2).length) {
    program.outputHelp();
}
