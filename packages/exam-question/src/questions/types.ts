// 选项类型定义
export interface Option {
    encryptId: string; // 加密后的选项ID
    content: string; // 选项内容
    rank: number; // 选项的排序
    correct: number; // 选项是否正确，0表示错误，1表示正确
    files: any | null; // 选项关联的文件，可能为空
    id: number; // 选项ID
}

// 附件类型定义
export interface Attachment {
    encryptId: string; // 加密后的附件ID
    name: string; // 附件名称
    type: string; // 附件类型，例如"png"或"jpg"
    size: number; // 附件大小，以字节为单位
    uri: string; // 附件的URI路径
    url: string; // 附件的完整下载URL
}

export interface QuestionAnswerDetail {
    // 标签加密 ID 列表
    encryptTagIds?: string[];
    // 题目的难度等级
    hardLevel: number;
    //
    tagNames: string[];
    // 主题目的答案
    answer: string;
    // 题目的分析内容
    analysis: string;
    tagOptions?: { encryptId: string; tagName: string }[];
}

// 问题类型定义
export interface Question {
    encryptId: string | null; // 加密后的问题ID，可能为空
    encryptQuestionId?: string; // 加密后的问题ID
    title: string; // 问题标题
    type: number; // 问题类型，1表示单选，2表示多选等
    score: number; // 该问题的分数
    scoreList: number[] | null; // 每个选项的得分列表，可能为空
    rank: number; // 问题的排序
    // answer: string | string[] // 问题的答案
    answer?: any; // 问题的答案
    answerStatus: number; // 回答状态，0表示未回答
    answerScore: number | null; // 用户的得分，可能为空
    options: Option[]; // 选项列表
    recoveryOptions: Option[] | null; // 备选选项列表，可能为空
    attachments: Attachment[]; // 问题的附件列表
    answerContentList: string[]; // 用户回答的内容列表
    answerAttachments: Attachment[] | null; // 用户上传的附件，可能为空
    subQuestionList: Question[] | null; // 子问题列表
    answerContentInOption: boolean; // 答案是否在选项中
    adjustAnswerContentList: string[] | null; // 调整后的答案内容列表，可能为空
    questionId: number; // 问题ID
    answerContentIdList: number[]; // 用户选择的选项ID列表
    adjustAnswerContentIdList: number[]; // 调整后的选项ID列表
    scoreExamEnable: number; // 是否展示分值
    codeQuestionInfo?: {
        result: number;
    };
}

// 维度数据类型定义
export interface DimensionData {
    encryptDimensionId?: string; // 加密后的维度ID
    dimensionName?: string; // 维度名称
    totalQuestionNum?: number; // 问题总数
    totalScore?: number; // 总分数
    totalAnswerScore?: number | null; // 已回答的总分数，可能为空
    questionList?: Question[]; // 问题列表
    structureDescription?: string; // 结构说明
    scoreExamEnable?: number; // 是否显示分数
}

export interface QuestionPreviewDialogPropsType {
    question: Question;
    answerDetail?: QuestionAnswerDetail;
    dimensionInfo?: DimensionData;
    editPreview?: boolean;
    questionIndex?: number;
    onGoPre?: () => Promise<any>;
    onGoNext?: () => Promise<any>;
    onGoEdit?: () => void;
    onClose?: () => void;
}
