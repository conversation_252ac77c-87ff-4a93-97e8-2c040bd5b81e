<template>
    <div class="dimension-title">
        <div class="title-text">
            {{ dimensionItem?.dimensionName }}
        </div>
        <div v-if="dimensionItem?.totalQuestionNum" class="title-score">（共{{ dimensionItem.totalQuestionNum }}题，合计{{ dimensionItem.totalScore }}分）</div>
    </div>
    <div v-if="dimensionItem?.structureDescription" class="structure-description">
        {{ dimensionItem.structureDescription }}
    </div>
</template>

<script setup lang="ts">
import type { DimensionData } from './types';

defineOptions({
    name: 'DimensionTitle',
});
defineProps<{
    dimensionItem?: DimensionData;
}>();
</script>

<style lang="less" scoped>
.dimension-title {
    font-size: 18px;
    margin-bottom: 24px;
    display: flex;
    .title-text {
        color: #2d2d2d;
        font-weight: 500;
    }
    .title-score {
        color: #9fa6b5;
    }
}
.structure-description {
    color: #3a3f4d;
    font-size: 13px;
    line-height: 22px;
    margin-bottom: 24px;
}
</style>
