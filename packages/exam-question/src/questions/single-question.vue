<template>
    <component
        :is="components[question.type]"
        ref="componentRef"
        v-model="question"
        class="question-item"
        v-bind="{
            index: questionIndex,
            question,
            score: question.score,
            scoreExamEnable: question.scoreExamEnable,
        }"
        @answerSave="answerSave"
    />
</template>

<script setup lang="ts">
import type { Question } from './types';
import { provide, ref, defineAsyncComponent } from 'vue';

defineOptions({
    name: 'SingleQuestion',
});

const question = defineModel<Question>({
    required: true,
});

const props = withDefaults(
    defineProps<{
        questionIndex?: number;
        scoreExamEnable?: number;
    }>(),
    {
        questionIndex: 0,
        scoreExamEnable: 1,
    },
);

const emit = defineEmits<{
    answerSave: [params: any];
}>();
function answerSave(params: any) {
    emit('answerSave', params);
}

provide('scoreExamEnable', props.scoreExamEnable);

// 动态加载组件
const components: any = {
    1: defineAsyncComponent(() => import('./question-classes/single-choice.vue')),
    2: defineAsyncComponent(() => import('./question-classes/multiple-choice.vue')),
    3: defineAsyncComponent(() => import('./question-classes/single-choice.vue')),
    4: defineAsyncComponent(() => import('./question-classes/question-answer.vue')),
    5: defineAsyncComponent(() => import('./question-classes/fill-blank.vue')),
    6: defineAsyncComponent(() => import('./question-classes/program.vue')),
    7: defineAsyncComponent(() => import('./question-classes/draw.vue')),
    8: defineAsyncComponent(() => import('./question-classes/case.vue')),
};

const componentRef = ref();
</script>
