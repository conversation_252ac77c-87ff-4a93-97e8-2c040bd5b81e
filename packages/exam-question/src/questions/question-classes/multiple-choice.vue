<template>
    <div :id="`question-${question.encryptQuestionId?.replace(/~/g, '')}`">
        <QuestionTitle
            :config="{
                number: number2Serial(index + 1, nested),
                title: question.title,
                score: question.score,
                imgList: question.attachments,
            }"
        />
        <ChoicesImg v-model:answer="answer" :question="question" :options="question.options" />
    </div>
</template>

<script setup lang="ts">
import type { Question } from '../types';
import { debounce } from 'es-toolkit';
import { onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { number2Serial } from '../../utils/methods';
import ChoicesImg from '../components/choices-img.vue';
import QuestionTitle from '../components/question-title.vue';

defineOptions({
    name: 'MultipleChoice',
});

const question = defineModel<Question>({
    required: true,
});

defineProps<{
    index: number;
    nested: boolean;
}>();

const emit = defineEmits<{
    answerSave: [params: any];
}>();

const route = useRoute();
const answer = ref<string[]>([]);
const saveAnswer = debounce(async () => {
    const params = {
        encryptExamId: route?.params.examId,
        encryptId: question.value.encryptId,
        answerList: [
            {
                encryptQuestionId: question.value.encryptQuestionId,
                contentList: answer.value,
                rank: question.value.rank,
                answerAttachments: [],
            },
        ],
    };
    emit('answerSave', params);
}, 500);
function startWatch() {
    watch(
        () => answer.value,
        () => {
            if (answer.value.length > 0) {
                question.value.answerStatus = 1;
            } else {
                question.value.answerStatus = 0;
            }
            saveAnswer();
        },
        {},
    );
}
onMounted(() => {
    answer.value = question.value.answerContentList;
    startWatch();
});
</script>
