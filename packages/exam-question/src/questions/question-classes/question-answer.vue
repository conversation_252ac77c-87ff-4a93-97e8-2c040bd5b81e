<template>
    <div :id="`question-${question.encryptQuestionId?.replace(/~/g, '')}`">
        <div class="title">
            <span class="title-number">{{ number2Serial(index + 1, nested) }}</span>
            <span class="title-content">{{ question.title }}</span>
            <span v-if="scoreExamEnable" class="score">（{{ question.score }}分）</span>
        </div>
        <div v-if="question.attachments && question.attachments.length > 0" class="img-list">
            <div v-for="attachment of question.attachments" :key="attachment.encryptId" class="attachment-wrap">
                <img :src="attachment.url" alt="" @click="clickImage" />
            </div>
        </div>
        <div v-if="question.subQuestionList && question.subQuestionList.length > 0" class="children-list">
            <div v-for="(childItem, childIndex) of question.subQuestionList" :key="`${childItem.encryptId}`" class="child-item">
                <div class="child-title">
                    <span class="title-number">{{ number2Serial(childIndex + 1, true) }}</span>
                    <span class="title-content">{{ childItem.title }}</span>
                </div>
                <!-- <b-textarea
                    v-model="childItem.answer"
                    :autoSize="{
                        minRows: 4,
                        maxRows: 10,
                    }"
                    :maxLength="{
                        length:1000,
                        errorOnly:true
                    }"
                    placeholder="限2000字"
                    showWordLimit
                ></b-textarea> -->
                <b-textarea v-model="childItem.answer" placeholder="限2000字" :maxLength="2000" allowClear showWordLimit @paste.prevent />
            </div>
        </div>
        <div v-else class="single-answer-wrap">
            <b-textarea v-model="question.answer" placeholder="限2000字" :maxLength="2000" allowClear showWordLimit @paste.prevent />
        </div>
    </div>
</template>

<script setup lang="ts">
import type { Question } from '../types';
import { debounce } from 'es-toolkit';
import { inject, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import { number2Serial } from '../../utils/methods';

defineOptions({
    name: 'QuestionAnswer',
});

const question = defineModel<Question>({
    required: true,
});

defineProps<{
    index: number;
    nested: boolean;
}>();

const emit = defineEmits<{
    answerSave: [params: any];
    clickImage: [params: any];
}>();

const scoreExamEnable = inject<boolean>('scoreExamEnable');

function clickImage(e: Event) {
    emit('clickImage', e);
}

const route = useRoute();
const saveAnswer = debounce(async () => {
    const params = {
        encryptExamId: route?.params.examId,
        encryptId: question.value.encryptId,
        answerList: [] as any[],
    };
    if (question.value.subQuestionList && question.value.subQuestionList.length > 0) {
        params.answerList = question.value.subQuestionList.map((subQuestionItem: any) => {
            const result: any = {};
            result.encryptQuestionId = subQuestionItem.encryptQuestionId;
            result.contentList = [subQuestionItem.answer || ''];
            result.rank = subQuestionItem.rank;
            result.answerAttachments = [];
            return result;
        });
    } else {
        params.answerList = [
            {
                encryptQuestionId: question.value.encryptQuestionId,
                contentList: [question.value.answer || ''],
                rank: question.value.rank,
                answerAttachments: [],
            },
        ];
    }
    emit('answerSave', params);
}, 500);
function startWatch() {
    watch(
        question.value,
        () => {
            if (question.value.subQuestionList && question.value.subQuestionList.length > 0) {
                for (let i = 0; i < question.value.subQuestionList.length; i++) {
                    const subQuestionItem = question.value.subQuestionList[i];
                    if (subQuestionItem) {
                        subQuestionItem.answerStatus = subQuestionItem.answer !== undefined && subQuestionItem.answer !== '' && subQuestionItem.answer.trim() !== '' ? 1 : 0;
                    }
                }
                question.value.answerStatus = question.value.subQuestionList.every((x: any) => x.answerStatus) ? 1 : 0;
            } else {
                question.value.answerStatus = question.value.answer !== undefined && question.value.answer !== '' && question.value.answer.trim() !== '' ? 1 : 0;
            }
            saveAnswer();
        },
        {},
    );
}
onMounted(() => {
    if (question.value.subQuestionList && question.value.subQuestionList.length > 0) {
        for (let i = 0; i < question.value.subQuestionList.length; i++) {
            const subQuestionItem = question.value.subQuestionList[i];
            if (subQuestionItem) {
                subQuestionItem.answer = subQuestionItem.answerContentList[0];
            }
        }
    } else {
        question.value.answer = question.value.answerContentList[0];
    }
    startWatch();
});
</script>

<style lang="less" scoped>
.children-list {
    margin-left: 18px;
}
.single-answer-wrap {
    margin-left: 18px;
}
</style>
