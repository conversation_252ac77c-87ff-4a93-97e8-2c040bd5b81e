<template>
    <div :id="`question-${question.encryptQuestionId?.replace(/~/g, '')}`">
        <QuestionTitle
            :config="{
                number: number2Serial(index + 1, nested),
                title: question.title,
                score: question.score,
                imgList: question.attachments,
            }"
            @clickImage="clickImage"
        />
        <div v-for="(childItem, childIndex) of question.subQuestionList" :key="`${childItem.encryptId}`" class="child-wrap">
            <QuestionTitle
                :config="{
                    number: number2Serial(childIndex + 1, true),
                    title: childItem.title,
                    score: childItem.score,
                    imgList: childItem.attachments,
                }"
                @clickImage="clickImage"
            />
            <div class="child-choice-wrap">
                <ChoicesImg v-model:answer="childItem.answer" :question="question" :options="childItem.options" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { Question } from '../types';
import { debounce } from 'es-toolkit';
import { onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import { number2Serial } from '../../utils/methods';
import ChoicesImg from '../components/choices-img.vue';
import QuestionTitle from '../components/question-title.vue';

defineOptions({
    name: 'Case',
});

const question = defineModel<Question>({
    required: true,
});

defineProps<{
    index: number;
    nested: boolean;
}>();
const emit = defineEmits<{
    answerSave: [params: any];
    clickImage: [e: Event];
}>();

function clickImage(e: Event) {
    emit('clickImage', e);
}

const route = useRoute();
const saveAnswer = debounce(async () => {
    const params = {
        encryptExamId: route?.params.examId,
        encryptId: question.value.encryptId,
        answerList: question.value.subQuestionList?.map((subQuestionItem: any) => {
            const result: any = {};
            result.encryptQuestionId = subQuestionItem.encryptQuestionId;
            result.contentList = subQuestionItem.answer ? [subQuestionItem.answer] : [];
            result.rank = subQuestionItem.rank;
            result.answerAttachments = [];
            return result;
        }),
    };
    emit('answerSave', params);
}, 500);
function startWatch() {
    watch(
        () => question.value,
        () => {
            if (question.value.subQuestionList) {
                for (let i = 0; i < question.value.subQuestionList.length; i++) {
                    const subQuestionItem = question.value.subQuestionList[i];
                    subQuestionItem.answerStatus = subQuestionItem.answer !== undefined && subQuestionItem.answer !== '' ? 1 : 0;
                }
                question.value.answerStatus = question.value.subQuestionList.every((x: any) => x.answerStatus) ? 1 : 0;
            }
            saveAnswer();
        },
        { deep: true },
    );
}
onMounted(() => {
    if (question.value.subQuestionList) {
        for (let i = 0; i < question.value.subQuestionList.length; i++) {
            const subQuestionItem = question.value.subQuestionList[i];
            subQuestionItem.answer = subQuestionItem.answerContentList[0];
        }
    }
    startWatch();
});
</script>

<style lang="less" scoped>
.child-wrap {
    & + .child-wrap {
        margin-top: 16px;
    }

    :deep(.question-title-wrap) {
        color: #5d7080;
        margin-left: 18px;

        .score {
            color: #9fa6b5;
        }
    }

    .child-choice-wrap {
        margin-left: 36px;

        :deep(.choices-wrap) {
            // margin-top: -10px !important;

            &.line-four {
                .choice-item {
                    margin-right: 8px;
                    width: 166px;
                }
            }
        }
    }
}
</style>
