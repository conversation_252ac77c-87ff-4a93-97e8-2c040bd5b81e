<template>
    <div :id="`question-${question.encryptQuestionId?.replace(/~/g, '')}`">
        <div class="title">
            <span class="title-number">{{ number2Serial(index + 1, nested) }}</span>
            <span class="title-content">{{ question.title }}</span>
            <span v-if="scoreExamEnable" class="score">（{{ question.score }}分）</span>
        </div>
        <Upload v-model="uploadList" :uploadMethod="questionConfig.onFileUpload" :downloadMethod="questionConfig.onFileDownload" :onError="onError" />
    </div>
</template>

<script setup lang="ts">
import type { IError, IFileItem } from '../components/use-upload';
import type { Attachment, Question } from '../types';
import { debounce } from 'es-toolkit';
import { inject, onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { number2Serial } from '../../utils/methods';
import Upload from '../components/upload.vue';
import { errorTypeEnum, UPLOAD_STATUS } from '../components/use-upload';
import { useQuestionInject } from './hook/use-question';

defineOptions({
    name: 'Draw',
});

const question = defineModel<Question>({
    required: true,
});

defineProps<{
    index: number;
    nested: boolean;
}>();

const emit = defineEmits<{
    answerSave: [params: any];
    fileDownload: [];
}>();

const questionConfig = useQuestionInject();

const scoreExamEnable = inject<boolean>('scoreExamEnable');
const route = useRoute();
const uploadList = ref<Attachment[]>([]);
const saveAnswer = debounce(async () => {
    const params = {
        encryptExamId: route?.params.examId,
        encryptId: question.value.encryptId,
        answerList: [
            {
                encryptQuestionId: question.value.encryptQuestionId,
                contentList: [],
                rank: question.value.rank,
                answerAttachments: uploadList.value.map((x: any) => ({
                    encryptId: x.encryptId,
                    name: x.name,
                })),
            },
        ],
    };
    emit('answerSave', params);
}, 500);
function startWatch() {
    watch(
        () => uploadList.value,
        () => {
            if (uploadList.value.length > 0 && uploadList.value.every((x: any) => x.status === UPLOAD_STATUS.SUCCESS)) {
                question.value.answerStatus = 1;
            } else {
                question.value.answerStatus = 0;
            }
            saveAnswer();
        },
        { deep: true },
    );
}
onMounted(() => {
    uploadList.value = (question.value.answerAttachments ?? []).map((x: any) => ({
        ...x,
        status: UPLOAD_STATUS.SUCCESS,
    }));
    startWatch();
});
function onError(errors: IError[]) {
    for (let i = 0; i < errors.length; i++) {
        if (errors[i].code === errorTypeEnum.SIZE_ERROR) {
            questionConfig.showToast.warning('上传图片大小不能超过 50MB!');
        } else if (errors[i].code === errorTypeEnum.TYPE_ERROR) {
            questionConfig.showToast.warning('上传文件只能是jpg、jpeg、png格式!');
        }
    }
}
</script>
