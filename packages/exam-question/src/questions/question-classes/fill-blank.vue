<template>
    <div :id="`question-${question.encryptQuestionId?.replace(/~/g, '')}`">
        <div>
            <span class="title-number">{{ number2Serial(index + 1, nested) }}</span>
            <span class="title-content">
                <span v-for="(item, i) in title" :key="i">
                    <span>{{ unescape(item) }}</span>
                    <template v-if="i < title.length - 1">
                        【
                        <span ref="blankRef" contenteditable class="blank" @input="(e) => onInput(e, i)" @paste.prevent />
                        】
                        <span v-if="scoreExamEnable && question.scoreList && question.scoreList[i]" class="score"> （{{ question.scoreList[i] }}分） </span>
                    </template>
                </span>
            </span>
            <span v-if="scoreExamEnable" class="score">（合计{{ question.score }}分）</span>
        </div>
        <div v-if="question.attachments && question.attachments.length > 0" class="img-list">
            <div v-for="attachment of question.attachments" :key="attachment.encryptId" class="attachment-wrap">
                <img :src="attachment.url" alt="" @click="clickImage" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { Question } from '../types';
import { debounce } from 'es-toolkit';
import { computed, inject, onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { number2Serial } from '../../utils/methods';
import unescape from '../../utils/unescape';

defineOptions({
    name: 'FillBlank',
});

const question = defineModel<Question>({
    required: true,
});

defineProps<{
    index: number;
    nested: boolean;
}>();

const emit = defineEmits<{
    answerSave: [params: any];
    clickImage: [e: Event];
}>();

const scoreExamEnable = inject<boolean>('scoreExamEnable');

const route = useRoute();

const title = computed(() => {
    if (question.value.title.includes('<hr>')) {
        return question.value.title.split('<hr>');
    } else {
        return question.value.title.split('____');
    }
});

function clickImage(e: Event) {
    emit('clickImage', e);
}

const answerArray = ref<any>(Array.from({ length: title.value.length - 1 }).fill(''));
const onInput = (e: Event, index: number) => {
    const target = e.target as HTMLElement;
    answerArray.value[index] = target.innerHTML;
};
const saveAnswer = debounce(async () => {
    const params = {
        encryptExamId: route?.params.examId,
        encryptId: question.value.encryptId,
        answerList: [
            {
                encryptQuestionId: question.value.encryptQuestionId,
                contentList: answerArray.value,
                rank: question.value.rank,
                answerAttachments: [],
            },
        ],
    };
    emit('answerSave', params);
}, 500);
function startWatch() {
    watch(
        () => answerArray.value,
        () => {
            const valid = answerArray.value.length === title.value.length - 1 && answerArray.value.every((x: any) => x !== undefined && x !== '' && x.trim() !== '');
            if (valid) {
                question.value.answerStatus = 1;
            } else {
                question.value.answerStatus = 0;
            }
            saveAnswer();
        },
        { deep: true },
    );
}
const blankRef = ref();
onMounted(() => {
    for (let i = 0; i < blankRef.value?.length; i++) {
        answerArray.value[i] = question.value.answerContentList[i] || '';
        blankRef.value[i].innerHTML = question.value.answerContentList[i] || '';
    }
    startWatch();
});
</script>

<style lang="less" scoped>
.blank {
    // display: inline-block;
    min-width: 30px;
    border-bottom: 1px solid #1f384c;
    padding: 0 15px;
    outline: none;
    word-break: break-all;
}
.score {
    color: #9fa6b5;
    display: inline-block;
    font-weight: 500;
}
</style>
