<template>
    <div class="choices-wrap" :class="answerLengthClass(options as Array<IchoiceItem>)">
        <b-checkbox-group v-if="[2].includes(question.type)" v-model="answerVal" direction="vertical">
            <div v-for="(choiceItem, choiceIndex) of options" :key="choiceItem.encryptId" class="choice-item">
                <b-checkbox :value="choiceItem.encryptId">
                    <p class="answer-item">
                        <span class="title-number">{{ number2Alphabet(choiceIndex + 1, !!choiceItem.content) }}</span>
                        {{ choiceItem.content }}
                    </p>
                </b-checkbox>
                <div v-if="choiceItem.files && choiceItem.files.length" class="img-wrap">
                    <ImgWrap :list="choiceItem.files" />
                </div>
            </div>
        </b-checkbox-group>

        <b-radio-group v-else v-model="answerVal" direction="vertical">
            <div v-for="(choiceItem, choiceIndex) of options" :key="choiceItem.encryptId" class="choice-item">
                <b-radio :value="choiceItem.encryptId">
                    <p class="answer-item">
                        <span class="title-number">{{ number2Alphabet(choiceIndex + 1, !!choiceItem.content) }}</span>
                        {{ choiceItem.content }}
                    </p>
                </b-radio>
                <div v-if="choiceItem.files && choiceItem.files.length" class="img-wrap">
                    <ImgWrap :list="choiceItem.files" />
                </div>
            </div>
        </b-radio-group>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { number2Alphabet } from '../../utils/methods';
import ImgWrap from './img-wrap.vue';

interface IchoiceItem {
    encryptId: string;
    content: string;
    files: Array<{ uri: string }>;
}

interface IProps {
    options?: Array<IchoiceItem>;
    answer?: Array<string>;
    question?: any;
}
const props = withDefaults(defineProps<IProps>(), {
    options: () => [],
    answer: () => [],
    question: {
        type: 0,
    },
});

const emit = defineEmits<{
    'update:answer': [params: any];
}>();

const answerVal = ref<any>(props.question?.type === 2 ? [] : null);
function answerLengthClass(list: Array<IchoiceItem>) {
    let answerMaxLength = 0;
    let className = '';
    let imgLength = 0;

    list.forEach((item) => {
        if (item) {
            const { content = '', files } = item;
            if (content.length > answerMaxLength) {
                answerMaxLength = content.length;
            }

            if (files && files.length) {
                imgLength += 1;
            }
        }
    });

    if (imgLength !== list.length) {
        return 'line-one';
    }

    if (answerMaxLength <= 8) {
        className = 'line-four';
    } else if (answerMaxLength > 8 && answerMaxLength < 20) {
        className = 'line-tow';
    } else if (answerMaxLength >= 20) {
        className = 'line-one';
    }

    return className;
}

watch(
    () => props.answer,
    () => {
        answerVal.value = props.answer;
    },
    {},
);

watch(
    () => answerVal.value,
    () => {
        if (answerVal.value !== props.answer) {
            emit('update:answer', answerVal.value);
        }
    },
    {},
);
</script>

<style lang="less" scoped>
.choices-wrap {
    // margin-top: 10px;
    color: #2d2d2d;
    font-size: 13px;
    margin-left: 17px;

    &.line-four {
        margin-left: 0 !important;

        .ui-radio-group,
        .ui-checkbox-group {
            margin-top: -10px;
            margin-right: -12px;

            .choice-item {
                margin-right: 12px;
                width: 170px;

                .ui-radio,
                .b-checkbox {
                    margin-right: 0;
                    width: auto;
                    // text-align: center;
                    justify-content: center !important;
                }

                :deep(.img-wrap) {
                    margin: 8px auto !important;
                }
            }
        }
    }

    &.line-tow {
        margin-left: 17px;

        .choice-item {
            width: 320px !important;
        }
    }

    &.line-one {
        margin-left: 17px;

        .ui-radio-group,
        .ui-checkbox-group {
            flex-direction: column;

            .choice-item {
                &:first-child {
                    margin-top: 0;
                }
            }
        }
    }

    .ui-radio-group,
    .ui-checkbox-group {
        display: flex;
        align-items: flex-start;
        flex-wrap: wrap;

        .choice-item {
            width: auto;
            margin-right: 10px;
            margin-top: 10px;
            display: flex;
            flex-direction: column;

            :deep(.ui-checkbox, .ui-radio) {
                display: flex;
                justify-content: left;

                .ui-radio-label,
                .ui-checkbox-label {
                    .answer-item {
                        white-space: nowrap;

                        .title-number {
                            margin-right: 0;
                        }
                    }
                }
            }

            :deep(.img-wrap) {
                margin: 8px 22px;
            }
        }
    }
}
</style>
