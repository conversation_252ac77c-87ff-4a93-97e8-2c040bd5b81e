<!-- eslint-disable vue/require-prop-comment -->
<template>
    <div class="img-list-wrap">
        <template v-for="item in list" :key="item.encryptId">
            <div
                class="img-wrap"
                :style="{
                    // width: `${imgList[index]?.width || 120}px`,
                    width: `${getShowSizeBase(item.showSize || 0)?.horizontal || 120}px`,
                    height: isShowType ? 'auto' : '120px',
                }"
                @click="() => previewImageInject?.open(item.uri)"
            >
                <div class="img-inner">
                    <img
                        class="img"
                        :style="{
                            width: `100%`,
                        }"
                        :src="item.uri"
                    />
                </div>
            </div>
        </template>
    </div>
</template>

<script setup lang="ts">
import { inject } from 'vue';

interface IProps {
    list?: Array<{ uri: string; showSize?: number; url?: string; horizontal?: boolean }>;
    isShowType?: boolean;
}

const props = withDefaults(defineProps<IProps>(), {
    list: [] as any,
    isShowType: false,
});

const previewImageInject = inject<{ open: (uri: string) => void }>('preview-image');

const showSizeMap = {
    // 默认
    0: {
        horizontal: 120,
        vertical: 120,
    },
    // 小
    1: {
        horizontal: 200,
        vertical: 40,
    },
    // 中
    2: {
        horizontal: 460,
        vertical: 78,
    },
    // 大
    3: {
        horizontal: 720,
        vertical: 118,
    },
};

function getShowSizeBase(type: number) {
    const showType = (type === 0 && props.isShowType ? 2 : type) as keyof typeof showSizeMap;
    return showSizeMap[showType];
}

// 图片等比相关计算，暂时取消不用
// function imgonLoad(event, index) {
//     if (!props.isShowType) {
//         return
//     }
//     const data = { ...props.list[index] }

//     const { naturalWidth, naturalHeight } = event.target
//     data.horizontal = true

//     if ((naturalHeight / naturalWidth) >= 2) {
//         data.horizontal = false
//     }

//     if (data.showSize > 0) {
//         console.log('dasdasdasd', data)
//         const { horizontal, vertical } = showSizeMap[data.showSize]
//         // let flag = data.horizontal? horizontal: vertical;
//         const flag = horizontal

//         data.width = flag
//     }

//     imgList.value[index] = { ...data }
// }
</script>

<style lang="less" scoped>
.img-list-wrap {
    overflow: hidden; // 清除浮动
}

.img-wrap {
    // float: left;
    display: inline-block;
    vertical-align: top;
    width: 120px;
    border: 1px solid #efefef;
    // height: 120px;
    // background: #efefef;
    border-radius: 8px;

    overflow: hidden;
    background-position: center;
    background-size: contain;
    background-repeat: no-repeat;

    .img-inner {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
}
</style>
