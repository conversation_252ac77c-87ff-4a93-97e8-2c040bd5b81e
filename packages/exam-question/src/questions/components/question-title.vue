<template>
    <div class="question-title-wrap">
        <div class="title-wrap">
            <span class="number">{{ config.number }}</span>
            <div class="content">
                <slot name="content">
                    {{ config.title }}
                </slot>
                <span v-if="scoreExamEnable" class="score">（{{ config.score }}分）</span>
                <!-- <span class="score">（{{ config.score }}分）</span> -->
            </div>
        </div>
        <div v-if="config.imgList && config.imgList.length > 0" class="title-img-list">
            <ImgWrap :list="config.imgList" :isShowType="true" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { inject } from 'vue';
import ImgWrap from './img-wrap.vue';

defineProps<{
    config: {
        number: number | string;
        title: string;
        score: number;
        imgList: Array<{ uri: string }>;
    };
}>();
const scoreExamEnable = inject<boolean>('scoreExamEnable');
</script>

<style lang="less" scoped>
.question-title-wrap {
    // margin-bottom: 24px;

    .title-wrap {
        display: flex;
        margin-bottom: 10px;

        .number {
            margin-right: 5px;
            font-weight: 500;
            white-space: nowrap;
        }

        .content {
            display: inline;
            font-weight: 500;
            white-space: pre-wrap;
            // word-break: break-all;

            :deep(span) {
                font-weight: 500;
            }
        }

        .score {
            color: #9fa6b5;
            display: inline-block;
        }
    }

    .title-img-list {
        width: 100%;
        margin-left: 17px;

        :deep(.img-wrap) {
            margin-bottom: 8px;
            margin-right: 17px;
        }
    }
}
</style>
