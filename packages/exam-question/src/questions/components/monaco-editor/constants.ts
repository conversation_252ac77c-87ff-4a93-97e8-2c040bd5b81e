export const EDITOR_LANGUAGES = [
    {
        editorLanguage: 'cpp',
        showLanguage: 'C++',
        serverLanguage: 'C++',
        exampleCode: ``,
    },
    {
        editorLanguage: 'c',
        showLanguage: 'C',
        serverLanguage: 'C',
        exampleCode: ``,
    },
    {
        editorLanguage: 'python',
        showLanguage: 'Python3',
        serverLanguage: 'Python3',
        exampleCode: ``,
    },
    {
        editorLanguage: 'java',
        showLanguage: 'Java',
        serverLanguage: 'Java',
        exampleCode: ``,
    },
];
export const MAX_EDITOR_LENGTH = 20000;
export enum EXECUTE_STATUS {
    未运行 = -1,
    已完成 = 0,
    运行中 = 3007,
    编译错误 = 3001,
    系统错误 = 3002,
    时间超限 = 3003,
    内存超限 = 3004,
    输出超限 = 3005,
    运行错误 = 3006,
}
export enum LANGUAGE_STATUS {
    初始,
    未选,
    已选,
}
export const STATUS_CONFIG = {
    [EXECUTE_STATUS.未运行]: {
        name: EXECUTE_STATUS[EXECUTE_STATUS.未运行],
        colorText: '#C7152B',
        colorBackground: '#FEEAE8',
        iconConfig: false,
    },
    [EXECUTE_STATUS.运行中]: {
        name: EXECUTE_STATUS[EXECUTE_STATUS.运行中],
        colorText: '#0072CF',
        colorBackground: '#E8FAFF',
        iconConfig: {
            animate: true,
            content: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16" viewBox="0 0 16 16">
                        <path transform="matrix(1 0 0 1 1.33333 1.33333)" d="M1.1379 9.00193Q0.666667 7.88781 0.666667 6.66667C0.666667 6.29848 0.368187 6 0 6C-0.368187 6 -0.666667 6.29848 -0.666667 6.66667Q-0.666667 8.15819 -0.0901034 9.52134Q0.466577 10.8375 1.48122 11.8521Q2.49586 12.8668 3.812 13.4234Q5.17514 14 6.66667 14Q8.15818 14 9.52134 13.4234Q10.8375 12.8668 11.8521 11.8521Q12.8668 10.8375 13.4234 9.52134Q14 8.15819 14 6.66667Q14 5.17515 13.4234 3.812Q12.8668 2.49586 11.8521 1.48122Q10.8375 0.466577 9.52134 -0.0901039Q8.15819 -0.666667 6.66667 -0.666667L6.66667 0.666667Q7.88781 0.666667 9.00194 1.1379Q10.0786 1.59328 10.9093 2.42403Q11.7401 3.25478 12.1954 4.3314Q12.6667 5.44553 12.6667 6.66667Q12.6667 7.88781 12.1954 9.00194Q11.7401 10.0786 10.9093 10.9093Q10.0786 11.7401 9.00193 12.1954Q7.88781 12.6667 6.66667 12.6667Q5.44552 12.6667 4.3314 12.1954Q3.25478 11.7401 2.42403 10.9093Q1.59328 10.0786 1.1379 9.00193ZM6 0C6 0.368187 6.29848 0.666667 6.66667 0.666667L6.66667 -0.666667C6.29848 -0.666667 6 -0.368187 6 0Z" fill-rule="evenodd" fill="rgb(0, 146, 250)"/>
                    </svg>`,
        },
    },
    [EXECUTE_STATUS.已完成]: {
        name: EXECUTE_STATUS[EXECUTE_STATUS.已完成],
        colorText: '#069038',
        colorBackground: '#E8FFEC',
        iconConfig: false,
    },
    [EXECUTE_STATUS.编译错误]: {
        name: EXECUTE_STATUS[EXECUTE_STATUS.编译错误],
        colorText: '#C7152B',
        colorBackground: '#FEEAE8',
        iconConfig: false,
    },
    [EXECUTE_STATUS.系统错误]: {
        name: EXECUTE_STATUS[EXECUTE_STATUS.系统错误],
        colorText: '#C7152B',
        colorBackground: '#FEEAE8',
        iconConfig: false,
    },
    [EXECUTE_STATUS.运行错误]: {
        name: EXECUTE_STATUS[EXECUTE_STATUS.运行错误],
        colorText: '#C7152B',
        colorBackground: '#FEEAE8',
        iconConfig: false,
    },
    [EXECUTE_STATUS.时间超限]: {
        name: EXECUTE_STATUS[EXECUTE_STATUS.时间超限],
        colorText: '#C7152B',
        colorBackground: '#FEEAE8',
        iconConfig: false,
    },
    [EXECUTE_STATUS.内存超限]: {
        name: EXECUTE_STATUS[EXECUTE_STATUS.内存超限],
        colorText: '#C7152B',
        colorBackground: '#FEEAE8',
        iconConfig: false,
    },
    [EXECUTE_STATUS.输出超限]: {
        name: EXECUTE_STATUS[EXECUTE_STATUS.输出超限],
        colorText: '#C7152B',
        colorBackground: '#FEEAE8',
        iconConfig: false,
    },
} as const;
