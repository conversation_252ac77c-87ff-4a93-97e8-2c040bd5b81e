<template>
    <div class="monaco-editor-container">
        <!-- 编辑器dom -->
        <div ref="editorRef" class="editor" />
    </div>
</template>

<script setup lang="ts">
import type { editorOptionsType } from '../../components/monaco-editor/type';
import { onMounted, ref, watch } from 'vue';
import type * as monaco from 'monaco-editor/esm/vs/editor/editor.api';
interface IProp {
    modelValue?: string;
    options?: editorOptionsType;
    max: number;
}

defineOptions({
    name: 'MonacoEditor',
});

const props = withDefaults(defineProps<IProp>(), {
    modelValue: '',
    options: () => ({}),
});

const emits = defineEmits<{
    (event: 'update:modelValue', content: string): void;
}>();

const editorRef = ref();
const editorContent = ref('');
let editor: monaco.editor.IStandaloneCodeEditor;
/**
 * 创建编辑器实例
 */
function createEditor(dom: HTMLElement, monacoInstance: typeof monaco): monaco.editor.IStandaloneCodeEditor {
    return monacoInstance.editor.create(dom, {
        ...props.options,
        value: props.modelValue,
    });
}

/**
 * 为编辑器绑定事件
 */
function bindEditorEvent(monacoInstance: typeof monaco) {
    editor.onDidChangeModelContent((event) => {
        const content = editor.getValue();
        let newContent = content;

        if (content.length > props.max) {
            // 获取当前光标位置
            const position = editor.getPosition();
            // 截断内容并更新编辑器内容
            newContent = content.slice(0, props.max);
            editor.setValue(newContent);
            // 恢复光标位置
            if (position) {
                editor.setPosition(position);
            }
        }

        editorContent.value = newContent;
        emits('update:modelValue', newContent);
    });
}

onMounted(async () => {
    const monacoInstance = await import('monaco-editor/esm/vs/editor/editor.api');
    editor = createEditor(editorRef.value, monacoInstance);
    bindEditorEvent(monacoInstance);

    watch(
        () => props.options.language,
        () => {
            monacoInstance.editor.setModelLanguage(editor.getModel() as monaco.editor.IModel, props.options.language as string);
        },
        {
            immediate: true,
        },
    );
});
</script>

<style lang="less" scoped>
.monaco-editor-container {
    .editor {
        height: 100%;
    }
}
</style>
