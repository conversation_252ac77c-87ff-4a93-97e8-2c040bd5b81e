import { ref } from 'vue';

interface IConfig {
    max: number;
    allowSize: number; // 单位为mb
    allowExt: string[]; // 格式数组
    onError: (errors: IError[]) => void;
    onSuccess: (res: any, fileItem: IFileItem) => void;
    autoValidate: boolean;
    beforeUpload?: (fileList: IFileItem[]) => Promise<boolean>;
    autoUpload: boolean;
    action: (params: any, fileItem: IFileItem) => Promise<any>;
    extraParams?: Record<string, any>;
}
export enum errorTypeEnum {
    /** 大小不合规 */
    SIZE_ERROR = 101,
    /** 类型不合规 */
    TYPE_ERROR = 102,
    /** 请求错误 */
    REQUEST_ERROR = 103,
    /** 数量超过最大值 */
    EXCEED_ERROR = 104,
}
interface ISizeError {
    code: errorTypeEnum.SIZE_ERROR;
    name: '文件大小不合规';
    message: { limited: number; fileSize: number; fileName: string; fileId: number };
}
interface ITypeError {
    code: errorTypeEnum.TYPE_ERROR;
    name: '文件类型不合规';
    message: { limited: string[]; fileExt: string; fileName: string; fileId: number };
}
interface IRequestError {
    code: errorTypeEnum.REQUEST_ERROR;
    name: '请求错误';
    message: { error: any; fileName: string; fileId: number };
}
interface IExceedError {
    code: errorTypeEnum.EXCEED_ERROR;
    name: '数量超限';
    message: { limited: number; fileId?: number };
}
export type IError = ISizeError | ITypeError | IRequestError | IExceedError;

export interface IFileItem {
    uid: number;
    file: File;
    status: UPLOAD_STATUS;
    progress?: number;
    responseBody: Record<string, any>;
    uploadAction: () => Promise<void>;
}
export enum UPLOAD_STATUS {
    FAIL = -1, // 上传失败
    INITIAL = 0, // 上传初始化
    IN_PROGRESS = 1, // 上传中
    SUCCESS = 2, // 上传成功
}
export default function (config: IConfig) {
    const fileList = ref<IFileItem[]>([]);

    function getValidFiles(fileArray: any[]) {
        const allowCount = config.max - fileList.value.length;
        const __r = fileArray.slice(0, allowCount);
        return __r;
    }
    function onChange(e: Event) {
        const inputEl = e.target as HTMLInputElement;
        const fileArrayNormalized = Array.from(inputEl.files || []);
        const fileArrayValid = getValidFiles(fileArrayNormalized);
        if (fileArrayValid.length > 0) {
            for (let i = 0; i < fileArrayValid.length; i++) {
                const timeStamp = Date.now() + i;
                fileList.value.push({
                    uid: timeStamp,
                    file: fileArrayValid[i],
                    status: UPLOAD_STATUS.INITIAL,
                    progress: 0,
                    responseBody: {},
                    uploadAction: () => executeUpload(fileArrayValid[i], timeStamp),
                });
            }
        } else {
            config.onError([
                {
                    code: errorTypeEnum.EXCEED_ERROR,
                    name: '数量超限',
                    message: { limited: config.max },
                },
            ]);
        }
        inputEl.value = '';
    }
    async function executeUpload(file: File, fileUid: number) {
        const fileItem = fileList.value.find((x) => x.uid === fileUid);
        if (!fileItem) {
            return;
        }
        fileItem.status = UPLOAD_STATUS.IN_PROGRESS;
        if (config.autoValidate) {
            const errorParams = validateUpload(file, fileUid);
            if (errorParams.length) {
                config.onError(errorParams);
                fileItem.status = UPLOAD_STATUS.FAIL;
                return;
            }
        }
        if (config.beforeUpload) {
            const beforeUploadTruth = await config.beforeUpload(fileList.value);
            if (!beforeUploadTruth) {
                return;
            }
        }
        /** 处理参数 */
        const formData = new FormData();
        formData.append('file', file);
        if (config.extraParams) {
            for (const key in config.extraParams) {
                if (Object.prototype.hasOwnProperty.call(config.extraParams, key)) {
                    formData.append(key, config.extraParams[key]);
                }
            }
        }
        /** 处理参数 */
        try {
            const res = await config.action(formData, fileItem);
            config.onSuccess(res, fileItem);
        } catch (error: any) {
            config.onError([
                {
                    code: errorTypeEnum.REQUEST_ERROR,
                    name: '请求错误',
                    message: { error, fileName: file.name, fileId: fileUid },
                },
            ]);
            fileItem.status = UPLOAD_STATUS.FAIL;
        }
    }
    function validateUpload(file: File, fileUid: number): IError[] {
        const errorParams = [];
        const _fileSize = file.size / 1024 / 1024;
        const isFileSizeValid = _fileSize < config.allowSize;
        const isFileExtValid = config.allowExt.includes(file.type);
        if (!isFileSizeValid) {
            errorParams.push({
                code: errorTypeEnum.SIZE_ERROR as const,
                name: '文件大小不合规' as const,
                message: { limited: config.allowSize, fileSize: _fileSize, fileName: file.name, fileId: fileUid },
            });
        }
        if (!isFileExtValid) {
            errorParams.push({
                code: errorTypeEnum.TYPE_ERROR as const,
                name: '文件类型不合规' as const,
                message: { limited: config.allowExt, fileExt: file.type, fileName: file.name, fileId: fileUid },
            });
        }

        return errorParams;
    }
    function submit() {
        const fileListClone = [...fileList.value];
        for (let i = 0; i < fileListClone.length; i++) {
            const fileItem = fileListClone[i];
            if (fileItem && fileItem.status === UPLOAD_STATUS.INITIAL) {
                fileItem.uploadAction();
            }
        }
    }
    return { fileList, onChange, executeUpload, submit };
}
