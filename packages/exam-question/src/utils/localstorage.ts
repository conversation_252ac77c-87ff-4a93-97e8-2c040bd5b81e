/**
 * 对本地存贮对象
 */

const Storage = {
    get(key: string) {
        const storage = this._getStorage();
        let str = '';
        if (storage) {
            str = storage.getItem(key) || '';
            return str && JSON.parse(str);
        }
    },
    set(key: string, value: object | string) {
        const storage = this._getStorage();
        if (key && typeof value !== 'undefined' && storage) {
            storage.setItem(key, JSON.stringify(value));
        }
    },
    /**
     * 清除本地存贮数据
     * @param {string} isPrefix 可选，如果包含此参数，则删除的是包含key的所有项，如果什么都不传则清空
     */
    del(key: string, isPrefix?: string) {
        const storage = this._getStorage();
        if (storage) {
            if (key && isPrefix) {
                const entries = Object.keys(storage);
                entries.forEach((i) => {
                    if (i.indexOf(isPrefix) === 0) {
                        storage.removeItem(i);
                    }
                });
            } else if (key) {
                storage.removeItem(key);
            } else {
                storage.clear();
            }
        }
    },
    _getStorage() {
        let _localStorage;
        try {
            /* 在Android 4.0下，如果webview没有打开localStorage支持，在读取localStorage对象的时候会导致js运行出错，所以要放在try{}catch{}中 */
            _localStorage = window.localStorage;
        } catch (e) {
            _localStorage = null;
        }

        return _localStorage;
    },
};

export default Storage;
