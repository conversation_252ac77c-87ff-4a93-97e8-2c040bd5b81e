// 截流
export function throttle<T extends Fn>(fn: T, limit = 300): (...args: Parameters<T>) => ReturnType<T> {
    let result: any;
    let running = false;
    let lastExecution = false;
    let _args: any;

    return (...args: any[]) => {
        if (!running) {
            running = true;

            setTimeout(() => {
                running = false;

                if (!lastExecution) {
                    lastExecution = true;

                    return fn(..._args);
                }
            }, limit);

            result = fn(...args);
            lastExecution = true;
        } else {
            _args = [...args];
            lastExecution = false;
        }

        return result;
    };
}

/**
 * parseURL
 * @param {string} window.location.href || url
 */

export function parseURL(url: string) {
    const a = document.createElement('a');
    a.href = url;
    return {
        origin: a.origin,
        pathname: a.pathname,
        source: url,
        protocol: a.protocol.replace(':', ''),
        host: a.hostname,
        port: a.port,
        query: a.search,
        params: (function () {
            let params = {};
            const seg = a.search.replace(/^\?/, '').split('&');
            const len = seg.length;
            let p;
            for (let i = 0; i < len; i++) {
                if (seg[i]) {
                    p = seg[i].split('=');
                    params = {
                        [p[0]]: p[1],
                        ...params,
                    };
                }
            }
            return params;
        })(),
        hash: a.hash.replace('#', ''),

        path: a.pathname.replace(/^([^/])/, '/$1'),
    };
}

// 对象序列化
export function formatParams(obj: { [x: string]: any }, needBlank = false) {
    let str = '';

    const entires = Object.entries(obj);
    entires.forEach((item) => {
        if (needBlank || (item[1] && !needBlank)) {
            str += str.length ? `&${item[0]}=${item[1]}` : `${item[0]}=${item[1]}`;
        }
    });

    return str;
}

// 判断字符串长度（英文占0.5个字符，中文汉字占1个字符）
export function zhLength(strVal = ''): number {
    let realLength = 0;
    const str = strVal.replace(/(^\s*)|(\s*$)/g, '');
    const len = str.length;
    let charCode = -1;
    for (let i = 0; i < len; i++) {
        charCode = str.charCodeAt(i);
        if (charCode > 255) {
            // 大于255的包括中文字、全角符号、特殊字
            realLength += 1;
        } else {
            realLength += 0.5;
        }
    }
    return Math.ceil(realLength);
}

// 数字补0
export function addZero(val: number): string {
    if (Number.isNaN(val)) {
        return `${val}`;
    }
    return val > 9 ? `${val}` : `0${val}`;
}

// 日期格式化
export function formatDate(val: any, formatStr = 'yyyy-MM-dd') {
    if (!val) {
        return '';
    }
    let date = val;
    if (typeof val === 'string') {
        // 字符类型
        date = new Date(val.replace(/-/g, '/'));
    } else if (typeof val === 'number') {
        // 时间戳
        date = new Date(val);
    }

    let str = formatStr;
    str = str.replace(/yyyy|YYYY/, `${date.getFullYear()}`);
    str = str.replace(/MM/, addZero(date.getMonth() + 1));
    str = str.replace(/M/g, `${date.getMonth() + 1}`);
    str = str.replace(/dd|DD/, addZero(date.getDate()));
    str = str.replace(/d|D/g, `${date.getDate()}`);
    str = str.replace(/hh|HH/, addZero(date.getHours()));
    str = str.replace(/h|H/g, `${date.getHours()}`);
    str = str.replace(/mm/, addZero(date.getMinutes()));
    str = str.replace(/m/g, `${date.getMinutes()}`);
    str = str.replace(/ss|SS/, addZero(date.getSeconds()));
    str = str.replace(/s|S/g, `${date.getSeconds()}`);
    return str;
}

// 手机号正则
export function validateMobile(mobile: string): boolean {
    if (!mobile) {
        return false;
    }
    const reg = /^1[3-9]\d{9}$/;
    return reg.test(mobile);
}

// 货币格式化
export function formatMoney(value: number | string, direct?: boolean) {
    if ((!value && `${value}` !== '0') || Number.isNaN(value)) {
        return '';
    }
    let str = direct ? value : Number(value).toFixed(2);
    str += '';
    return String(str).replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,');
}

// 格式化数字  999999999 -> 999,999,999
// 超过 limit = 5 位数：100000000 -> 99,999+
export const formatNumber: (value: string | number | undefined | null, limit?: number) => string = (value, limit) => {
    const str = value ?? '';
    if (limit) {
        const max = 10 ** limit - 1;
        if (+str > max) {
            return `${`${max}`.replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,')}+`;
        }
    }
    return `${str}`.replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,');
};

/**
 * 导出
 * @param url
 * @param name
 */
export function exportFile(url: string, name?: string) {
    const _a: any = document.createElement('a');
    _a.hidden = 'hidden';
    _a.target = '_blank';
    _a.download = name || new Date().getTime();
    _a.href = (url || '').replace('http:', 'https:'); // 会影响blob对象的下载
    document.body.appendChild(_a);
    _a.click();
    document.body.removeChild(_a);
}
// 比较两个数组内容是否完全一致。（忽略位置）[1,2] [2,1] 为相等
export function compareArray(a1: Array<number | string>, a2: Array<number | string>): boolean {
    if (a1 === a2) {
        return true;
    }
    if ((!a1 && a2) || (a1 && !a2)) {
        return false;
    }
    if (a1.length !== a2.length) {
        return false;
    }
    a1.sort();
    a2.sort();
    for (let i = 0, n = a1.length; i < n; i++) {
        if (a1[i] !== a2[i]) {
            return false;
        }
    }
    return true;
}
// 正则表达式中，需要转义的特殊符号处理。
export function quote(regex: string): string {
    return regex.replace(/([()[{*+.$^\\|?])/g, '\\$1');
}

/**
 * 处理富文本的显示逻辑
 * @param richText 富文本内容
 * @param containerElement 富文本展示的容器
 * @param callback
 */
export function displayRichText({ richText, containerElement }: { richText: string; containerElement: Element }, callback?: Function) {
    if (!(containerElement instanceof HTMLElement)) {
        throw new TypeError('富文本容器不是HTML元素!');
    }

    if (containerElement.attachShadow) {
        const shadowDom = containerElement.attachShadow({ mode: 'open' });
        // const richTextContainer = document.createElement('div');

        callback?.(shadowDom);

        // richTextContainer.innerHTML = richText;

        // shadowDom.appendChild(richTextContainer);
        shadowDom.innerHTML = richText;
    } else {
        containerElement.innerHTML = richText;
    }
}

/**
 * 数组取差集
 * arr1:[1,2,3]
 * arr2:[1,2]
 * -> [3]
 */
export function arrSubtraction(arr1: [], arr2: []) {
    return arr1.filter((x) => !arr2.includes(x));
}

export function formatNumber2Time(value: any) {
    let secondTime: any = Number.parseInt(value); // 秒
    let minuteTime: any = 0; // 分
    let hourTime: any = 0; // 小时
    if (secondTime >= 60) {
        minuteTime = Number.parseInt(String(secondTime / 60));
        secondTime = Number.parseInt(String(secondTime % 60));
        if (minuteTime >= 60) {
            hourTime = Number.parseInt(String(minuteTime / 60));
            minuteTime = Number.parseInt(String(minuteTime % 60));
        }
    }
    const time = `${hourTime >= 10 ? hourTime : `0${hourTime}`}:${minuteTime >= 10 ? minuteTime : `0${minuteTime}`}:${secondTime >= 10 ? secondTime : `0${secondTime}`}`;

    // if (minuteTime > 0) {
    //     time = '' + parseInt(minuteTime) + ':' + time;
    // }
    // if (hourTime > 0) {
    //     time = '' + parseInt(hourTime) + ':' + time;
    // }
    return time;
}
// 是否为移动端
export function isMobile() {
    const ua = navigator.userAgent.toLowerCase();
    return /mobile|android|iphone|ipod|phone|ipad/i.test(ua);
}

export const isIpad = navigator.userAgent.match(/(iPad)/) || (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);

export function dataURLtoFile(dataurl: any, filename: any) {
    const arr = dataurl.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, { type: mime });
}
export function generateWaterMarkConfig(name: string, id: number | string) {
    return {
        text: [`${name}-${id}`], // 渲染文字内容,数组控制换行
        rotate: -10,
        opacity: 0.005,
        fontSize: [12, 28],
        fontColor: '#ff0000',
        lineHeight: [23, 23],
        paddingWidth: 10,
        paddingHeight: 25,
    };
}

/**
 * base64转file
 */
// 将 Base64 编码字符串转换为 Blob 对象

export function base64ToUint8Array(base64: string) {
    const binaryString = window.atob(base64); // 已弃用，但可以使用 TextDecoder 解决
    const len = binaryString.length;
    const bytes = new Uint8Array(len);
    for (let i = 0; i < len; i++) {
        bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes;
}
// 将 Base64 字符串转化为 File 对象
export function base64ToFile(base64String: string, fileName: string) {
    const parts = base64String.split(',') as any;
    const mimeType = parts[0].match(/:(.*?);/)[1];
    const base64Data = parts[1];
    const byteArrays = base64ToUint8Array(base64Data);
    const blob = new Blob([byteArrays], { type: mimeType });
    const file = new File([blob], fileName, { type: mimeType });
    return file;
}

// 获取颜色分布
function getColorDistribution(imageData: any) {
    const colorDist = Array.from({ length: 256 }).fill(0); // 存储每个颜色值的出现次数
    for (let i = 0; i < imageData.length; i += 4) {
        const r = imageData[i];
        const g = imageData[i + 1];
        const b = imageData[i + 2];
        const brightness = 0.2126 * r + 0.7152 * g + 0.0722 * b; // 计算亮度
        colorDist[Math.round(brightness)]++; // 增加亮度对应的颜色值出现次数
    }
    return colorDist;
}

export function checkCameraBlockage(base64String: string) {
    // 改成promise写法
    return new Promise((resolve, reject) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d') as any;
        const img = new Image();
        img.src = base64String;
        img.onload = () => {
            canvas.width = img.width;
            canvas.height = img.height;
            // 将视频帧绘制到 canvas
            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
            // 获取图像数据
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;

            // 计算整个图像的平均亮度
            let totalBrightness = 0;
            for (let i = 0; i < data.length; i += 4) {
                // RGB 转换为亮度 (0.2126*R + 0.7152*G + 0.0722*B)
                const brightness = 0.2126 * data[i] + 0.7152 * data[i + 1] + 0.0722 * data[i + 2];
                totalBrightness += brightness;
            }
            const averageBrightness = totalBrightness / (data.length / 4);

            // 判断亮度是否低于某个阈值
            if (averageBrightness < 30) {
                // 阈值可以根据实际情况调整
                resolve(true);
            } else {
                // 检查颜色分布是否高度集中
                const colorDist = getColorDistribution(data);
                const maxColorCount = Math.max(...colorDist);
                const totalPixels = canvas.width * canvas.height;
                const colorDistributionThreshold = 0.9; // 颜色分布阈值可以根据实际情况调整
                if (maxColorCount > totalPixels * colorDistributionThreshold) {
                    resolve(true);
                } else {
                    resolve(false);
                }
            }
        };
    });
}

/**
 * 截取视频的当前帧
 */
export function getCurrentFrame(video: HTMLVideoElement) {
    const canvas = document.createElement('canvas');
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    const ctx = canvas.getContext('2d');
    ctx?.drawImage(video, 0, 0, canvas.width, canvas.height);
    return canvas.toDataURL('image/png');
}
export function numberToChinese(num: number): string {
    if (num < 1 || num > 99) {
        return '数字范围应在1到99之间';
    }
    const digits: string[] = ['一', '二', '三', '四', '五', '六', '七', '八', '九'];
    const tens: string[] = ['', '十', '二十', '三十', '四十', '五十', '六十', '七十', '八十', '九十'];
    if (num <= 10) {
        return digits[num - 1];
    } else if (num < 20) {
        return `十${digits[num - 11]}`;
    } else {
        const ten = Math.floor(num / 10);
        const digit = num % 10;

        if (digit === 0) {
            return tens[ten];
        } else {
            return tens[ten] + digits[digit - 1];
        }
    }
}

/**
 * 生成一个 uuid
 * @param len 长度
 * @param baseRadix 基数
 * @returns
 */
export function genUuid(len: number, baseRadix: number) {
    const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
    const uuid = [];
    let i;
    const radix = baseRadix || chars.length;
    if (len) {
        for (i = 0; i < len; i++) {
            uuid[i] = chars[0 | (Math.random() * radix)];
        }
    } else {
        let r;
        uuid[8] = '-';
        uuid[13] = '-';
        uuid[18] = '-';
        uuid[23] = '-';
        uuid[14] = '4';
        for (i = 0; i < 36; i++) {
            if (!uuid[i]) {
                r = 0 | (Math.random() * 16);
                uuid[i] = chars[i === 19 ? (r & 0x3) | 0x8 : r];
            }
        }
    }
    return uuid.join('');
}
