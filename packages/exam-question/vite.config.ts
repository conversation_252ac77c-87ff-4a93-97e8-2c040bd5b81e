import { fileURLToPath, URL } from 'node:url';
import { BossDesignResolver } from '@boss/design-resolver';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import AutoImport from 'unplugin-auto-import/vite'; // 自动导入组件
import components from 'unplugin-vue-components/vite'; // 自动导入组件
import { defineConfig } from 'vite';
import viteCompression from 'vite-plugin-compression';
import dts from 'vite-plugin-dts';
import { libInjectCss } from 'vite-plugin-lib-inject-css';

export default defineConfig(({ command, mode }) => ({
    plugins: [
        dts({ insertTypesEntry: true, tsconfigPath: './tsconfig.json' }),
        vue(),
        vueJsx(),
        libInjectCss(),
        viteCompression({
            algorithm: 'gzip',
            threshold: 10240,
        }),
        components({
            resolvers: [BossDesignResolver()],
        }),
        AutoImport({
            resolvers: [BossDesignResolver({ autoImport: true })],
        }),
    ],
    esbuild: {
        drop: mode === 'production' ? ['console', 'debugger'] : undefined,
    },
    base: '/',
    build: {
        rollupOptions: {
            external: ['vue', 'vue-router', 'pinia', '@boss/design'],
        },
        sourcemap: true,
        assetsInlineLimit: 1024, // 小于1kb的资源内联
        lib: {
            entry: 'src/index.ts',
            fileName: 'index',
            formats: ['es'],
        },
    },
    resolve: {
        alias: {
            '@': fileURLToPath(new URL('./src', import.meta.url)),
        },
    },
}));
