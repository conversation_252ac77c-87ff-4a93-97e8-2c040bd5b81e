{"name": "kaoshi-fe-web", "version": "0.0.0", "private": true, "beeGazeId": "1658700ccd36f251e4855fa5eb7479c0", "scripts": {"clear": "node scripts/remove.js", "dev": "turbo run dev", "init": "pnpm run clear && pnpm i", "lint": "eslint . --fix --cache", "postcheckout": "pnpm i", "precommit": "lint-staged", "preinstall": "npx only-allow pnpm", "prepare": "husky", "prepush": "git fetch --all", "publish": "pnpm run publish:prepare && pnpm run publish:changeset && git add -A && git commit -m 'feat: publish' && pnpm changeset publish", "publish:changeset": "pnpm changeset && pnpm changeset version", "publish:prepare": "pnpm i", "setup:cache": "node scripts/setup-remote-cache.js", "test": "vitest watch", "test:ci": "vitest run", "tsc": "vue-tsc --noEmit", "tsc:build": "vue-tsc --build", "tsc:clean": "vue-tsc --build --clean", "typecheck": "pnpm run tsc", "typecheck:build": "pnpm run tsc:build", "typecheck:watch": "vue-tsc --noEmit --watch"}, "lint-staged": {"*": ["eslint --fix --cache", "git add -A"]}, "dependencies": {"@boss/utils": "2.0.13", "@bzl/zhice-exam-question": "workspace:*", "@crm/exam-components": "workspace:*", "@crm/exam-constants": "workspace:*", "@crm/exam-directives": "workspace:*", "@crm/exam-hooks": "workspace:*", "@crm/exam-types": "workspace:*", "@crm/exam-utils": "workspace:*", "@crm/vueuse-pro": "2.1.0", "@datastar/warlock-jssdk": "2.2.8", "@micro-zoe/micro-app": "1.0.0-rc.18", "@nebula/bodydetect-web": "2.0.5", "@types/js-cookie": "^3.0.6", "axios": "1.8.4", "bowser": "2.11.0", "dayjs": "1.11.13", "es-toolkit": "1.39.6", "js-cookie": "^3.0.5", "lodash": "4.17.21", "lodash-es": "4.17.21", "mitt": "3.0.1", "monaco-editor": "0.52.2", "mqtt": "4.3.8", "msw": "2.7.5", "node-fetch": "3.3.2", "pinia": "2.3.1", "protobufjs": "7.5.0", "qrcode": "1.5.4", "v-viewer": "3.0.22", "vue": "3.5.13", "vue-draggable-plus": "0.6.0", "vue-router": "4.5.0", "woodpecker-monitor": "2.7.2"}, "devDependencies": {"@boss/apm-sdk-types": "^1.0.0", "@boss/design": "1.7.117", "@boss/design-resolver": "1.7.117", "@boss/eslint-config": "2.0.0", "@boss/prettier-config": "1.7.117", "@boss/stylelint-config": "1.7.117", "@boss/tsconfig": "1.7.117", "@changesets/cli": "2.29.2", "@commitlint/cli": "19.8.0", "@commitlint/config-conventional": "19.8.0", "@crm/eslint-config": "1.0.0", "@eslint/js": "10.0.0", "@rollup/plugin-inject": "5.0.5", "@types/lodash-es": "4.17.12", "@types/node": "22.14.1", "@types/qrcode": "1.5.5", "@types/qs": "6.9.18", "@types/uuid": "10.0.0", "@types/webfontloader": "1.6.38", "@vitejs/plugin-legacy": "6.1.1", "@vitejs/plugin-vue": "5.2.3", "@vitejs/plugin-vue-jsx": "4.1.2", "@vitest/coverage-istanbul": "3.1.2", "@vitest/ui": "3.1.2", "@vue/test-utils": "^2.4.6", "autoprefixer": "10.4.21", "eslint": "9.25.1", "eslint-config-prettier": "10.1.2", "eslint-plugin-prettier": "5.2.6", "eslint-plugin-vue": "10.0.0", "glob": "11.0.1", "globals": "15.15.0", "husky": "9.1.7", "jiti": "2.4.2", "less": "4.3.0", "lint-staged": "15.5.1", "only-allow": "1.2.1", "ora": "6.3.1", "prettier": "3.5.3", "rollup-plugin-visualizer": "5.14.0", "semver": "7.7.1", "sourcemap-set-path-plugin": "1.2.3", "stylelint": "16.18.0", "taze": "18.7.1", "tinyglobby": "0.2.13", "turbo": "2.5.0", "typescript": "5.8.3", "typescript-eslint": "8.31.0", "unplugin-auto-import": "19.1.2", "unplugin-vue-components": "28.5.0", "uuid": "11.1.0", "vite": "6.3.2", "vite-plugin-checker": "0.9.1", "vite-plugin-compression": "0.5.1", "vite-plugin-dts": "4.5.3", "vite-plugin-jsonx": "1.1.2", "vite-plugin-lib-inject-css": "2.2.2", "vite-plugin-node-polyfills": "0.23.0", "vite-plugin-svg-icons": "2.0.1", "vitest": "3.1.2", "vue-tsc": "2.2.8"}, "packageManager": "pnpm@10.9.0", "engines": {"node": ">=22.13.0", "pnpm": ">=10.0.0"}, "pnpm": {"peerDependencyRules": {"allowedVersions": {"eslint": "9", "stylelint": "16", "vite": "6", "vue": "3"}}}}