# fe-kaoshi-web 本地代码门禁实施文档

> 基于现代前端 Monorepo 本地代码门禁最佳实践

## 1. 项目门禁架构概览

本项目已实施一套完整的本地代码门禁系统，遵循分层防御原则，通过多重检查确保代码质量和一致性。

### 技术栈
- Git Hooks 管理：`husky`
- 暂存区任务运行：`lint-staged`
- Monorepo 任务编排：`Turborepo`
- 代码规范检查：`ESLint`、`Stylelint`、`Prettier`
- 类型检查：`TypeScript`
- 单元测试：`Vitest`
- Commit 消息规范：`commitlint`

### 检查分层
- **Pre-Commit**：快速检查暂存区文件的格式和规范
- **Commit-msg**：确保提交信息符合规范
- **Pre-Push**：全面检查代码质量和构建可行性

## 2. Pre-Commit 阶段

### 2.1 已实现的功能

当执行 `git commit` 命令时，会自动触发以下检查：

```bash
# 通过 lint-staged 运行对暂存区文件的检查
pnpm run precommit
```

`lint-staged` 配置（package.json）：

```json
"lint-staged": {
    "*": [
        "eslint --fix --cache",
        "git add -A"
    ]
}
```

这些检查可以：
- 自动修复格式问题
- 检查并修复简单的 ESLint 规则违反
- 确保提交的代码符合项目规范

### 2.2 优化建议

为进一步提升 Pre-Commit 检查效率，建议：

1. **细化 lint-staged 配置**，针对不同文件类型使用不同工具：

```js
// 建议在根目录创建 .lintstagedrc.js
module.exports = {
  "*.{js,jsx,ts,tsx,vue}": [
    "eslint --fix --cache",
    "prettier --write"
  ],
  "*.{css,less,scss}": [
    "stylelint --fix",
    "prettier --write"
  ],
  "*.{json,md}": [
    "prettier --write"
  ]
}
```

2. **添加快速敏感信息检测**，阻止密钥意外提交：

```bash
# 在 .husky/pre-commit 中添加
npx gitleaks protect --staged --verbose
```

## 3. Commit-msg 阶段

### 3.1 已实现的功能

当完成 commit 时，会校验提交信息格式：

```bash
npx --no-install commitlint --edit "$1"
```

commitlint 配置（commitlint.config.js）已支持多种提交类型：
- feat: 新功能
- fix: 修复bug
- docs: 文档变更
- style: 代码格式变更
- refactor: 代码重构
- 等等...

### 3.2 优化建议

为提升开发体验，建议：

1. **添加 commitizen 支持**，引导开发者编写规范的提交信息：

```bash
# 安装依赖
pnpm add -D commitizen cz-conventional-changelog

# 在 package.json 中添加
"config": {
  "commitizen": {
    "path": "cz-conventional-changelog"
  }
},
"scripts": {
  "commit": "cz"
}
```

开发者可通过 `pnpm commit` 代替 `git commit`，获得交互式提交体验。

## 4. Pre-Push 阶段

### 4.1 已实现的功能

目前 pre-push 钩子运行：

```bash
# 更新远程分支信息
npm run prepush
```

### 4.2 优化建议

建议在 pre-push 阶段添加更全面的检查：

1. **结合 Turborepo 进行智能检查**，修改 `.husky/pre-push` 文件：

```bash
#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

# 获取最新的远程分支信息
pnpm run prepush

# 运行类型检查 (仅针对变更的包)
echo "🔍 运行类型检查..."
pnpm turbo run typecheck --filter=[HEAD^1]

# 运行测试 (仅针对变更的包)
echo "🧪 运行测试..."
pnpm turbo run test --filter=[HEAD^1] || { echo "❌ 测试失败，推送终止"; exit 1; }

# 验证构建 (仅针对变更的包)
echo "🔨 验证构建..."
pnpm turbo run build --filter=[HEAD^1] || { echo "❌ 构建失败，推送终止"; exit 1; }
```

2. **添加 package.json 相应脚本**：

```json
"scripts": {
  "typecheck": "vue-tsc --noEmit",
  "prepush": "git fetch --all"
}
```

## 5. code-guard 工具实现

为统一管理各种检查工具，建议实现一个 code-guard 包作为项目内部 CLI 工具：

### 5.1 创建基础结构

```
packages/code-guard/
├── bin/
│   └── code-guard.js         # CLI 入口
├── src/
│   ├── commands/           # 命令实现
│   │   ├── check.js        # 执行检查
│   │   ├── lint.js         # 运行 lint
│   │   └── test.js         # 运行测试
│   ├── utils/              # 工具函数
│   │   ├── git.js          # Git 相关操作
│   │   └── turbo.js        # Turborepo 调用
│   └── index.js            # 主入口
├── package.json
└── README.md
```

### 5.2 实现功能

建议在 code-guard 工具中实现以下功能：

1. **智能检测变更范围**：基于 Git 变更分析，只检查受影响的包
2. **多级检查模式**：
   - `code-guard staged`：快速检查暂存区文件（用于 pre-commit）
   - `code-guard branch`：检查当前分支变更（用于 pre-push）
   - `code-guard full`：全面深度检查（用于发布前）
3. **检查结果可视化**：清晰展示各项检查的通过/失败状态
4. **本地构建验证**：自动验证受影响包的构建

### 5.3 与 Turborepo 集成

```js
// packages/code-guard/src/utils/turbo.js
const { execSync } = require('child_process');

/**
 * 运行 Turborepo 命令
 * @param {string} task - 任务名称（如 'build', 'test'）
 * @param {string} filter - 过滤器（如 '[HEAD^1]'）
 * @returns {boolean} 是否成功
 */
function runTurboTask(task, filter) {
  try {
    const cmd = `pnpm turbo run ${task} ${filter ? `--filter="${filter}"` : ''}`;
    console.log(`执行: ${cmd}`);
    execSync(cmd, { stdio: 'inherit' });
    return true;
  } catch (error) {
    console.error(`❌ ${task} 失败`);
    return false;
  }
}

module.exports = { runTurboTask };
```

## 6. VS Code 集成

为优化开发体验，建议以下 VS Code 配置：

### 6.1 推荐扩展

在 `.vscode/extensions.json` 中添加：

```json
{
  "recommendations": [
    "dbaeumer.vscode-eslint",
    "esbenp.prettier-vscode",
    "stylelint.vscode-stylelint",
    "vue.volar",
    "ZixuanChen.vitest-explorer"
  ]
}
```

### 6.2 工作区设置

在 `.vscode/settings.json` 中配置：

```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.fixAll.stylelint": "explicit"
  },
  "typescript.tsdk": "node_modules/typescript/lib",
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue"
  ]
}
```

### 6.3 自定义任务

在 `.vscode/tasks.json` 中添加：

```json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "代码门禁: 检查暂存文件",
      "type": "shell",
      "command": "pnpm run precommit",
      "problemMatcher": ["$eslint-compact"]
    },
    {
      "label": "代码门禁: 检查当前变更",
      "type": "shell",
      "command": "pnpm code-guard branch",
      "problemMatcher": ["$eslint-compact", "$tsc"]
    }
  ]
}
```

## 7. CI/CD 集成

本地代码门禁与 CI/CD 集成，形成完整的质量保障体系：

1. **本地门禁**：开发阶段和提交前快速反馈
2. **CI 检查**：合并请求时全面验证
3. **CD 部署**：部署前最终确认

建议在 CI 流程中复用本地门禁中的检查命令，确保一致性。

## 8. 性能优化

为确保门禁系统不影响开发效率，建议：

1. **充分利用缓存**：
   - ESLint 缓存: `--cache` 选项
   - Turborepo 缓存: 已配置在 `turbo.json` 中
   - TypeScript 增量编译: `tsc --incremental`

2. **优化检查范围**：
   - Pre-commit: 仅检查暂存区变更
   - Pre-push: 仅检查自上次推送以来的变更
   - 利用 Turborepo 的 `--filter` 功能缩小检查范围

3. **并行执行任务**：
   - 利用 Turborepo 的任务并行特性
   - 对无依赖关系的检查采用并行执行

## 9. 总结与最佳实践

1. **分层设计**：
   - Pre-commit: 快速反馈，专注格式和简单规则
   - Pre-push: 全面检查，确保质量
   - CI/CD: 最终验证，确保稳定

2. **开发者体验**：
   - 提供清晰的错误信息
   - 支持自动修复
   - IDE 集成实现即时反馈

3. **持续改进**：
   - 定期评估门禁有效性
   - 根据团队反馈调整规则
   - 跟进工具链更新

按照本文档指导实施和优化代码门禁系统，可有效提升代码质量，减少线上问题，同时保持良好的开发体验。

## 10. 故障排除

在使用代码门禁系统时，可能会遇到一些常见问题。以下是解决方案：

### 10.1 TypeScript 类型检查失败

**问题：** 运行 `pnpm run tsc` 或推送代码时出现 TypeScript 错误。

**解决方案：**
1. 检查并修复实际的类型错误
2. 在紧急情况下，可以在 pre-push 钩子提示时选择"y"继续推送
3. 使用 `git push --no-verify` 跳过所有 Git 钩子检查（不推荐常规使用）

### 10.2 测试失败

**问题：** 单元测试失败导致无法推送。

**解决方案：**
1. 修复测试用例
2. 更新测试期望值以匹配新的实现
3. 紧急情况下，在 pre-push 钩子提示时选择"y"继续推送

### 10.3 lint-staged 运行缓慢

**问题：** pre-commit 钩子执行时间过长。

**解决方案：**
1. 优化 lint-staged 配置，仅对特定文件类型运行特定命令
2. 确保 ESLint 使用 `--cache` 选项
3. 考虑使用更细粒度的检查策略，将部分检查移至 pre-push 阶段

### 10.4 找不到 code-guard 命令

**问题：** 尝试运行 code-guard 命令时提示找不到。

**解决方案：**
1. code-guard 工具尚未实现，请参考计划安排
2. 使用现有的替代命令：`pnpm run lint`，`pnpm run tsc` 和 `pnpm run test:ci`

### 10.5 强制检查策略

**原则：** 本项目采用严格的代码质量把控策略，原则上不允许跳过任何检查。

**解决问题的正确方式：**
1. **类型错误**：修复所有类型错误，确保类型安全
2. **测试失败**：修复测试用例或更新测试预期结果
3. **Lint 错误**：遵循项目规范修复代码风格问题

**特殊情况：**
在极少数紧急情况下，如需临时绕过检查，请同时：
1. 获得团队负责人明确批准
2. 创建 Issue 记录需要修复的问题
3. 承诺在下一个迭代中修复问题
4. 使用 `git push --no-verify` 临时跳过检查（**不推荐**）

记住：今天跳过的检查可能会导致明天的生产问题。

### 10.6 检查结果误报或漏报

**问题：** 门禁系统没有捕获实际问题或报告了误报。

**解决方案：**
1. 调整 ESLint/Stylelint 规则配置
2. 更新 TypeScript tsconfig.json 配置
3. 提交 issue 或 PR 来改进门禁系统

如果遇到其他问题，请参考项目文档或向团队成员寻求帮助。
