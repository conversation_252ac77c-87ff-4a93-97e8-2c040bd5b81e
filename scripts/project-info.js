/* eslint-disable no-console */
/* eslint-disable no-unused-vars */
const fs = require('node:fs');
const path = require('node:path');
const { execSync } = require('node:child_process');

// 生成项目信息报告
function generateProjectInfo() {
    const packageJson = JSON.parse(fs.readFileSync('./package.json', 'utf8'));

    const info = {
        name: packageJson.name,
        version: packageJson.version,
        packages: [],
        lastCommit: null,
        contributors: null,
        dependencyCounts: {
            dependencies: Object.keys(packageJson.dependencies || {}).length,
            devDependencies: Object.keys(packageJson.devDependencies || {}).length,
        },
    };

    // 获取工作区包
    if (fs.existsSync('./pnpm-workspace.yaml')) {
        const packages = fs.readdirSync('./packages');
        const apps = fs.existsSync('./apps') ? fs.readdirSync('./apps') : [];

        info.packages = [...packages.map((pkg) => `packages/${pkg}`), ...apps.map((app) => `apps/${app}`)];
    }

    // 获取最后一次提交信息
    try {
        info.lastCommit = execSync('git log -1 --pretty=format:"%h - %an, %ar : %s"').toString().trim();
    } catch (error) {
        info.lastCommit = 'Git 信息未能获取';
    }

    // 获取贡献者信息
    try {
        info.contributors = execSync('git shortlog -sn | head -5').toString().trim();
    } catch (error) {
        info.contributors = 'Git 贡献者信息未能获取';
    }

    console.log('项目信息:');
    console.log(JSON.stringify(info, null, 2));
}

// 执行生成项目信息
generateProjectInfo();
