/* eslint-disable no-unused-vars */
/* eslint-disable max-depth */
/* eslint-disable no-console */
const fs = require('node:fs');
const path = require('node:path');

// 需要删除的文件夹和文件
const DEFAULT_DIRS_TO_DELETE = ['node_modules', '.turbo', 'dist', 'build', 'coverage', '.cache', '.pnpm-store'];

// 需要删除的文件类型
const DEFAULT_FILES_TO_DELETE = ['.eslintcache', '.DS_Store', '*.log', '.env.local', '*.tsbuildinfo'];

// 不删除的目录
const IGNORED_DIRS = ['.git', '.github', '.vscode'];

// 处理命令行参数
const args = process.argv.slice(2);
const dryRun = args.includes('--dry-run'); // 只显示将要删除的内容，不实际删除
const verbose = args.includes('--verbose'); // 显示详细日志

// 设置根目录路径 - 提升为全局变量
const rootDir = path.resolve('./'); // 获取绝对路径

/**
 * 递归删除目录下的指定文件夹和文件
 * @param {string} dir - 要处理的目录
 * @param {number} level - 递归层级，用于缩进日志
 */
function deleteFolders(dir, level = 0) {
    try {
        const files = fs.readdirSync(dir);

        // 检查当前目录是否包含需要删除的文件夹
        for (const file of files) {
            const fullPath = path.join(dir, file);

            try {
                const stat = fs.statSync(fullPath);

                // 如果是目录
                if (stat.isDirectory()) {
                    // 如果是需要忽略的目录，则跳过
                    if (IGNORED_DIRS.includes(file)) {
                        verbose && console.log(`${' '.repeat(level * 2)}跳过保护目录: ${fullPath}`);
                        continue;
                    }

                    // 如果是需要删除的目录，则删除
                    if (DEFAULT_DIRS_TO_DELETE.includes(file)) {
                        console.log(`${' '.repeat(level * 2)}删除目录: ${fullPath}`);
                        if (!dryRun) {
                            fs.rmSync(fullPath, { recursive: true, force: true });
                        }
                    } else {
                        // 否则递归处理子目录
                        deleteFolders(fullPath, level + 1);
                    }
                } else {
                    // 如果是文件，检查是否匹配需要删除的模式
                    const shouldDelete = DEFAULT_FILES_TO_DELETE.some((pattern) => {
                        if (pattern.includes('*')) {
                            const regex = new RegExp(`^${pattern.replace(/\*/g, '.*')}$`);
                            return regex.test(file);
                        }
                        return file === pattern;
                    });

                    if (shouldDelete) {
                        console.log(`${' '.repeat(level * 2)}删除文件: ${fullPath}`);
                        if (!dryRun) {
                            fs.unlinkSync(fullPath);
                        }
                    }
                }
            } catch (err) {
                console.error(`处理 ${fullPath} 时出错:`, err.message);
            }
        }

        // 在所有子文件都处理完后，如果目录变为空并且不是根目录，可以选择删除空目录
        if (dir !== rootDir && !dryRun) {
            try {
                const remainingFiles = fs.readdirSync(dir);
                if (remainingFiles.length === 0) {
                    verbose && console.log(`${' '.repeat(level * 2)}删除空目录: ${dir}`);
                    fs.rmdirSync(dir);
                }
            } catch (err) {
                console.error(`尝试删除空目录 ${dir} 时出错:`, err.message);
            }
        }
    } catch (err) {
        console.error(`无法读取目录 ${dir}:`, err.message);
    }
}

/**
 * 清理 npm/yarn/pnpm 缓存
 */
function cleanPackageManagerCache() {
    if (dryRun) {
        console.log('将执行: npm cache clean --force');
        console.log('将执行: yarn cache clean (如果安装了yarn)');
        console.log('将执行: pnpm store prune (如果安装了pnpm)');
        return;
    }

    try {
        console.log('清理 npm 缓存...');
        require('child_process').execSync('npm cache clean --force', { stdio: 'inherit' });
    } catch (err) {
        console.error('清理 npm 缓存失败:', err.message);
    }

    try {
        console.log('检查并清理 yarn 缓存...');
        require('child_process').execSync('yarn cache clean', { stdio: 'inherit' });
    } catch (err) {
        verbose && console.log('yarn 缓存清理失败 (可能未安装 yarn)');
    }

    try {
        console.log('检查并清理 pnpm 缓存...');
        require('child_process').execSync('pnpm store prune', { stdio: 'inherit' });
    } catch (err) {
        verbose && console.log('pnpm 缓存清理失败 (可能未安装 pnpm)');
    }
}

/**
 * 显示使用帮助
 */
function showHelp() {
    console.log(`
使用方法:
  node remove.js [选项]

选项:
  --help          显示帮助信息
  --dry-run       仅显示将要删除的内容，不实际删除
  --verbose       显示详细的操作日志
  --no-npm-cache  不清理 npm/yarn/pnpm 缓存
`);
}

// 主函数
function main() {
    if (args.includes('--help')) {
        showHelp();
        return;
    }

    console.log(`开始清理项目${dryRun ? ' (试运行模式)' : ''}...`);
    console.log(`根目录: ${rootDir}`);

    // 删除文件和目录
    deleteFolders(rootDir);

    // 清理包管理器缓存
    if (!args.includes('--no-npm-cache')) {
        cleanPackageManagerCache();
    }

    console.log('清理完成！');

    if (dryRun) {
        console.log('这是试运行模式，没有实际删除任何文件。要实际执行删除，请移除 --dry-run 参数。');
    } else {
        console.log('项目已彻底清理，所有缓存和构建产物已删除。');
    }
}

// 执行主函数
main();
