#!/usr/bin/env node
/* eslint-disable max-depth */
/* eslint-disable no-unused-vars */
/* eslint-disable no-console */
const fs = require('node:fs');
const path = require('node:path');
const { exec, execSync } = require('node:child_process');
const { promisify } = require('util');

// --- Configuration ---
const IGNORE_PATTERNS = [
    '**/node_modules/**',
    '**/dist/**',
    '**/build/**',
    '**/coverage/**',
    '**/.*/**', // Ignore hidden directories like .git, .husky, .turbo, .vscode etc.
];
const PNPM_VIEW_COMMAND = 'pnpm view --json'; // Use pnpm view
const NPM_VIEW_COMMAND = 'npm view --json'; // 备选：使用 npm view
// --- End Configuration ---

let semver;

try {
    semver = require('semver');
} catch (error) {
    console.error("Error: 'semver' package is required.");
    console.error('Please install it in your workspace root: pnpm add -D semver -w');
    process.exit(1);
}

let glob;
try {
    glob = require('glob').glob;
} catch (error) {
    console.error("Error: 'glob' package is required.");
    console.error('Please install it in your workspace root: pnpm add -D glob -w');
    process.exit(1);
}

const execPromise = promisify(exec);

// 寻找所有的 package.json 文件（简单版）
function findPackageJsonFiles(dir, results = []) {
    const files = fs.readdirSync(dir);

    for (const file of files) {
        const fullPath = path.join(dir, file);
        try {
            const stats = fs.statSync(fullPath);
            // 排除 node_modules 等目录
            if (stats.isDirectory() && !fullPath.includes('node_modules') && !file.startsWith('.')) {
                findPackageJsonFiles(fullPath, results);
            } else if (file === 'package.json') {
                results.push(fullPath);
            }
        } catch (err) {
            console.error(`读取 ${fullPath} 时出错:`, err.message);
        }
    }

    return results;
}

// 分析依赖版本一致性
async function analyzeDependencyConsistency() {
    console.log('\n--- 检查依赖版本一致性 ---');
    const packageJsonFiles = findPackageJsonFiles('./');
    const dependencyVersions = {};

    // 收集所有依赖的版本
    for (const file of packageJsonFiles) {
        const packageJson = JSON.parse(fs.readFileSync(file, 'utf8'));
        const pkgPath = path.relative('./', file);

        ['dependencies', 'devDependencies', 'peerDependencies', 'optionalDependencies'].forEach((depType) => {
            if (!packageJson[depType]) return;

            Object.entries(packageJson[depType]).forEach(([pkg, version]) => {
                if (!dependencyVersions[pkg]) {
                    dependencyVersions[pkg] = [];
                }

                if (!dependencyVersions[pkg].some((entry) => entry.version === version)) {
                    dependencyVersions[pkg].push({ version, locations: [pkgPath] });
                } else {
                    const entry = dependencyVersions[pkg].find((entry) => entry.version === version);
                    entry.locations.push(pkgPath);
                }
            });
        });
    }

    // 找出有多个版本的依赖
    const inconsistentDeps = {};
    for (const [pkg, versions] of Object.entries(dependencyVersions)) {
        if (versions.length > 1) {
            inconsistentDeps[pkg] = versions;
        }
    }

    if (Object.keys(inconsistentDeps).length > 0) {
        console.log('发现不一致的依赖版本:');
        console.log(JSON.stringify(inconsistentDeps, null, 2));
        return { hasInconsistencies: true, inconsistentDeps };
    } else {
        console.log('所有依赖版本一致 ✅');
        return { hasInconsistencies: false };
    }
}

/**
 * Fetches all available versions for a given package using package manager view commands.
 * @param {string} packageName - The name of the package.
 * @param {number} retries - Number of retries left (default: 3).
 * @param {boolean} useNpm - Whether to use npm instead of pnpm (for fallback).
 * @returns {Promise<string[]|null>} A promise that resolves with an array of versions, or null if an error occurs.
 */
async function getPackageVersions(packageName, retries = 3, useNpm = false) {
    try {
        // 选择使用 pnpm 还是 npm
        const command = `${useNpm ? NPM_VIEW_COMMAND : PNPM_VIEW_COMMAND} ${packageName} versions --no-color`;
        const { stdout } = await execPromise(command);
        // Trim whitespace which might interfere with JSON parsing
        const versions = JSON.parse(stdout.trim());
        // Ensure it's an array, package managers might return a single string if only one version exists
        return Array.isArray(versions) ? versions : versions ? [versions] : [];
    } catch (error) {
        // 如果使用 pnpm 失败，尝试使用 npm
        if (!useNpm) {
            console.warn(`  [WARN] Failed to fetch versions using pnpm for ${packageName}. Trying npm...`);
            return getPackageVersions(packageName, retries, true);
        }

        // 如果还有重试次数，并且错误可能是网络或临时性问题，则重试
        if (retries > 0 && (error.code === 'ETIMEDOUT' || error.code === 'ECONNRESET' || error.stderr?.includes('ERR_PNPM_NETWORK_ERROR') || error.stderr?.includes('ENOTFOUND'))) {
            console.warn(`  [WARN] Network error when fetching versions for ${packageName}. Retrying... (${retries} left)`);
            // 增加延迟，避免立即重试
            await new Promise((resolve) => setTimeout(resolve, 1000));
            return getPackageVersions(packageName, retries - 1, useNpm);
        }

        // 处理包不存在或无版本可用的情况
        const stderr = error.stderr?.toString() || '';
        if (stderr.includes('ERR_PNPM_NO_MATCHING_VERSION') || stderr.includes('404') || stderr.includes('E404')) {
            console.warn(`  [WARN] Package not found or no versions available for: ${packageName}`);
        } else {
            console.error(`  [ERROR] Failed to fetch versions for ${packageName}: ${stderr || error.message}`);
        }
        return null;
    }
}

/**
 * Finds the latest minor and major versions based on the current version.
 * @param {string} currentVersionStr - The current version string (e.g., '1.2.3').
 * @param {string[]} allVersions - An array of all available version strings.
 * @returns {{latestMinor: string|null, latestMajor: string|null}}
 */
function findLatestVersions(currentVersionStr, allVersions) {
    if (!allVersions || allVersions.length === 0 || !semver.valid(currentVersionStr)) {
        return { latestMinor: null, latestMajor: null };
    }

    const current = semver.parse(currentVersionStr);
    if (!current) {
        return { latestMinor: null, latestMajor: null };
    }

    const validVersions = allVersions
        .map((v) => semver.parse(v))
        .filter((v) => v !== null && v.prerelease.length === 0) // Filter out nulls and pre-releases
        .sort(semver.compare); // Sort ascending

    if (validVersions.length === 0) {
        return { latestMinor: null, latestMajor: null };
    }

    let latestMinor = current;
    // Find the highest version with the same major number
    for (let i = validVersions.length - 1; i >= 0; i--) {
        const v = validVersions[i];
        if (v.major === current.major) {
            if (semver.compare(v, latestMinor) > 0) {
                latestMinor = v;
            }
        }
        // Since sorted, break early if we go below current major
        if (v.major < current.major) break;
    }

    const latestMajor = validVersions[validVersions.length - 1]; // Highest valid version overall

    return {
        latestMinor: latestMinor?.version,
        latestMajor: latestMajor?.version,
    };
}

/**
 * Processes a single package.json file for updates.
 * @param {string} filePath - Absolute path to the package.json file.
 * @param {boolean} upgradeMajor - Whether to upgrade to the latest major version.
 * @param {Array<object>} majorUpgradesList - Array to store available major upgrades.
 */
async function processPackageJson(filePath, upgradeMajor, majorUpgradesList) {
    const relativePath = path.relative(process.cwd(), filePath);
    console.log(`\nProcessing ${relativePath}...`);
    try {
        const content = await fs.promises.readFile(filePath, 'utf-8');
        const packageJson = JSON.parse(content);
        let updated = false;
        const changes = []; // Track changes for summary

        // 处理 packageManager 字段
        if (packageJson.packageManager) {
            // packageManager 格式通常为 "pnpm@7.5.0"
            const pmMatch = packageJson.packageManager.match(/^([^@]+)@(.+)$/);
            if (pmMatch) {
                const [, pmName, pmVersion] = pmMatch;

                // 检查版本是否符合我们的规范（没有前缀）
                if (pmVersion.startsWith('^') || pmVersion.startsWith('~')) {
                    // 解析版本号，移除前缀
                    const parsedVersion = semver.parse(pmVersion.replace(/^[\^~]/, ''));
                    if (parsedVersion) {
                        const exactVersion = parsedVersion.version;
                        const newPackageManager = `${pmName}@${exactVersion}`;

                        if (newPackageManager !== packageJson.packageManager) {
                            packageJson.packageManager = newPackageManager;
                            changes.push(`  - packageManager: ${packageJson.packageManager} -> ${newPackageManager} (removing range prefix)`);
                            updated = true;
                        }
                    }
                }

                // 获取最新版本信息
                try {
                    const allVersions = await getPackageVersions(pmName);
                    if (allVersions && allVersions.length > 0) {
                        // 解析当前版本
                        const currentPmVersion = pmVersion.replace(/^[\^~]/, '');
                        const { latestMinor, latestMajor } = findLatestVersions(currentPmVersion, allVersions);

                        // 决定使用哪个版本进行更新
                        const targetVersion = upgradeMajor ? latestMajor : latestMinor;

                        if (targetVersion && semver.gt(targetVersion, currentPmVersion)) {
                            const newPackageManager = `${pmName}@${targetVersion}`;
                            packageJson.packageManager = newPackageManager;
                            changes.push(`  - packageManager: ${pmName}@${currentPmVersion} -> ${newPackageManager}`);
                            updated = true;
                        }
                    }
                } catch (error) {
                    console.warn(`  [WARN] Failed to check for updates to packageManager ${pmName}: ${error.message}`);
                }
            }
        }

        // 处理包的依赖项
        const depTypes = ['dependencies', 'devDependencies', 'peerDependencies', 'optionalDependencies'];

        for (const depType of depTypes) {
            if (!packageJson[depType] || typeof packageJson[depType] !== 'object') {
                continue; // Skip if depType doesn't exist or isn't an object
            }

            const dependencies = packageJson[depType];
            const packageNames = Object.keys(dependencies);

            if (packageNames.length === 0) continue;

            // Batch fetch versions for all dependencies in this section
            const versionFetchPromises = packageNames.map(async (packageName) => {
                const currentVersionRange = dependencies[packageName];
                // Skip non-standard ranges (like 'workspace:*', 'latest', git URLs etc.)
                // We only want to update specific semver ranges/versions
                if (!semver.validRange(currentVersionRange) && !semver.valid(currentVersionRange)) {
                    console.log(`  - Skipping ${packageName}: Non-semver range "${currentVersionRange}"`);
                    return { packageName, currentVersionRange, newVersionRange: currentVersionRange, majorUpgrade: null };
                }

                const currentVersion = semver.minVersion(currentVersionRange)?.version;
                if (!currentVersion) {
                    console.log(`  - Skipping ${packageName}: Could not parse version from range "${currentVersionRange}"`);
                    return { packageName, currentVersionRange, newVersionRange: currentVersionRange, majorUpgrade: null };
                }

                // 如果当前版本范围带有前缀（^ 或 ~），我们需要将其固定为具体版本
                const shouldPin = currentVersionRange.startsWith('^') || currentVersionRange.startsWith('~');
                if (shouldPin) {
                    // 记录这个改动，因为我们要移除版本范围前缀
                    changes.push(`  - ${depType}: ${packageName} ${currentVersionRange} -> ${currentVersion} (removing range prefix)`);
                    updated = true;
                }

                const allVersions = await getPackageVersions(packageName);
                if (!allVersions) {
                    // 对于 peerDependencies，使用 >= 加主要版本和次要版本
                    if (depType === 'peerDependencies' && currentVersion) {
                        const parsedVersion = semver.parse(currentVersion);
                        if (parsedVersion) {
                            const peerDependencyRange = `>=${parsedVersion.major}.${parsedVersion.minor}.0`;
                            console.log(`  - Skipping ${packageName}: Could not determine latest versions. Using peer dependency range ${peerDependencyRange}.`);
                            return { packageName, currentVersionRange, newVersionRange: peerDependencyRange, majorUpgrade: null };
                        }
                    }

                    const pinnedVersion = currentVersion ? currentVersion : currentVersionRange;
                    console.log(`  - Skipping ${packageName}: Failed to fetch versions. Pinning to ${pinnedVersion}.`);
                    return { packageName, currentVersionRange, newVersionRange: pinnedVersion, majorUpgrade: null };
                }

                const { latestMinor, latestMajor } = findLatestVersions(currentVersion, allVersions);

                if (!latestMinor || !latestMajor) {
                    // 如果计算最新版本失败，但我们之前解析了当前版本，则固定到该版本
                    // 对于 peerDependencies，使用 >= 加主要版本和次要版本
                    if (depType === 'peerDependencies' && currentVersion) {
                        const parsedVersion = semver.parse(currentVersion);
                        if (parsedVersion) {
                            const peerDependencyRange = `>=${parsedVersion.major}.${parsedVersion.minor}.0`;
                            console.log(`  - Skipping ${packageName}: Could not determine latest versions. Using peer dependency range ${peerDependencyRange}.`);
                            return { packageName, currentVersionRange, newVersionRange: peerDependencyRange, majorUpgrade: null };
                        }
                    }

                    const pinnedVersion = currentVersion ? currentVersion : currentVersionRange;
                    console.log(`  - Skipping ${packageName}: Could not determine latest versions. Pinning to ${pinnedVersion}.`);
                    return { packageName, currentVersionRange, newVersionRange: pinnedVersion, majorUpgrade: null };
                }

                let majorUpgrade = null;
                // Check if a major upgrade is available (and different from minor)
                if (semver.compare(latestMajor, latestMinor) > 0 && semver.compare(latestMajor, currentVersion) > 0) {
                    majorUpgrade = {
                        package: packageName,
                        from: currentVersion,
                        to: latestMajor,
                        file: relativePath,
                    };
                }

                const targetVersion = upgradeMajor ? latestMajor : latestMinor;

                // 对于 peerDependencies，使用 >= 加主要版本号和次要版本号
                if (depType === 'peerDependencies') {
                    const parsedVersion = semver.parse(targetVersion);
                    if (parsedVersion) {
                        const peerDependencyRange = `>=${parsedVersion.major}.${parsedVersion.minor}.0`;
                        // 只有当范围不同时才记录更改
                        if (peerDependencyRange !== currentVersionRange) {
                            changes.push(`  - ${depType}: ${packageName} ${currentVersionRange} -> ${peerDependencyRange} (peer dependency)`);
                            return { packageName, currentVersionRange, newVersionRange: peerDependencyRange, majorUpgrade };
                        } else {
                            return { packageName, currentVersionRange, newVersionRange: currentVersionRange, majorUpgrade };
                        }
                    }
                }

                // 对于其他依赖类型，直接使用目标版本，不加任何前缀
                const newVersionString = targetVersion;

                // 仅当新版本号与旧版本范围不同，且新版本确实更新时才记录更改
                if (newVersionString !== currentVersionRange && semver.compare(targetVersion, currentVersion) > 0) {
                    changes.push(`  - ${depType}: ${packageName} ${currentVersionRange} -> ${newVersionString}`);
                    return { packageName, currentVersionRange, newVersionRange: newVersionString, majorUpgrade };
                } else {
                    // 对于 peerDependencies，使用 >= 加主要版本
                    if (depType === 'peerDependencies') {
                        const parsedVersion = semver.parse(shouldPin ? currentVersion : targetVersion);
                        if (parsedVersion) {
                            const peerDependencyRange = `>=${parsedVersion.major}.${parsedVersion.minor}.0`;
                            if (peerDependencyRange !== currentVersionRange) {
                                changes.push(`  - ${depType}: ${packageName} ${currentVersionRange} -> ${peerDependencyRange} (peer dependency)`);
                                updated = true;
                            }
                            return {
                                packageName,
                                currentVersionRange,
                                newVersionRange: peerDependencyRange,
                                majorUpgrade,
                            };
                        }
                    }

                    // 其他依赖类型：即使不需要更新版本，也返回固定版本（无前缀）
                    return {
                        packageName,
                        currentVersionRange,
                        newVersionRange: shouldPin ? currentVersion : currentVersionRange,
                        majorUpgrade,
                    };
                }
            });

            const results = await Promise.all(versionFetchPromises);

            results.forEach((result) => {
                if (result) {
                    // Check if result exists (might be undefined if a promise failed unexpectedly, though handled)
                    dependencies[result.packageName] = result.newVersionRange;
                    if (result.newVersionRange !== result.currentVersionRange) {
                        updated = true;
                    }
                    if (result.majorUpgrade) {
                        majorUpgradesList.push(result.majorUpgrade);
                    }
                }
            });
        }

        if (updated) {
            // Try to detect original indentation, default to 2 spaces
            const match = content.match(/^(\s+)"/m);
            const indent = match ? match[1] : '  ';
            await fs.promises.writeFile(filePath, JSON.stringify(packageJson, null, indent) + '\n', 'utf-8');
            console.log(`  Updated ${relativePath}. Changes:`);
            changes.forEach((change) => console.log(change));
        } else {
            console.log(`  No updates applied to ${relativePath}.`);
        }
    } catch (error) {
        if (error instanceof SyntaxError) {
            console.error(`  [ERROR] Failed to parse JSON in ${relativePath}: ${error.message}`);
        } else {
            console.error(`  [ERROR] Failed to process ${relativePath}: ${error}`);
        }
    }
}

/**
 * 更新依赖版本
 */
async function updateDependencies() {
    console.log('\n--- 更新依赖版本 ---');
    const upgradeMajor = process.argv.includes('-a') || process.argv.includes('--all');
    console.log(`Mode: ${upgradeMajor ? 'Update to LATEST MAJOR' : 'Update to LATEST MINOR'}`);
    console.log(`Ignoring patterns: ${IGNORE_PATTERNS.join(', ')}`);

    const packageJsonFiles = await glob('**/package.json', {
        ignore: IGNORE_PATTERNS,
        cwd: process.cwd(),
        absolute: true,
        nodir: true, // Ensure only files are matched
    });

    console.log(`Found ${packageJsonFiles.length} package.json files.`);

    if (packageJsonFiles.length === 0) {
        console.log('No package.json files found matching the criteria.');
        return;
    }

    const majorUpgradesList = [];

    // Process files sequentially for clearer logs
    for (const filePath of packageJsonFiles) {
        await processPackageJson(filePath, upgradeMajor, majorUpgradesList);
    }

    // --- Summary ---
    console.log('\n--- 依赖更新总结 ---');

    // Consolidate major upgrades: show highest available 'to' version per package/file combo
    const uniqueMajorUpgrades = majorUpgradesList.reduce((acc, upgrade) => {
        const key = `${upgrade.package}@${upgrade.file}`;
        if (!acc[key] || semver.gt(upgrade.to, acc[key].to)) {
            acc[key] = upgrade;
        }
        return acc;
    }, {});

    const availableUpgrades = Object.values(uniqueMajorUpgrades).sort((a, b) => a.package.localeCompare(b.package));

    if (availableUpgrades.length > 0) {
        console.log('\n可用的主要版本升级 (运行时添加 -a 或 --all 参数以应用):');
        availableUpgrades.forEach((upgrade) => {
            console.log(`  - ${upgrade.package} (在 ${upgrade.file} 中): ${upgrade.from} -> ${upgrade.to}`);
        });
        if (!upgradeMajor) {
            console.log('\n提示: 运行此脚本时添加 -a (或 --all) 参数以应用这些主要版本升级。');
        } else {
            console.log('\n已根据 -a 参数更新主要版本。');
        }
    } else {
        console.log('\n未检测到可用的主要版本升级。');
    }
}

/**
 * 显示帮助信息
 */
function showHelp() {
    console.log(`
依赖检查和更新工具

用法:
  node check-deps.js [选项]

选项:
  -h, --help     显示帮助信息
  -c, --check    仅检查依赖版本一致性
  -u, --update   仅更新依赖版本
  -a, --all      更新时包含主要版本升级

如果不指定选项，默认会同时检查和更新依赖。
`);
}

/**
 * 主函数
 */
async function main() {
    console.time('脚本执行时间');

    // 解析命令行参数
    const args = process.argv.slice(2);
    const helpMode = args.includes('-h') || args.includes('--help');
    const checkOnly = args.includes('-c') || args.includes('--check');
    const updateOnly = args.includes('-u') || args.includes('--update');

    if (helpMode) {
        showHelp();
        return;
    }

    // 如果具体模式，则同时检查和更新
    const shouldCheck = checkOnly || (!checkOnly && !updateOnly);
    const shouldUpdate = updateOnly || (!checkOnly && !updateOnly);

    // 执行依赖一致性检查
    if (shouldCheck) {
        await analyzeDependencyConsistency();
    }

    // 执行依赖更新
    if (shouldUpdate) {
        await updateDependencies();
    }

    console.log('\n---------------------------------');
    console.timeEnd('脚本执行时间');
}

// 执行主函数
main().catch((error) => {
    console.error('\n脚本执行失败:', error);
    process.exit(1);
});
