{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["tsconfig.base.json", ".npmrc", "pnpm-workspace.yaml"], "globalEnv": ["NODE_ENV", "DEPLOY_ENV"], "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["src/**", "public/**", "package.json", "vite.config.ts", "tsconfig.json", ".env*"], "outputs": ["dist/**"], "cache": true}, "build:rd": {"dependsOn": ["^build"], "inputs": ["src/**", "public/**", "package.json", "vite.config.ts", "tsconfig.json", ".env*"], "outputs": ["dist/**"], "env": ["DEPLOY_ENV=rd"], "cache": true}, "build:qa": {"dependsOn": ["^build"], "inputs": ["src/**", "public/**", "package.json", "vite.config.ts", "tsconfig.json", ".env*"], "outputs": ["dist/**"], "env": ["DEPLOY_ENV=qa"], "cache": true}, "build:prod": {"dependsOn": ["^build"], "inputs": ["src/**", "public/**", "package.json", "vite.config.ts", "tsconfig.json", ".env*"], "outputs": ["dist/**"], "env": ["DEPLOY_ENV=prod"], "cache": true}, "build:pre": {"dependsOn": ["^build"], "inputs": ["src/**", "public/**", "package.json", "vite.config.ts", "tsconfig.json", ".env*"], "outputs": ["dist/**"], "env": ["DEPLOY_ENV=pre"], "cache": true}, "test": {"dependsOn": ["^build"], "inputs": ["src/**", "test/**", "package.json", "vitest.config.ts", "tsconfig.json"], "outputs": ["coverage/**"], "cache": true}, "lint": {"inputs": ["src/**", "package.json", ".eslintrc*"], "outputs": [], "cache": true}, "typecheck": {"inputs": ["src/**", "package.json", "tsconfig.json"], "outputs": [], "cache": true}, "optimize": {"dependsOn": ["build"], "cache": true}, "dev": {"cache": false, "persistent": true}, "dev:rd": {"cache": false, "persistent": true, "env": ["DEPLOY_ENV=rd"]}, "dev:qa": {"cache": false, "persistent": true, "env": ["DEPLOY_ENV=qa"]}}, "remoteCache": {"signature": true}}