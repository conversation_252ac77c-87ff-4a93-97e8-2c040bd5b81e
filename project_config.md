# Project Configuration (LTM)

*This file contains the stable, long-term context for the project.*
*It should be updated infrequently, primarily when core goals, tech, or patterns change.*

---

## Core Goal

创建一个在线考试及测评系统前端应用，为用户提供考试/测评体验，包括身份验证、设备检测、答题、提交等功能。该项目采用 Monorepo 结构，便于团队协作和代码共享。

---

## Tech Stack

*   **Frontend:** Vue 3, TypeScript, Vite, Pinia
*   **UI Framework:** Boss Design
*   **Monorepo:** pnpm workspace, Turborepo
*   **HTTP Client:** Axios
*   **WebSocket:** MQTT
*   **Testing:** Vitest
*   **Linting/Formatting:** ESLint, Prettier, Stylelint

---

## Critical Patterns & Conventions

*   **项目结构:** Monorepo 架构，分为 apps 和 packages 两大部分
*   **应用划分:** main（主应用）和 h5（移动端）
*   **状态管理:** 使用 Pinia 进行状态管理
*   **API 设计:** 通过 services 包中的模块化接口调用
*   **组件复用:** 通过 packages/components 实现跨应用组件共享
*   **工具函数:** 通过 packages/utils 实现通用工具函数
*   **环境配置:** 支持多环境部署（rd、qa、pre、prod）
*   **构建优化:** 使用 Turborepo 优化构建流程和缓存策略
*   **Commit 规范:** 遵循 Conventional Commits 格式

---

## Key Constraints

*   **Node 版本:** 需要 Node.js >= 22.13.0
*   **包管理工具:** 必须使用 pnpm >= 10.0.0
*   **构建环境:** 支持多环境构建和部署（研发、测试、预发、生产）
*   **性能要求:** 考试系统对实时性和稳定性有较高要求
*   **兼容性:** 需支持主流浏览器和移动设备

---

## Tokenization Settings

*   **Estimation Method:** Character-based
*   **Characters Per Token (Estimate):** 4
