# fe-kaoshi-web 代码库导览

## 本地代码门禁实施状态

本项目已部分实施了现代前端 Monorepo 本地代码门禁系统，具体实施状态如下：

### 已完成的部分

1. **Git Hooks 配置**
   - 使用 husky 管理 Git Hooks
   - 已配置 pre-commit、commit-msg 和 pre-push 钩子

2. **代码格式和规范检查**
   - 使用 lint-staged 在 pre-commit 阶段检查暂存区文件
   - 使用 commitlint 在 commit-msg 阶段检查提交信息格式

3. **基础类型检查和测试**
   - 在 pre-push 阶段运行 TypeScript 类型检查
   - 在 pre-push 阶段运行单元测试

4. **IDE 集成**
   - 已配置 VS Code 推荐扩展
   - 已配置 VS Code 工作区设置，支持自动格式化和错误修复
   - 已配置 VS Code 任务，支持手动运行检查

### 待完成的部分

1. **code-guard 工具实现**
   - 实现代码门禁命令行工具，支持更细粒度的检查和更友好的反馈
   - 目标路径：`packages/code-guard/`

2. **增量检查优化**
   - 优化 pre-push 钩子，仅检查变更的包
   - 扩展 turbo.json 配置，支持更多的检查任务

3. **敏感信息检测**
   - 添加敏感信息扫描工具集成

## 相关文件

- [README-CODE-GUARD.md](./README-CODE-GUARD.md) - 本地代码门禁实施文档
- [.husky/pre-commit](./.husky/pre-commit) - pre-commit 钩子脚本
- [.husky/pre-push](./.husky/pre-push) - pre-push 钩子脚本
- [.husky/commit-msg](./.husky/commit-msg) - commit-msg 钩子脚本
- [.vscode/extensions.json](./.vscode/extensions.json) - VS Code 推荐扩展
- [.vscode/settings.json](./.vscode/settings.json) - VS Code 工作区设置
- [.vscode/tasks.json](./.vscode/tasks.json) - VS Code 自定义任务

## 下一步工作

1. 实现 code-guard 工具，参考 README-CODE-GUARD.md 中的设计
2. 细化 lint-staged 配置，针对不同文件类型使用不同工具
3. 添加 commitizen 支持，提升提交信息编写体验
4. 完善与 Turborepo 的集成，实现更智能的增量检查
