# FE-考试系统前端

## 项目介绍

本项目是基于 Vue 3 + TypeScript + Vite 构建的瞰荐职测C端项目（考试+ 测评），采用 Monorepo 结构进行开发管理。项目使用 pnpm 作为包管理工具，Turborepo 进行构建优化，整体架构清晰，便于团队协作和功能扩展。

## 环境要求

- Node.js >= 22.13.0
- pnpm >= 10.0.0
- Git >= 2.30.0

## 项目结构

```
├── apps                    # 应用目录
│   ├── main                # 主应用
│   │   ├── src             # 源代码目录
│   │   │   ├── assets      # 静态资源文件
│   │   │   ├── router      # 路由配置
│   │   │   ├── styles      # 全局样式文件
│   │   │   ├── views       # 页面视图组件
│   │   │   │   ├── error   # 错误页面
│   │   │   │   ├── layout  # 布局组件
│   │   │   │   ├── login   # 登录相关页面
│   │   │   │   ├── monitor # 监控页面
│   │   │   │   ├── mpa     # 多页应用相关页面
│   │   │   │   └── pub     # 公共页面
│   │   │   ├── App.vue     # 根组件
│   │   │   └── main.ts     # 应用入口文件
│   │   ├── public          # 公共静态资源
│   │   ├── index.html      # HTML模板
│   │   ├── vite.config.ts  # Vite配置
│   │   └── package.json    # 应用依赖配置
│   │
│   └── login               # 登录应用
│       ├── src             # 源代码目录
│       │   ├── assets      # 静态资源文件
│       │   ├── styles      # 样式文件
│       │   ├── views       # 页面视图组件
│       │   ├── App.vue     # 根组件
│       │   ├── main.ts     # 应用入口文件
│       │   └── router.ts   # 路由配置
│       ├── public          # 公共静态资源
│       ├── index.html      # HTML模板
│       ├── vite.config.ts  # Vite配置
│       └── package.json    # 应用依赖配置
│
├── packages                # 共享包目录
│   ├── components          # 可复用UI组件
│   │   ├── src             # 源代码目录
│   │   │   ├── business    # 业务相关组件
│   │   │   ├── feature     # 功能性组件
│   │   │   └── index.ts    # 组件导出文件
│   │   └── package.json    # 组件包配置
│   │
│   ├── constants           # 常量定义
│   │   ├── src             # 源代码目录
│   │   │   └── index.ts    # 常量导出文件
│   │   └── package.json    # 常量包配置
│   │
│   ├── directives          # Vue指令
│   │   ├── src             # 源代码目录
│   │   │   └── index.ts    # 指令导出文件
│   │   └── package.json    # 指令包配置
│   │
│   ├── hooks               # 自定义Hooks
│   │   ├── src             # 源代码目录
│   │   │   └── index.ts    # Hooks导出文件
│   │   └── package.json    # Hooks包配置
│   │
│   ├── services            # API服务
│   │   ├── src             # 源代码目录
│   │   │   ├── apis        # API接口定义
│   │   │   ├── socket      # WebSocket相关
│   │   │   ├── http.ts     # HTTP请求封装
│   │   │   ├── http-interceptors.ts # 请求拦截器
│   │   │   └── index.ts    # 服务导出文件
│   │   └── package.json    # 服务包配置
│   │
│   ├── types               # 类型定义
│   │   ├── src             # 源代码目录
│   │   │   └── index.ts    # 类型导出文件
│   │   └── package.json    # 类型包配置
│   │
│   └── utils               # 通用工具函数
│       ├── src             # 源代码目录
│       │   ├── libs        # 第三方库封装
│       │   ├── tools       # 工具函数
│       │   ├── index.ts    # 工具导出文件
│       │   ├── magpie.ts   # 埋点相关
│       │   ├── mitt.ts     # 事件总线
│       │   ├── send-error.ts # 错误上报
│       │   └── track-event.ts # 事件追踪
│       └── package.json    # 工具包配置
│
├── scripts                 # 项目脚本
│   ├── remove.js           # 清理脚本
│   └── setup-remote-cache.js # 远程缓存设置脚本
│
├── .changeset             # Changeset配置（版本管理）
├── .husky                 # Git Hooks配置
├── .vscode                # VSCode配置
├── package.json           # 项目依赖配置
├── pnpm-workspace.yaml    # 工作区配置
└── turbo.json             # Turborepo配置
```

### 主要目录说明

#### 1. apps 目录

包含项目的各个应用入口，每个应用都是一个独立的 Vue 项目：

- **main**: 主应用，包含系统的主要功能和路由管理

    - `views`: 页面组件，按功能模块划分
    - `router`: 路由配置，管理不同页面间的导航
    - `styles`: 全局样式定义
    - `assets`: 静态资源如图片、字体等

- **login**: 登录应用，处理用户认证相关流程
    - 包含独立的路由和视图组件

#### 2. packages 目录

包含可在不同应用间共享的通用代码库：

- **components**: 共享UI组件

    - `business`: 业务相关组件，如表单、表格等
    - `feature`: 功能性组件，如弹窗、提示等

- **constants**: 常量定义

    - 包含系统中使用的常量值、枚举值等

- **directives**: Vue自定义指令

    - 提供可复用的DOM操作封装

- **hooks**: Vue组合式API Hooks

    - 提供可复用的逻辑功能

- **services**: API服务层

    - `apis`: 按模块划分的API接口定义
    - `socket`: WebSocket通信相关
    - `http.ts`: Axios请求封装
    - `http-interceptors.ts`: HTTP请求/响应拦截器

- **types**: TypeScript类型定义

    - 包含项目中使用的接口、类型等定义

- **utils**: 工具函数
    - `libs`: 第三方库的包装和适配
    - `tools`: 通用工具函数集合
    - 各种特定功能的工具模块（事件总线、错误处理等）

#### 3. 配置文件

- **pnpm-workspace.yaml**: 定义monorepo工作区结构
- **turbo.json**: Turborepo构建配置，包含各种构建任务和缓存策略
- **tsconfig.base.json**: 基础TypeScript配置
- **.eslintrc**: ESLint代码规范配置
- **.husky**: Git Hooks配置，用于代码提交检查
- **.changeset**: 版本发布管理配置

## 技术栈

- **核心框架**：Vue 3.5.x
- **状态管理**：Pinia 2.0.x
- **路由管理**：Vue Router 4.1.x
- **UI组件库**：Boss Design 1.7.x
- **构建工具**：Vite 6.2.x
- **工作区管理**：pnpm workspace + Turborepo
- **代码规范**：ESLint + Stylelint + TypeScript
- **测试框架**：Vitest
- **版本控制**：Changeset
- **其他工具**：
    - axios（HTTP请求）
    - micro-app（微前端）
    - woodpecker-monitor（监控）
    - mqtt（消息推送）

## 开发指南

### 项目初始化

首次克隆项目后，执行以下命令初始化项目：

```bash
# 安装依赖并构建工作区
pnpm init
```

或者手动执行以下步骤：

```bash
# 安装依赖
pnpm install

# 构建工作区
pnpm build:workspace
```

### 本地开发

```bash
# 启动所有应用的开发服务
pnpm dev

# 启动特定应用开发服务（以main为例）
pnpm --filter @apps/main dev

# 在指定环境下启动开发服务
pnpm dev:rd  # 研发环境
pnpm dev:qa  # 测试环境
```

### 构建项目

```bash
# 构建所有应用
pnpm build

# 构建特定应用
pnpm --filter @apps/main build

# 构建特定环境
pnpm build:rd   # 研发环境构建
pnpm build:qa   # 测试环境构建
pnpm build:pre  # 预发环境构建
pnpm build:prod # 生产环境构建
```

### 代码规范检查

```bash
# 运行代码检查（自动修复）
pnpm lint

# 运行TypeScript类型检查
pnpm tsc
```

### 测试

```bash
# 运行测试（带监视模式）
pnpm test

# 运行CI测试
pnpm test:ci
```

### 依赖管理

```bash
# 检查依赖升级
pnpm upgrade

# 安装新依赖（以main应用为例）
pnpm --filter @apps/main add <package-name>

# 安装共享包（在apps中使用packages里的包）
pnpm --filter @apps/main add @crm/exam-utils@workspace
```

### 版本发布

```bash
# 准备发布
pnpm publish:prepare

# 创建变更集
pnpm changeset

# 执行发布
pnpm publish
```

## 注意事项

1. 请确保使用 pnpm 作为包管理工具，项目已配置只允许使用 pnpm
2. 遵循项目的代码规范和 Git 提交规范
3. 开发前请先拉取最新代码并安装依赖
4. 提交代码前请确保通过代码检查和单元测试

## 多入口架构

- **主应用(main)**：负责整体框架、路由控制、公共功能
- **登录应用(login)**：处理用户认证相关功能

## 环境配置

项目支持多环境配置：

- **rd**: 研发环境
- **qa**: 测试环境
- **pre**: 预发环境
- **prod**: 生产环境

每个应用的环境变量配置在各自目录下的`.env.*`文件中。

## 调试与优化

- 使用 Vue Devtools 进行 Vue 组件调试
- 使用 Vite 自带的 HMR 热更新提高开发效率
- 使用 Turbo 提供的缓存机制加速构建过程

## 常见问题

### 依赖安装失败

检查 Node.js 和 pnpm 版本是否符合要求，尝试清除缓存后重新安装：

```bash
pnpm store prune
pnpm install
```

### 构建缓存问题

清除 Turborepo 缓存：

```bash
npx turbo clean
```

### 类型错误

运行类型检查查找具体错误：

```bash
pnpm tsc
```
