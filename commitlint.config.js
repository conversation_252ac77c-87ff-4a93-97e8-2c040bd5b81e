module.exports = {
    extends: ['@commitlint/config-conventional'],
    rules: {
        'type-enum': [
            // type 类型 限制 用于定义提交类型的枚举
            2,
            'always',
            [
                'feat', // 新功能（feature）
                'fix', // 修补bug
                'docs', // 文档（documentation）
                'style', // 格式（不影响代码运行的变动）
                'refactor', // 重构（即不是新增功能，也不是修改bug的代码变动）
                'perf', // 性能优化
                'test', // 增加测试
                'build', // 打包
                'ci', // 持续集成
                'chore', // 构建过程或辅助工具的变动
                'revert', // 回滚
                'wip', // 开发中
                'workflow', // 工作流改进
                'types', // 类型定义文件更改
                'release', // 发布
                'deps', // 依赖更新
                'config', // 配置更新
                'merge', // 合并分支
                'security', // 安全更新
                'breaking', // 重大变更
                'upgrade', // 升级
            ],
        ],
        'subject-full-stop': [0, 'never'], // subject 结尾是否需要句号， 0 表示关闭
        'subject-case': [0, 'never'], // subject大小写限制【上面的即subject】， 0 表示关闭
    },
};
