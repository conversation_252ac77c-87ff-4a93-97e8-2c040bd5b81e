import bossConfig from '@boss/eslint-config';
import pluginJs from '@eslint/js';
import prettierRecommended from 'eslint-plugin-prettier/recommended';
import pluginVue from 'eslint-plugin-vue';
import globals from 'globals';
import tseslint from 'typescript-eslint';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import { createRequire } from 'node:module';

// 获取当前文件目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 使用 createRequire 加载 JSON
const require = createRequire(import.meta.url);
const autoImportConfigMain = require('./apps/main/.eslintrc-auto-import.json');
const autoImportConfigH5 = require('./apps/h5/.eslintrc-auto-import.json');

// 定义需要类型检查的子项目及其 tsconfig 路径
const typeScriptProjects = [
    { files: ['apps/h5/**/*.{ts,tsx,vue}'], tsconfig: './apps/h5/tsconfig.json', env: globals.browser },
    { files: ['apps/main/**/*.{ts,tsx,vue}'], tsconfig: './apps/main/tsconfig.json', env: globals.browser },
    { files: ['packages/utils/**/*.{ts,tsx}'], tsconfig: './packages/utils/tsconfig.json' },
    { files: ['packages/components/**/*.{ts,tsx,vue}'], tsconfig: './packages/components/tsconfig.json', env: globals.browser },
    { files: ['packages/directives/**/*.{ts,tsx}'], tsconfig: './packages/directives/tsconfig.json' },
    { files: ['packages/hooks/**/*.{ts,tsx}'], tsconfig: './packages/hooks/tsconfig.json' },
    { files: ['packages/types/**/*.{ts,tsx}'], tsconfig: './packages/types/tsconfig.json' },
    { files: ['packages/constants/**/*.{ts,tsx}'], tsconfig: './packages/constants/tsconfig.json' },
    { files: ['packages/exam-question/**/*.{ts,tsx,vue}'], tsconfig: './packages/exam-question/tsconfig.json', env: globals.browser },
    // 如果 scripts 需要类型检查，也添加到这里
    // { files: ['scripts/**/*.ts'], tsconfig: './scripts/tsconfig.json', env: globals.node },
];

export default tseslint.config(
    // 全局忽略

    // 基础 JS 配置
    pluginJs.configs.recommended,

    // --- Vue 配置 Start ---
    // 包含 vue-eslint-parser 和基础 Vue 规则
    ...pluginVue.configs['flat/recommended'],
    // --- Vue 配置 End ---

    // --- TypeScript 项目特定配置 Start ---
    // 为每个需要类型感知的项目生成配置块
    ...typeScriptProjects.flatMap((project) => [
        // 先应用 tseslint.configs.recommended，并限制 files
        ...tseslint.configs.recommended.map((config) => ({
            ...config, // 继承推荐配置的内容
            files: project.files, // 确保它只应用于当前项目的文件
            // 注意：这里可能会引入全局的 parserOptions，如果 tseslint.configs.recommended 包含它的话
            // 下面的特定配置需要能覆盖它
        })),
        // 再应用项目特定的配置，覆盖继承来的配置
        {
            files: project.files, // 再次指定 files 可能冗余，但确保优先级
            languageOptions: {
                globals: { ...(project.env || {}), ...globals.es2021 }, // 添加环境全局变量和 ES 版本
                parserOptions: {
                    projectService: true, // 使用 project service 处理 project references
                    tsconfigRootDir: __dirname, // tsconfig 相对于项目根目录
                    extraFileExtensions: project.files.some((f) => f.includes('.vue')) ? ['.vue'] : [], // 如果包含 vue 文件则添加
                },
            },
            // 项目特定规则
            rules: {
                // 示例：如果 components 包需要不同的规则
                ...(project.tsconfig.includes('components')
                    ? {
                          /* 'some-rule': 'off' */
                      }
                    : {}),
            },
        },
    ]),
    // --- TypeScript 项目特定配置 End ---

    // 共享的 @boss/eslint-config
    ...bossConfig.eslint.config,

    // --- Prettier 配置 Start ---
    prettierRecommended,
    // 覆盖 prettier/prettier 规则以使用自定义配置
    {
        rules: {
            'prettier/prettier': ['error', { ...bossConfig.prettier.config }],
        },
    },
    // --- Prettier 配置 End ---

    // 全局规则覆盖 (用户添加)
    {
        rules: {
            'no-console': 'error', // 禁止 console
        },
    },
    // Scripts 配置 (Node.js 环境 - 无类型检查)
    {
        files: ['**/*.{js,mjs,cjs}'], // 仅 JS
        languageOptions: {
            globals: { ...globals.node }, // Node 环境
            parser: undefined, // 显式指定不使用 TS parser
            parserOptions: {
                // 明确禁用类型检查相关选项
                project: null,
                projectService: null,
            },
        },
    },
    {
        ignores: [
            '**/dist/**',
            '**/node_modules/**',
            '**/*.min.js',
            'build/**',
            '**/*.md',
            '**/auto-imports.d.ts', // 忽略自动导入的类型声明
            '**/components.d.ts', // 忽略自动生成的组件类型声明
            'pnpm-lock.yaml', // 忽略锁文件
            '**/.turbo/**', // 忽略 turbo 缓存
            '**/.eslintcache/**', // 忽略 eslint 缓存
            '**/.vite/**', // 忽略 vite 缓存
            '**/public/**', // 忽略 public 目录
        ],
    },
    {
        languageOptions: {
            globals: {
                ...autoImportConfigMain.globals,
                ...autoImportConfigH5.globals,
            },
        },
        // 确保这个配置只应用到需要它的文件，比如 main 应用
        files: ['apps/main/**/*.{ts,tsx,vue,js,jsx}', 'apps/h5/**/*.{ts,tsx,vue,js,jsx}'],
    },
);
