{
    "extends": "@boss/tsconfig/tsconfig.base.json",
    "compilerOptions": {
        "target": "ESNext",
        "module": "ESNext",
        "moduleResolution": "bundler",

        // 严格模式配置
        "strict": true,
        "noImplicitAny": true,
        "strictNullChecks": true,
        "strictFunctionTypes": true,
        "strictBindCallApply": true,
        "strictPropertyInitialization": true,
        "noImplicitThis": true,
        "noImplicitReturns": true,
        "noFallthroughCasesInSwitch": true,
        "noUncheckedIndexedAccess": true,
        "exactOptionalPropertyTypes": true,

        // 代码质量检查
        "noUnusedLocals": true,
        "noUnusedParameters": true,
        "noImplicitOverride": true,
        "noPropertyAccessFromIndexSignature": false,

        // 模块和导入配置
        "skipLibCheck": true,
        "forceConsistentCasingInFileNames": true,
        "resolveJsonModule": true,
        "isolatedModules": true,
        "esModuleInterop": true,
        "allowSyntheticDefaultImports": true,

        // 性能和缓存配置
        "incremental": true,

        "lib": ["ESNext", "DOM", "DOM.Iterable"],
        "baseUrl": ".",
        "paths": {
            "@vue/shared": [
                "./node_modules/@vue/shared"
            ]
        },
        "jsxImportSource": "vue"
    },
    "include": [
        "env.d.ts",
        "shims.d.ts"
    ],
    "exclude": [
        "node_modules",
        "**/dist/**",
        "**/*.tsbuildinfo"
    ]
}
