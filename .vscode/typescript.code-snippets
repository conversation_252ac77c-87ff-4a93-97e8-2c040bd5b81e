{"TypeScript 类": {"prefix": "tsclass", "body": ["export class $1 {", "  constructor(private $2: $3) {}", "", "  public $4($5: $6): $7 {", "    $8", "  }", "", "  private $9($10: $11): $12 {", "    $13", "  }", "}"], "description": "TypeScript 类定义"}, "TypeScript 枚举": {"prefix": "tsenum", "body": ["export enum $1 {", "  $2 = '$3',", "  $4 = '$5',", "}"], "description": "TypeScript 枚举定义"}, "TypeScript 泛型接口": {"prefix": "tsgeneric", "body": ["export interface $1<T> {", "  data: T;", "  $2: $3;", "}"], "description": "TypeScript 泛型接口"}, "TypeScript 类型守卫": {"prefix": "tsguard", "body": ["function is$1(value: any): value is $2 {", "  return $3;", "}"], "description": "TypeScript 类型守卫"}, "TypeScript 联合类型": {"prefix": "tsun<PERSON>", "body": ["type $1 = $2 | $3;"], "description": "TypeScript 联合类型"}, "TypeScript 交叉类型": {"prefix": "tsintersection", "body": ["type $1 = $2 & $3;"], "description": "TypeScript 交叉类型"}, "TypeScript 模块声明": {"prefix": "tsdeclare", "body": ["declare module '$1' {", "  export interface $2 {", "    $3: $4;", "  }", "", "  export function $5($6: $7): $8;", "}"], "description": "TypeScript 模块声明"}, "TypeScript 异步函数": {"prefix": "tsasync", "body": ["async function $1($2: $3): Promise<$4> {", "  try {", "    const result = await $5;", "    return result;", "  } catch (error) {", "    console.error(error);", "    throw error;", "  }", "}"], "description": "TypeScript 异步函数"}, "TypeScript 工具类型": {"prefix": "<PERSON><PERSON><PERSON>", "body": ["type $1<T> = {", "  [K in keyof T]: $2;", "};"], "description": "TypeScript 工具类型"}, "TypeScript 条件类型": {"prefix": "tsconditional", "body": ["type $1<T> = T extends $2 ? $3 : $4;"], "description": "TypeScript 条件类型"}, "TypeScript 命名空间": {"prefix": "tsnamespace", "body": ["namespace $1 {", "  export interface $2 {", "    $3: $4;", "  }", "", "  export function $5($6: $7): $8 {", "    $9", "  }", "}"], "description": "TypeScript 命名空间"}, "Vite 环境变量声明": {"prefix": "viteenv", "body": ["/// <reference types=\"vite/client\" />", "", "interface ImportMetaEnv {", "  readonly VITE_APP_TITLE: string", "  readonly VITE_API_BASE_URL: string", "  readonly VITE_DEPLOY_VERSION: string", "  readonly VITE_ENV: 'dev' | 'qa' | 'rd' | 'pre' | 'prod'", "  // 更多环境变量...", "}", "", "interface ImportMeta {", "  readonly env: ImportMetaEnv", "}", "", "declare const DEPLOY_VERSION: string"], "description": "Vite 环境变量类型声明"}}