{"Vue 3 组件": {"prefix": "vue3", "body": ["<script setup lang=\"ts\">", "import { ref } from 'vue'", "", "$1", "</script>", "", "<template>", "  <div>", "    $2", "  </div>", "</template>", "", "<style scoped>", "$3", "</style>"], "description": "Vue 3 组件模板"}, "Vue 3 Props": {"prefix": "vprops", "body": ["const props = defineProps<{", "  $1: $2", "}>()"], "description": "Vue 3 Props 定义"}, "Vue 3 Emits": {"prefix": "vemits", "body": ["const emit = defineEmits<{", "  (e: '$1', $2): void", "}>()"], "description": "Vue 3 Emits 定义"}, "Vue 3 Ref": {"prefix": "vref", "body": ["const $1 = ref<$2>($3)"], "description": "Vue 3 Ref 定义"}, "Vue 3 Computed": {"prefix": "vcomputed", "body": ["const $1 = computed(() => {", "  return $2", "})"], "description": "Vue 3 Computed 定义"}, "Vue 3 Watch": {"prefix": "vwatch", "body": ["watch($1, (newValue, oldValue) => {", "  $2", "})"], "description": "Vue 3 Watch 定义"}, "Vue 3 onMounted": {"prefix": "vonmounted", "body": ["onMounted(() => {", "  $1", "})"], "description": "Vue 3 onMounted 生命周期"}, "TypeScript 接口": {"prefix": "tsinterface", "body": ["interface $1 {", "  $2: $3", "}"], "description": "TypeScript 接口定义"}, "TypeScript 类型": {"prefix": "tstype", "body": ["type $1 = {", "  $2: $3", "}"], "description": "TypeScript 类型定义"}, "异步函数": {"prefix": "afn", "body": ["const $1 = async ($2) => {", "  try {", "    $3", "  } catch (error) {", "    console.error(error)", "  }", "}"], "description": "异步函数定义"}, "导入 Vue 组合式 API": {"prefix": "vimport", "body": ["import { $1 } from 'vue'"], "description": "导入 Vue 组合式 API"}, "导入 Vue Router": {"prefix": "vrouter", "body": ["import { useRouter, useRoute } from 'vue-router'"], "description": "导入 Vue Router"}, "Console Log": {"prefix": "clog", "body": ["console.log('$1:', $1)"], "description": "带标签的 Console Log"}}