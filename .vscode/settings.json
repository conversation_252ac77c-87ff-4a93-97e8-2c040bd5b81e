{"eslint.workingDirectories": [{"mode": "auto"}], "eslint.useFlatConfig": true, "files.autoSave": "off", "editor.tabSize": 4, "editor.insertSpaces": true, "editor.codeActions.triggerOnFocusChange": true, "editor.bracketPairColorization.independentColorPoolPerBracketType": true, "editor.formatOnPaste": true, "editor.formatOnSaveMode": "file", "editor.formatOnType": true, "files.autoSaveWorkspaceFilesOnly": true, "cSpell.enabled": true, "prettier.requireConfig": true, "prettier.tabWidth": 4, "prettier.trailingComma": "all", "prettier.vueIndentScriptAndStyle": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.defaultFoldingRangeProvider": "Vue.volar", "eslint.format.enable": true, "prettier.enable": false, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "explicit"}, "eslint.rules.customizations": [{"rule": "style/*", "severity": "off", "fixable": true}, {"rule": "format/*", "severity": "off", "fixable": true}, {"rule": "*-indent", "severity": "off", "fixable": true}, {"rule": "*-spacing", "severity": "off", "fixable": true}, {"rule": "*-spaces", "severity": "off", "fixable": true}, {"rule": "*-order", "severity": "off", "fixable": true}, {"rule": "*-dangle", "severity": "off", "fixable": true}, {"rule": "*-newline", "severity": "off", "fixable": true}, {"rule": "*quotes", "severity": "off", "fixable": true}, {"rule": "*semi", "severity": "off", "fixable": true}], "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue"], "files.insertFinalNewline": true, "files.trimTrailingWhitespace": true, "files.exclude": {"**/.git": true, "**/.DS_Store": true, "**/node_modules": true, "**/.turbo": true, "**/dist": false, "**/.eslintcache": true}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/.turbo": true, "**/coverage": true}, "git.autofetch": true, "git.enableSmartCommit": true, "typescript.updateImportsOnFileMove.enabled": "always", "javascript.updateImportsOnFileMove.enabled": "always", "cSpell.words": ["<PERSON>answer", "ant<PERSON>", "changeset", "commitlint", "drap", "ant<PERSON>", "changeset", "commitlint", "KANJIAN", "monorepo", "NEBULARTC", "Needlogin", "pagetip", "pinia", "pnpm", "turbo", "unocss", "unplugin", "vant", "vite", "vitest", "vueuse"], "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "files.eol": "\n", "typescript.tsdk": "node_modules/typescript/lib"}