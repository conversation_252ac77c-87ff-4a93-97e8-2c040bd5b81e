{"version": "2.0.0", "tasks": [{"label": "启动 Admin 开发环境", "type": "shell", "command": "cd ${workspaceFolder}/apps/admin && pnpm run dev", "isBackground": true, "problemMatcher": {"owner": "vite", "pattern": {"regexp": "."}, "background": {"activeOnStart": true, "beginsPattern": ".", "endsPattern": "VITE v\\d+\\.\\d+\\.\\d+.*ready in \\d+ms"}}, "presentation": {"reveal": "always", "panel": "new", "focus": false}, "group": {"kind": "build", "isDefault": true}, "options": {"env": {"FORCE_COLOR": "1"}}}, {"label": "启动 Admin QA 环境", "type": "shell", "command": "cd ${workspaceFolder}/apps/admin && pnpm run dev:qa", "isBackground": true, "problemMatcher": {"owner": "vite", "pattern": {"regexp": "."}, "background": {"activeOnStart": true, "beginsPattern": ".", "endsPattern": "VITE v\\d+\\.\\d+\\.\\d+.*ready in \\d+ms"}}, "presentation": {"reveal": "always", "panel": "new", "focus": false}, "options": {"env": {"FORCE_COLOR": "1"}}}, {"label": "启动 Admin RD 环境", "type": "shell", "command": "cd ${workspaceFolder}/apps/admin && pnpm run dev:rd", "isBackground": true, "problemMatcher": {"owner": "vite", "pattern": {"regexp": "."}, "background": {"activeOnStart": true, "beginsPattern": ".", "endsPattern": "VITE v\\d+\\.\\d+\\.\\d+.*ready in \\d+ms"}}, "presentation": {"reveal": "always", "panel": "new", "focus": false}, "options": {"env": {"FORCE_COLOR": "1"}}}, {"label": "启动 User 开发环境", "type": "shell", "command": "cd ${workspaceFolder}/apps/user && pnpm run dev", "isBackground": true, "problemMatcher": {"owner": "vite", "pattern": {"regexp": "."}, "background": {"activeOnStart": true, "beginsPattern": ".", "endsPattern": "VITE v\\d+\\.\\d+\\.\\d+.*ready in \\d+ms"}}, "presentation": {"reveal": "always", "panel": "new", "focus": false}, "options": {"env": {"FORCE_COLOR": "1"}}}, {"label": "启动 User QA 环境", "type": "shell", "command": "cd ${workspaceFolder}/apps/user && pnpm run dev:qa", "isBackground": true, "problemMatcher": {"owner": "vite", "pattern": {"regexp": "."}, "background": {"activeOnStart": true, "beginsPattern": ".", "endsPattern": "VITE v\\d+\\.\\d+\\.\\d+.*ready in \\d+ms"}}, "presentation": {"reveal": "always", "panel": "new", "focus": false}, "options": {"env": {"FORCE_COLOR": "1"}}}, {"label": "启动 User RD 环境", "type": "shell", "command": "cd ${workspaceFolder}/apps/user && pnpm run dev:rd", "isBackground": true, "problemMatcher": {"owner": "vite", "pattern": {"regexp": "."}, "background": {"activeOnStart": true, "beginsPattern": ".", "endsPattern": "VITE v\\d+\\.\\d+\\.\\d+.*ready in \\d+ms"}}, "presentation": {"reveal": "always", "panel": "new", "focus": false}, "options": {"env": {"FORCE_COLOR": "1"}}}, {"label": "运行所有测试", "type": "shell", "command": "pnpm run test", "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}, "group": {"kind": "test", "isDefault": true}}, {"label": "构建 Admin 预发环境", "type": "shell", "command": "cd ${workspaceFolder}/apps/admin && pnpm run build:pre", "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "构建 Admin 生产环境", "type": "shell", "command": "cd ${workspaceFolder}/apps/admin && pnpm run build:prod", "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "构建 Admin QA 环境", "type": "shell", "command": "cd ${workspaceFolder}/apps/admin && pnpm run build:qa", "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "构建 Admin RD 环境", "type": "shell", "command": "cd ${workspaceFolder}/apps/admin && pnpm run build:rd", "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "构建 User 预发环境", "type": "shell", "command": "cd ${workspaceFolder}/apps/user && pnpm run build:pre", "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "构建 User 生产环境", "type": "shell", "command": "cd ${workspaceFolder}/apps/user && pnpm run build:prod", "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "构建 User QA 环境", "type": "shell", "command": "cd ${workspaceFolder}/apps/user && pnpm run build:qa", "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "构建 User RD 环境", "type": "shell", "command": "cd ${workspaceFolder}/apps/user && pnpm run build:rd", "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "构建工作区", "type": "shell", "command": "pnpm run build:workspace", "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "Lint 修复", "type": "shell", "command": "pnpm run lint", "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "依赖升级检查", "type": "shell", "command": "pnpm run upgrade", "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "清理项目", "type": "shell", "command": "pnpm run clear", "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "初始化项目", "type": "shell", "command": "pnpm run init", "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "预览 Admin 构建", "type": "shell", "command": "cd ${workspaceFolder}/apps/admin && pnpm run preview", "isBackground": true, "problemMatcher": {"owner": "vite", "pattern": {"regexp": "."}, "background": {"activeOnStart": true, "beginsPattern": ".", "endsPattern": "Local:.+:\\d+"}}, "presentation": {"reveal": "always", "panel": "new", "focus": false}, "options": {"env": {"FORCE_COLOR": "1"}}}, {"label": "预览 User 构建", "type": "shell", "command": "cd ${workspaceFolder}/apps/user && pnpm run preview", "isBackground": true, "problemMatcher": {"owner": "vite", "pattern": {"regexp": "."}, "background": {"activeOnStart": true, "beginsPattern": ".", "endsPattern": "Local:.+:\\d+"}}, "presentation": {"reveal": "always", "panel": "new", "focus": false}, "options": {"env": {"FORCE_COLOR": "1"}}}, {"label": "代码门禁: 检查暂存文件", "type": "shell", "command": "pnpm run precommit", "problemMatcher": ["$eslint-compact"]}, {"label": "代码门禁: 检查当前变更", "type": "shell", "command": "pnpm turbo run typecheck test --filter=[HEAD^1]", "problemMatcher": ["$eslint-compact", "$tsc"]}, {"label": "代码门禁: 完整检查", "type": "shell", "command": "pnpm turbo run lint typecheck test", "problemMatcher": ["$eslint-compact", "$tsc"]}, {"label": "代码门禁: 修复格式问题", "type": "shell", "command": "pnpm run lint", "problemMatcher": ["$eslint-compact"]}]}