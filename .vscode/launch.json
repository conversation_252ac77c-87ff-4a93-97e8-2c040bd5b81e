{"version": "0.2.0", "configurations": [{"type": "chrome", "request": "launch", "name": "启动 Chrome 调试 Admin", "url": "http://localhost:5173", "webRoot": "${workspaceFolder}/apps/admin", "sourceMaps": true, "sourceMapPathOverrides": {"/./*": "${webRoot}/*", "/src/*": "${webRoot}/src/*", "webpack:///src/*": "${webRoot}/src/*", "/@fs/*": "${webRoot}/*"}, "preLaunchTask": "启动 Admin 开发环境", "serverReadyAction": {"pattern": "VITE v\\d+\\.\\d+\\.\\d+.*ready in \\d+ms", "uriFormat": "http://localhost:5173", "action": "debugWithChrome"}}, {"type": "chrome", "request": "launch", "name": "启动 Chrome 调试 User", "url": "http://localhost:6688", "webRoot": "${workspaceFolder}/apps/user", "sourceMaps": true, "sourceMapPathOverrides": {"/./*": "${webRoot}/*", "/src/*": "${webRoot}/src/*", "webpack:///src/*": "${webRoot}/src/*", "/@fs/*": "${webRoot}/*"}, "preLaunchTask": "启动 User 开发环境", "serverReadyAction": {"pattern": "VITE v\\d+\\.\\d+\\.\\d+.*ready in \\d+ms", "uriFormat": "http://localhost:6688", "action": "debugWithChrome"}}, {"type": "chrome", "request": "launch", "name": "启动 Chrome 调试 Admin (QA)", "url": "http://localhost:5173", "webRoot": "${workspaceFolder}/apps/admin", "sourceMaps": true, "sourceMapPathOverrides": {"/./*": "${webRoot}/*", "/src/*": "${webRoot}/src/*", "webpack:///src/*": "${webRoot}/src/*", "/@fs/*": "${webRoot}/*"}, "preLaunchTask": "启动 Admin QA 环境", "serverReadyAction": {"pattern": "VITE v\\d+\\.\\d+\\.\\d+.*ready in \\d+ms", "uriFormat": "http://localhost:5173", "action": "debugWithChrome"}}, {"type": "chrome", "request": "launch", "name": "启动 Chrome 调试 Admin (RD)", "url": "http://localhost:5173", "webRoot": "${workspaceFolder}/apps/admin", "sourceMaps": true, "sourceMapPathOverrides": {"/./*": "${webRoot}/*", "/src/*": "${webRoot}/src/*", "webpack:///src/*": "${webRoot}/src/*", "/@fs/*": "${webRoot}/*"}, "preLaunchTask": "启动 Admin RD 环境", "serverReadyAction": {"pattern": "VITE v\\d+\\.\\d+\\.\\d+.*ready in \\d+ms", "uriFormat": "http://localhost:5173", "action": "debugWithChrome"}}, {"type": "chrome", "request": "launch", "name": "启动 Chrome 调试 User (QA)", "url": "http://localhost:6688", "webRoot": "${workspaceFolder}/apps/user", "sourceMaps": true, "sourceMapPathOverrides": {"/./*": "${webRoot}/*", "/src/*": "${webRoot}/src/*", "webpack:///src/*": "${webRoot}/src/*", "/@fs/*": "${webRoot}/*"}, "preLaunchTask": "启动 User QA 环境", "serverReadyAction": {"pattern": "VITE v\\d+\\.\\d+\\.\\d+.*ready in \\d+ms", "uriFormat": "http://localhost:6688", "action": "debugWithChrome"}}, {"type": "chrome", "request": "launch", "name": "启动 Chrome 调试 User (RD)", "url": "http://localhost:6688", "webRoot": "${workspaceFolder}/apps/user", "sourceMaps": true, "sourceMapPathOverrides": {"/./*": "${webRoot}/*", "/src/*": "${webRoot}/src/*", "webpack:///src/*": "${webRoot}/src/*", "/@fs/*": "${webRoot}/*"}, "preLaunchTask": "启动 User RD 环境", "serverReadyAction": {"pattern": "VITE v\\d+\\.\\d+\\.\\d+.*ready in \\d+ms", "uriFormat": "http://localhost:6688", "action": "debugWithChrome"}}, {"type": "node", "request": "launch", "name": "运行当前文件", "program": "${file}", "skipFiles": ["<node_internals>/**"]}, {"type": "node", "request": "launch", "name": "运行 Vitest 测试", "autoAttachChildProcesses": true, "skipFiles": ["<node_internals>/**", "**/node_modules/**"], "program": "${workspaceRoot}/node_modules/vitest/vitest.mjs", "args": ["run", "${relativeFile}"], "smartStep": true, "console": "integratedTerminal"}, {"type": "node", "request": "launch", "name": "调试 Admin Vite 构建", "autoAttachChildProcesses": true, "skipFiles": ["<node_internals>/**", "**/node_modules/**"], "program": "${workspaceRoot}/node_modules/vite/bin/vite.js", "args": ["build"], "cwd": "${workspaceFolder}/apps/admin", "console": "integratedTerminal"}, {"type": "node", "request": "launch", "name": "调试 User Vite 构建", "autoAttachChildProcesses": true, "skipFiles": ["<node_internals>/**", "**/node_modules/**"], "program": "${workspaceRoot}/node_modules/vite/bin/vite.js", "args": ["build"], "cwd": "${workspaceFolder}/apps/user", "console": "integratedTerminal"}]}