{"Vue 3 Store": {"prefix": "vstore", "body": ["import { defineStore } from 'pinia'", "", "export const use$1Store = defineStore('$1', () => {", "  // 状态", "  const $2 = ref<$3>($4)", "", "  // 计算属性", "  const $5 = computed(() => {", "    return $6", "  })", "", "  // 动作", "  async function $7($8) {", "    try {", "      $9", "    } catch (error) {", "      console.error(error)", "    }", "  }", "", "  return {", "    $2,", "    $5,", "    $7", "  }", "})"], "description": "Vue 3 Pinia Store 模板"}, "Vue 3 Router": {"prefix": "vroute", "body": ["import { RouteRecordRaw } from 'vue-router'", "", "const routes: RouteRecordRaw[] = [", "  {", "    path: '/$1',", "    name: '$2',", "    component: () => import('@/views/$3.vue'),", "    meta: {", "      title: '$4',", "      requiresAuth: $5", "    }", "  }", "]", "", "export default routes"], "description": "Vue Router 路由配置"}, "Vue 3 API 请求": {"prefix": "vapi", "body": ["import { useRequest } from '@/hooks/useRequest'", "", "export interface $1Params {", "  $2: $3", "}", "", "export interface $1Response {", "  $4: $5", "}", "", "export function $6($7: $1Params) {", "  return useRequest<$1Response>({", "    url: '$8',", "    method: '$9',", "    $10: $7", "  })", "}"], "description": "Vue API 请求函数"}, "Vue 3 组件": {"prefix": "vcomponent", "body": ["<script setup lang=\"ts\">", "import { ref, computed, watch, onMounted } from 'vue'", "", "// Props", "const props = defineProps<{", "  $1: $2", "}>()", "", "// Emits", "const emit = defineEmits<{", "  (e: 'update:$3', value: $4): void", "  (e: '$5', $6): void", "}>()", "", "// 状态", "const $7 = ref<$8>($9)", "", "// 计算属性", "const $10 = computed(() => {", "  return $11", "})", "", "// 监听", "watch(() => props.$1, (newValue) => {", "  $12", "})", "", "// 方法", "function $13($14) {", "  $15", "}", "", "// 生命周期", "onMounted(() => {", "  $16", "})", "</script>", "", "<template>", "  <div>", "    $17", "  </div>", "</template>", "", "<style scoped>", "$18", "</style>"], "description": "完整的 Vue 3 组件模板"}, "Vue 3 Composition Function": {"prefix": "vcomposition", "body": ["import { ref, computed, watch } from 'vue'", "", "export function use$1($2) {", "  // 状态", "  const $3 = ref<$4>($5)", "", "  // 计算属性", "  const $6 = computed(() => {", "    return $7", "  })", "", "  // 方法", "  function $8($9) {", "    $10", "  }", "", "  return {", "    $3,", "    $6,", "    $8", "  }", "}"], "description": "Vue 3 组合式函数"}, "Vite 配置": {"prefix": "viteconfig", "body": ["import type { ConfigEnv } from 'vite'", "import path from 'node:path'", "import process from 'node:process'", "import { BossDesignResolver } from '@boss/design-resolver'", "import inject from '@rollup/plugin-inject'", "import vue from '@vitejs/plugin-vue'", "import vueJsx from '@vitejs/plugin-vue-jsx'", "import autoImport from 'unplugin-auto-import/vite'", "import components from 'unplugin-vue-components/vite'", "import { defineConfig } from 'vite'", "import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'", "import { aliasConfig } from '../../vite.config'", "", "// https://vitejs.dev/config/", "export default defineConfig(({ command, mode }: ConfigEnv) => {", "    console.log('DEPLOY_ENV:', mode)", "", "    return {", "        plugins: [", "            vue(),", "            inject({", "                Invoke: path.resolve(process.cwd(), 'src/services/invoke'),", "            }),", "            vueJsx(),", "            createSvgIconsPlugin({", "                iconDirs: [path.resolve(process.cwd(), 'src/assets/svg')],", "                symbolId: 'icon-[dir]-[name]',", "            }),", "            components({", "                resolvers: [BossDesignResolver()],", "            }),", "            autoImport({", "                resolvers: [BossDesignResolver({ autoImport: true })],", "            }),", "        ],", "        base: '/',", "        css: {", "            preprocessorOptions: {", "                less: {", "                    math: 'always',", "                    additionalData: `@import \"@/styles/base.less\";`,", "                },", "            },", "        },", "        resolve: {", "            alias: [", "                ...<PERSON><PERSON><PERSON><PERSON><PERSON>,", "                { find: '@', replacement: path.resolve(__dirname, 'src') },", "            ],", "        },", "        esbuild: {", "            drop: mode === 'prod' ? (['console', 'debugger'] as ('console' | 'debugger')[]) : undefined,", "        },", "        build: {", "            sourcemap: true,", "            assetsDir: 'static',", "            assetsInlineLimit: 1024,", "        },", "        server: {", "            host: true,", "            cors: true,", "            proxy: {", "                '/wapi': {", "                    target: mode === 'rd' ? 'https://zhice-rd.weizhipin.com' : 'https://zhice-qa.weizhipin.com',", "                    change<PERSON><PERSON>in: true,", "                    secure: false,", "                },", "            },", "        },", "    }", "})"], "description": "Vite 配置模板"}}